"use server";

import { encryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { v4 } from "uuid";

export const getUserSettings = async (userId: string) => {
  return await prisma.userSettings.findFirst({
    where: { userId: userId }
  });
};

export const createUserSettings = async (userId: string) => {
  const user = await getUserById(userId);
  if (!user) {
    return;
  }

  const userSecret = encryptData(v4());
  if (!userSecret) {
    return;
  }

  await prisma.userSettings.create({
    data: {
      userId: userId,
      twoFactorEnabled: false,
      secretToken: userSecret.encryptedData,
      secretTokenIv: userSecret.iv
    }
  });
}

export const updateUserSettings = async (userId: string, data: any) => {
  const userSettings = await getUserSettings(userId);
  if (!userSettings) {
    // return await prisma.userSettings.create({
    //   data: {
    //     userId: userId,
    //     ...data
    //   }
    // });
    return;
  }

  return await prisma.userSettings.update({
    where: { id: userSettings.id },
    data: data
  });
}
