"use client";

import { cn } from "@/lib/utils";
import {
  UsersIcon,
  VideoIcon,
  EyeIcon,
  CheckCircleIcon,
  FlagIcon,
  AlertCircleIcon,
  ClockIcon,
  CheckIcon,
  ImageIcon,
  HardDriveIcon,
  TrendingUp,
  ArrowUpRight,
} from "lucide-react";
import Link from "next/link";

interface AdminStatsProps {
  stats: {
    totalUsers: number;
    totalVideos: number;
    totalViews: number;
    recentUsers: number;
    recentVideos: number;
    totalImages: number;
    recentImages: number;
    packageStats: Record<string, number>;
    verifiedUsers: number;
    reports?: {
      total: number;
      open: number;
      pending: number;
      closed: number;
    };
    tickets?: {
      total: number;
      open: number;
      inProgress: number;
      closed: number;
      resolved: number;
    };
    storageUsage: {
      total: string;
      used: string;
      available: string;
    } | null;
  };
}

const CompactCard = ({
  title,
  value,
  change,
  trend,
  Icon,
  link,
  size = "default",
}: {
  title: string;
  value: string | number;
  change?: string;
  trend?: "up" | "down" | "neutral";
  Icon: any;
  link?: string;
  size?: "default" | "large";
}) => {
  const CardContent = () => (
    <div
      className={cn(
        "group relative bg-gray-900/30 hover:bg-gray-900/50 border border-gray-800/60 hover:border-gray-700/80 rounded-xl transition-all duration-200",
        "p-5",
      )}
    >
      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] via-transparent to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

      <div className="relative flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-3">
            <div className="flex items-center justify-center rounded-lg bg-gray-800/60 h-8 w-8">
              <Icon className="text-gray-300 h-4 w-4" />
            </div>
            <span className="text-sm text-gray-400 font-medium">{title}</span>
          </div>

          <div
            className={cn(
              "font-bold text-white mb-2",
              size === "large" ? "text-3xl" : "text-2xl",
            )}
          >
            {value}
          </div>

          {change && (
            <div
              className={cn(
                "flex items-center gap-1 text-sm",
                trend === "up" && "text-emerald-400",
                trend === "down" && "text-red-400",
                trend === "neutral" && "text-gray-400",
              )}
            >
              {trend === "up" && <TrendingUp className="h-3.5 w-3.5" />}
              <span>{change}</span>
            </div>
          )}
        </div>

        {link && (
          <ArrowUpRight className="h-4 w-4 text-gray-600 group-hover:text-gray-400 transition-colors" />
        )}
      </div>
    </div>
  );

  return link ? (
    <Link href={link} className="block h-full">
      <CardContent />
    </Link>
  ) : (
    <CardContent />
  );
};

const ProgressCard = ({
  title,
  value,
  percentage,
  Icon,
}: {
  title: string;
  value: string;
  percentage: number;
  Icon: any;
}) => (
  <div className="bg-gray-900/30 border border-gray-800/60 rounded-xl p-5">
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center rounded-lg bg-gray-800/60 h-8 w-8">
          <Icon className="h-4 w-4 text-gray-300" />
        </div>
        <span className="text-sm text-gray-400 font-medium">{title}</span>
      </div>
      <span className="text-sm text-gray-400 font-medium">{percentage}%</span>
    </div>

    <div className="text-2xl font-bold text-white mb-4">{value}</div>

    <div className="w-full h-2 bg-gray-800/60 rounded-full overflow-hidden">
      <div
        className={cn(
          "h-full rounded-full transition-all duration-700",
          percentage < 60 && "bg-gradient-to-r from-emerald-500 to-emerald-400",
          percentage >= 60 &&
            percentage < 85 &&
            "bg-gradient-to-r from-yellow-500 to-orange-400",
          percentage >= 85 && "bg-gradient-to-r from-red-500 to-red-400",
        )}
        style={{ width: `${percentage}%` }}
      />
    </div>
  </div>
);

export function AdminStats({ stats }: AdminStatsProps) {
  const userGrowth = stats.recentUsers > 0 ? `+${stats.recentUsers}` : "0";
  const contentGrowth = stats.recentVideos + stats.recentImages;
  const verificationRate =
    stats.totalUsers > 0
      ? Math.round((stats.verifiedUsers / stats.totalUsers) * 100)
      : 0;
  const storageData = stats.storageUsage || {
    total: "0 GB",
    used: "0 GB",
    available: "0 GB",
  };
  const storageUseRate = storageData.used
    ? Math.round(
        (parseFloat(storageData.used) / parseFloat(storageData.total)) * 100,
      )
    : 0;

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <CompactCard
          title="Total Users"
          value={stats.totalUsers.toLocaleString()}
          change={`${userGrowth} today`}
          trend={stats.recentUsers > 0 ? "up" : "neutral"}
          Icon={UsersIcon}
          link="/admin/users"
          size="large"
        />

        <CompactCard
          title="Content Library"
          value={(stats.totalVideos + stats.totalImages).toLocaleString()}
          change={
            contentGrowth > 0 ? `+${contentGrowth} today` : "No new content"
          }
          trend={contentGrowth > 0 ? "up" : "neutral"}
          Icon={VideoIcon}
          size="large"
        />

        <CompactCard
          title="Total Views"
          value={stats.totalViews.toLocaleString()}
          change={
            stats.totalVideos > 0
              ? `${Math.round(stats.totalViews / stats.totalVideos)} avg/video`
              : "No views"
          }
          trend="neutral"
          Icon={EyeIcon}
          size="large"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        <CompactCard
          title="Videos"
          value={stats.totalVideos.toLocaleString()}
          change={stats.recentVideos > 0 ? `+${stats.recentVideos}` : "0"}
          trend={stats.recentVideos > 0 ? "up" : "neutral"}
          Icon={VideoIcon}
        />

        <CompactCard
          title="Images"
          value={stats.totalImages.toLocaleString()}
          change={stats.recentImages > 0 ? `+${stats.recentImages}` : "0"}
          trend={stats.recentImages > 0 ? "up" : "neutral"}
          Icon={ImageIcon}
        />

        <CompactCard
          title="Verified"
          value={stats.verifiedUsers.toLocaleString()}
          change={`${verificationRate}% rate`}
          trend="neutral"
          Icon={CheckCircleIcon}
        />

        {stats.reports && (
          <CompactCard
            title="Open Reports"
            value={stats.reports.open.toString()}
            change={stats.reports.open > 0 ? "Needs attention" : "All clear"}
            trend={stats.reports.open > 0 ? "down" : "up"}
            Icon={AlertCircleIcon}
            link="/admin/reports"
          />
        )}
      </div>

      {/* Support & Tickets Section */}
      {stats.tickets && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          <CompactCard
            title="Open Tickets"
            value={stats.tickets.open.toString()}
            change={stats.tickets.open > 0 ? "Needs response" : "All clear"}
            trend={stats.tickets.open > 0 ? "down" : "up"}
            Icon={ClockIcon}
            link="/admin/tickets"
          />

          <CompactCard
            title="In Progress"
            value={stats.tickets.inProgress.toString()}
            change="Being handled"
            trend="neutral"
            Icon={AlertCircleIcon}
            link="/admin/tickets"
          />

          <CompactCard
            title="Resolved"
            value={stats.tickets.resolved.toString()}
            change={`${stats.tickets.total > 0 ? Math.round((stats.tickets.resolved / stats.tickets.total) * 100) : 100}% rate`}
            trend="up"
            Icon={CheckIcon}
            link="/admin/tickets"
          />

          <CompactCard
            title="Total Tickets"
            value={stats.tickets.total.toString()}
            change="All time"
            trend="neutral"
            Icon={FlagIcon}
            link="/admin/tickets"
          />
        </div>
      )}

      {/* System Status - 2 Column Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProgressCard
          title="Storage Usage"
          value={storageData.used}
          percentage={storageUseRate}
          Icon={HardDriveIcon}
        />

        <ProgressCard
          title="User Verification"
          value={`${stats.verifiedUsers} verified`}
          percentage={verificationRate}
          Icon={CheckCircleIcon}
        />
      </div>
    </div>
  );
}