import {getEmailSession, getUserSession} from '@/server/session';
import {redirect} from 'next/navigation';
import {NextResponse} from "next/server";

export async function POST(request: Request): Promise<Response> {
    const {email, password} = await request.json();
    if (!email || !password) {
        return new Response('Invalid email or password', {status: 401});
    }

    const loginRequest = await fetch(process.env.VIDEO_API_URL + '/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
            password,
        }),
    });

    const data = await loginRequest.json();
    if (!data) {
        return NextResponse.json({status: 500, message: 'Error while logging in'})
    }

    if (data && data.statusCode === 401) {
        return NextResponse.json({status: 401, message: data.message});
    }

    if (data && data.needVerification && data.email) {
        const session = await getEmailSession();
        session.email = data.email;
        await session.save();

        const sendEmailRequest = await fetch(process.env.VIDEO_API_URL + '/email-verification/send-verification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': process.env.API_SERVER_KEY!,
            },
            body: JSON.stringify({
                email,
            }),
        });

        const _data = await sendEmailRequest.json();
        if (_data && _data.accepted) {
            if (_data.accepted.includes(email)) {
                return NextResponse.json({status: 201, redirect: '/verify/mail'});
            }
        }

        return NextResponse.json({status: 401, message: "Error while sending verification email"});
    }

    if (data && data.twoFactorRequired) {
        if (data.tempToken) {
            const session = await getUserSession();
            session.userId = data.userId;
            session.accessToken = data.tempToken;
            await session.save();
        }
        return NextResponse.json({status: 201, redirect: '/verify/twofa'});
    }

    const session = await getUserSession();
    session.userId = data.user.id;
    session.accessToken = data.accessToken;
    await session.save();

    return NextResponse.json({status: 200, redirect: '/dashboard'});
}