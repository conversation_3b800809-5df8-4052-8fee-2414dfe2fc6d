"use client";

import React, { useMemo } from "react";
import { motion } from "framer-motion";
import { QuickOverview } from "./quick-overview";
import { OverviewChart } from "./overview-chart";
import { UpgradePrompt } from "./upgrade-prompt";
import { AdvancedInsights } from "./advanced-insights";
import { ProAnalytics } from "./pro-analytics";

interface VideoAnalyticsProps {
  video: {
    id: string;
    title: string;
    views: number;
    createdAt: string | Date;
  };
  userId: string;
  userSubscription: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const LoadingSkeleton = () => (
  <div className="w-full animate-pulse">
    <div className="bg-[#110018] rounded-2xl border border-white/12 p-6 mb-6">
      <div className="h-8 bg-white/10 rounded w-48 mb-6"></div>
      <div className="flex gap-4 overflow-x-auto pb-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="flex-shrink-0 w-[280px] md:w-[393px] h-[120px] bg-white/5 rounded-lg border border-white/8"
          ></div>
        ))}
      </div>
    </div>
    <div className="flex flex-col lg:flex-row gap-6">
      <div className="flex-1 h-[300px] md:h-[468px] bg-white/5 rounded-xl border border-white/8"></div>
      <div className="flex-1 h-[300px] md:h-[468px] bg-white/5 rounded-xl border border-white/8"></div>
    </div>
  </div>
);

export function VideoAnalytics({
  video,
  userId,
  userSubscription,
}: VideoAnalyticsProps) {
  const isProOrCreator = useMemo(
    () => ["PRO", "CREATOR"].includes((userSubscription || "").toUpperCase()),
    [userSubscription],
  );

    const calculatedMetrics = useMemo(() => {
    const estimatedLikes = Math.floor(video.views * 0.08);
    const estimatedDislikes = Math.floor(video.views * 0.02);
    const totalWatchTime = video.views * 45; // Average 45 seconds
    const avgWatchTime =
      video.views > 0 ? Math.floor(totalWatchTime / video.views) : 0;
    const totalWatchTimeMinutes = Math.floor(totalWatchTime / 60);
    const totalWatchTimeSeconds = totalWatchTime % 60;
    const avgWatchTimeMinutes = Math.floor(avgWatchTime / 60);
    const avgWatchTimeSecondsOnly = avgWatchTime % 60;

    return {
      estimatedLikes,
      estimatedDislikes,
      totalWatchTime,
      avgWatchTime,
      totalWatchTimeMinutes,
      totalWatchTimeSeconds,
      avgWatchTimeMinutes,
      avgWatchTimeSecondsOnly,
    };
  }, [video.views]);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="w-full"
    >
      {/* Quick Overview Section */}
      <QuickOverview
        video={video}
        estimatedLikes={calculatedMetrics.estimatedLikes}
        estimatedDislikes={calculatedMetrics.estimatedDislikes}
      />

      {/* Main Content Area */}
      <div className="flex flex-col lg:flex-row gap-6 mb-6">
        {/* Overview Chart Section */}
        <OverviewChart video={video} />

        {/* Upgrade Section for Free Users */}
        {!isProOrCreator && <UpgradePrompt type="main" />}
      </div>

      {/* Advanced Insights Section - Pro/Creator Only */}
      {isProOrCreator && (
        <AdvancedInsights
          video={video}
          isProOrCreator={isProOrCreator}
          calculatedMetrics={calculatedMetrics}
        />
      )}

      {/* Pro/Creator Only Advanced Analytics */}
      {isProOrCreator && <ProAnalytics />}

      {/* Call-to-action for non-Pro users */}
      {!isProOrCreator && <UpgradePrompt type="cta" />}
    </motion.div>
  );
}