import { getIronSession } from 'iron-session';
import { redirect } from 'next/navigation';
import { getUserById, getUserProfile } from '@/lib/db/user';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/prisma';
import { getMaintenance } from './maintenance';
import { hasPermission } from './admin';

export interface UserSessionData {
  userId: string;
  accessToken: string;
}

export interface EmailSessionData {
  email: string;
}

interface TwoFaDataSession {
  context: string | null;
  email: string;
}

export const userSessionOptions = {
  password: process.env.SESSION_PASSWORD!,
  cookieName: 'streambliss.user-session',
  ttl: 60 * 60 * 24 * 7, // 1 week
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
  },
};

export const emailSessionOptions = {
  password: process.env.SESSION_PASSWORD!,
  cookieName: 'streambliss.email-session',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
  },
};

export const twofaSessionOptions = {
  password: process.env.SESSION_PASSWORD!,
  cookieName: 'streambliss.twofa-session',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
  },
};

export async function getUserSession() {
  const userCookie = await cookies();
  const session = await getIronSession<UserSessionData>(userCookie, userSessionOptions);
  return session;
}

export async function getUser(isLogin: boolean = false) {
  const session = await getUserSession();
  if (!session || !session.userId || !session.accessToken) redirect('/login');

  const res = await fetch(`${process.env.VIDEO_API_URL}/auth/verify`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
      'x-api-key': process.env.API_SERVER_KEY!,
    },
  });

  if (!res.ok) {
    console.warn('JWT verification failed, redirecting...');
    redirect('/login');
  }

  const verified = await res.json();
  if (!verified?.valid) redirect('/login');

  const user = await getUserProfile(session.userId);
  if (!user) redirect('/login');

  const _user = await getUserById(user.id);
  if (!_user) redirect('/login');

  if (!_user.verified && isLogin) {
    return null;
  }

  if (user.banReason.length > 0) {
    redirect('/login?banned=true');
  }

  const isMaintenance = await getMaintenance();
  if (isMaintenance && !await hasPermission(_user.roleId, ['MAINTENANCE_BYPASS'])) {
    redirect('/maintenance');
  }

  return user;
}

export async function getEmailSession() {
  const emailCookie = await cookies();
  const session = await getIronSession<EmailSessionData>(emailCookie, emailSessionOptions);
  return session;
}

export async function getVerificationEmail() {
  const session = await getEmailSession();
  if (!session || !session.email) redirect('/login');
  return session.email;
}

export async function getTwoFactorSession() {
  const twoFaCookie = await cookies();
  const session = await getIronSession<TwoFaDataSession>(twoFaCookie, twofaSessionOptions);
  return session;
}

export async function getAdminUser(requiredPermissions: string[]) {
  const user = await getUser();
  if (!user) redirect('/');

  const dbUser = await prisma.user.findUnique({
    where: { id: user.id },
    include: {
      role: {
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
        },
      },
    },
  });

  if (!dbUser) redirect('/');

  const userPermissions = dbUser.role.permissions.map(p => p.permission.name);
  if (userPermissions.includes('ALL')) return dbUser;

  const hasAccess = requiredPermissions.some(permission => userPermissions.includes(permission));

  return hasAccess ? dbUser : userPermissions.includes('ADMIN_PAGE') ? redirect('/admin') : redirect('/');
}
