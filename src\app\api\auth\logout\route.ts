import { NextResponse } from 'next/server';
import { getEmailSession, getTwoFactorSession, getUserSession } from 'src/server/session';

export async function POST() {
  const session = await getUserSession();

  const logoutRequest = await fetch(process.env.VIDEO_API_URL + '/auth/logout', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': process.env.API_SERVER_KEY!,
      'Authorization': `Bearer ${session.accessToken}`,
    },
  });

  if (!logoutRequest.ok) {
    return new Response(null, {
      status: 500,
    });
  }

  const userSession = await getUserSession();
  userSession.destroy();

  const emailSession = await getEmailSession();
  emailSession.destroy();

  const twoFactorSession = await getTwoFactorSession();
  twoFactorSession.destroy();

  return NextResponse.json({ success: true });
}