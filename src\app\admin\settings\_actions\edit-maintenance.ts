"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { addMaintenanceUpdate, setMaintenance, setMaintenanceDuration, setMaintenanceStart } from "@/server/maintenance";
import { getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type Params = {
  startDate: Date;
  endDate: Date;
};

export default async function editMaintenance({ startDate, endDate }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!startDate || !endDate) {
    return {
      success: false,
      message: "Invalid date range",
    };
  };

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_SETTINGS_EDIT"]);
  if (!user) {
    return {
      success: false,
      message: "Unauthorized action"
    };
  }

  if (startDate > endDate) {
    return {
      success: false,
      message: "Start date cannot be greater than end date",
    };
  }

  const duration = Math.floor((endDate.getTime() - startDate.getTime()) / 1000);
  await setMaintenance(true);
  await setMaintenanceDuration(duration);
  await setMaintenanceStart(startDate);
  await addMaintenanceUpdate("Maintenance has been started", "INFO");
  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_MAINTENANCE_UPDATED, LogActions.ACCOUNT);

  return {
    success: true,
    message: "Maintenance saved successfully",
  };
}
