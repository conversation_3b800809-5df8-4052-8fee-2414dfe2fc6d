"use server";

import { decryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { createNotification } from "@/server/notifications";
import { getUser } from "@/server/session";
import { getUserSettings } from "@/server/user-settings";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
}

type Params = {
  videoId: string;
  removalReason: string;
};

const API_URL = process.env.VIDEO_API_URL || "http://localhost:9999";

export default async function submitRemoveVideo({ videoId, removalReason }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!videoId) {
    return {
      success: false,
      message: "Video ID is required."
    }
  }

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_REMOVE_VIDEO"]);
  if (!user) {
    return {
      success: false,
      message: "User is not authenticated."
    }
  }

  const userSettings = await getUserSettings(adminUser.id);
  if (!userSettings) {
    return {
      success: false,
      message: "User settings not found."
    }
  }

  const videoData = await prisma.video.findUnique({
    where: { id: videoId }
  });
  if (!videoData) {
    return {
      success: false,
      message: "Video not found."
    }
  }

  const decryptedAuthToken = await decryptData(userSettings.secretToken, userSettings.secretTokenIv);

  // TODO: send invoice to user that the video has been removed
  const deletion = await fetch(API_URL + `/admin/video/` + adminUser.id + "/" + videoData.id, {
    method: "DELETE",
    headers: {
      "Auth-Token": decryptedAuthToken
    }
  });

  if (!deletion.ok) {
    return {
      success: false,
      message: "Failed to remove video."
    }
  }

  await prisma.video.delete({
    where: { id: videoId }
  });

  await createNotification(videoData.userId, "Your Video has been removed: " + removalReason, "WARNING");
  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_VIDEO_DELETE, LogActions.VIDEO);

  return {
    success: true,
    message: `Video ${videoId} has been removed successfully.`
  }
}
