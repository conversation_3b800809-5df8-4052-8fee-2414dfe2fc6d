"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowLeft,
  Calendar,
  Video as VideoIcon,
  Eye,
  ExternalLink,
  MoreHorizontal,
  Share2,
  UserPlus,
  Settings,
  Filter,
  Grid3X3,
  List,
  TrendingUp,
  Clock,
  Play,
} from "lucide-react";
import {
  FaTwitter,
  FaInstagram,
  FaTwitch,
  FaYoutube,
  FaGlobe,
} from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { OptimizedVideoGrid } from "../../_components/optimized-video-grid";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";
import type { Video } from "@/types/video";

type UserProfileClientProps = {
  user: {
    id: string;
    name: string | null;
    image: string;
    website?: string | null;
    twitter?: string | null;
    instagram?: string | null;
    twitch?: string | null;
    youtube?: string | null;
    createdAt: Date;
    videosCount: number;
  };
  videos: Video[];
};

type SortOption = "recent" | "popular" | "oldest";

const SOCIAL_ICONS = {
  website: FaGlobe,
  twitter: FaTwitter,
  instagram: FaInstagram,
  twitch: FaTwitch,
  youtube: FaYoutube,
};

export function UserProfileClient({ user, videos }: UserProfileClientProps) {
  const router = useRouter();
  const [sortOption, setSortOption] = useState<SortOption>("recent");
  const [imageError, setImageError] = useState(false);

  const totalViews = useMemo(() => {
    return videos.reduce((sum, video) => sum + (video.views || 0), 0);
  }, [videos]);

  const sortedVideos = useMemo(() => {
    const sorted = [...videos];
    switch (sortOption) {
      case "recent":
        return sorted.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
      case "popular":
        return sorted.sort((a, b) => (b.views || 0) - (a.views || 0));
      case "oldest":
        return sorted.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      default:
        return sorted;
    }
  }, [videos, sortOption]);

  const socialLinks = useMemo(() => {
    return Object.entries(SOCIAL_ICONS).filter(([key]) => {
      const value = user[key as keyof typeof user];
      return value && typeof value === "string";
    });
  }, [user]);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-black/95 backdrop-blur-sm border-b border-zinc-800">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.push("/community")}
              className="text-zinc-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Community
            </Button>

            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Share2 className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Profile Section */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Left Side - Profile Info */}
          <div className="md:w-80 flex-shrink-0">
            <div className="sticky top-24">
              {/* Avatar */}
              <div className="w-32 h-32 rounded-full overflow-hidden bg-zinc-800 mb-6">
                {user.image && !imageError ? (
                  <Image
                    src={user.image}
                    alt={user.name || "User"}
                    width={128}
                    height={128}
                    className="w-full h-full object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full bg-zinc-700 flex items-center justify-center">
                    <span className="text-zinc-400 text-3xl font-bold">
                      {user.name?.charAt(0)?.toUpperCase() || "U"}
                    </span>
                  </div>
                )}
              </div>

              {/* Name */}
              <h1 className="text-3xl font-bold mb-2">{user.name}</h1>

              {/* Join Date */}
              <p className="text-zinc-400 mb-6 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Joined{" "}
                {formatDistanceToNow(new Date(user.createdAt), {
                  addSuffix: true,
                })}
              </p>

              {/* Stats */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-zinc-400">Videos</span>
                  <span className="font-semibold">
                    {user.videosCount.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-zinc-400">Total Views</span>
                  <span className="font-semibold">
                    {totalViews.toLocaleString()}
                  </span>
                </div>
                {totalViews > 0 && user.videosCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-zinc-400">Avg. Views</span>
                    <span className="font-semibold">
                      {Math.round(
                        totalViews / user.videosCount,
                      ).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>

              {/* Social Links */}
              {socialLinks.length > 0 && (
                <div className="space-y-3 mb-6">
                  <h3 className="text-sm font-medium text-zinc-400 uppercase tracking-wide">
                    Links
                  </h3>
                  <div className="space-y-2">
                    {socialLinks.map(([key, Icon]) => {
                      const value = user[key as keyof typeof user] as string;
                      let url = value;

                      if (key === "twitter")
                        url = `https://twitter.com/${value}`;
                      else if (key === "instagram")
                        url = `https://instagram.com/${value}`;
                      else if (key === "twitch")
                        url = `https://twitch.tv/${value}`;
                      else if (key === "youtube")
                        url = value.startsWith("http")
                          ? value
                          : `https://youtube.com/${value}`;
                      else if (key === "website")
                        url = value.startsWith("http")
                          ? value
                          : `https://${value}`;

                      return (
                        <Link
                          key={key}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 text-zinc-300 hover:text-white transition-colors group"
                        >
                          <Icon className="w-4 h-4" />
                          <span className="text-sm capitalize">{key}</span>
                          <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Follow Button */}
              <Button className="w-full bg-white text-black hover:bg-zinc-200">
                <UserPlus className="w-4 h-4 mr-2" />
                Follow
              </Button>
            </div>
          </div>

          {/* Right Side - Videos */}
          <div className="flex-1">
            {/* Content Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold mb-1">Videos</h2>
                <p className="text-zinc-400 text-sm">{videos.length} videos</p>
              </div>

              {/* Sort Options */}
              <div className="flex items-center gap-1 bg-zinc-900 rounded-lg p-1">
                <button
                  onClick={() => setSortOption("recent")}
                  className={cn(
                    "px-3 py-1.5 text-sm rounded-md transition-colors",
                    sortOption === "recent"
                      ? "bg-white text-black font-medium"
                      : "text-zinc-400 hover:text-white",
                  )}
                >
                  Recent
                </button>
                <button
                  onClick={() => setSortOption("popular")}
                  className={cn(
                    "px-3 py-1.5 text-sm rounded-md transition-colors",
                    sortOption === "popular"
                      ? "bg-white text-black font-medium"
                      : "text-zinc-400 hover:text-white",
                  )}
                >
                  Popular
                </button>
                <button
                  onClick={() => setSortOption("oldest")}
                  className={cn(
                    "px-3 py-1.5 text-sm rounded-md transition-colors",
                    sortOption === "oldest"
                      ? "bg-white text-black font-medium"
                      : "text-zinc-400 hover:text-white",
                  )}
                >
                  Oldest
                </button>
              </div>
            </div>

            {/* Videos Grid */}
            {sortedVideos.length > 0 ? (
              <OptimizedVideoGrid videos={sortedVideos} soundEnabled={true} />
            ) : (
              <div className="text-center py-16">
                <div className="w-16 h-16 bg-zinc-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <VideoIcon className="w-8 h-8 text-zinc-400" />
                </div>
                <h3 className="text-lg font-medium mb-2">No videos yet</h3>
                <p className="text-zinc-400 text-sm">
                  {user.name} hasn&apos;t uploaded any videos to the community
                  yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}