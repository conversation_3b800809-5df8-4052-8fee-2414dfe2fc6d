"use server"

import { prisma } from "@/lib/prisma"
import { getClientIp } from "./geolocation";
import { rateLimiterViews } from "@/lib/rate-limit";
const API_URL = process.env.VIDEO_API_URL || "http://localhost:9999";

export const getVideoDataByShortLink = async (shortLink: string) => {
  const videoData = await prisma.video.findUnique({
    where: {
      shortLink,
    },
  })
  return videoData
}

export const requestVideo = async (userId: string, videoId: string) => {
  return `${API_URL}/videos/${videoId}`
}

export const requestVideoThumbnail = async (userId: string, videoId: string) => {
  return `${API_URL}/videos/thumbnail/${videoId}`
}

export const getVideoOwner = async (videoId: string): Promise<string | null> => {
  const owner = await prisma.video.findUnique({
    where: {
      id: videoId,
    },
    select: {
      userId: true,
    }
  });
  if (owner == null) return null;

  return owner.userId;
}

export const getVideoDataById = async (videoId: string) => {
  const videoData = await prisma.video.findUnique({
    where: {
      id: videoId,
    },
  })
  return videoData
}

export const updateVideo = async (videoId: string, title: string, isPrivate: boolean, commentsDisabled: boolean, musicDisabled: boolean, showCommunity: boolean) => {
  await prisma.video.update({
    where: {
      id: videoId,
    },
    data: {
      musicDisabled: musicDisabled,
      commentsDisabled: commentsDisabled,
      isPrivate: isPrivate,
      title,
      showCommunity,
    }
  })
}

export const approveVideoForCommunity = async (videoId: string) => {
  return prisma.video.update({
    where: {
      id: videoId
    },
    data: {
      approvedForCommunity: true,
      showCommunity: true
    }
  })
};

export const rejectVideoForCommunity = async (videoId: string) => {
  return prisma.video.update({
    where: {
      id: videoId
    },
    data: {
      approvedForCommunity: false,
      showCommunity: false
    }
  })
};

export const likeVideo = async (videoId: string, userId: string) => {
  try {
    const dbVideo = await prisma.video.findUnique({
      where: {
        id: videoId,
      },
    });

    if (dbVideo == null) return null;

    const dbUser = await prisma.user.findUnique({
      where: {
        id: userId,
      },
    });
    if (dbUser == null) return null;

    if (await hasLikedVideo(videoId, userId)) {
      await unlikeVideo(videoId, userId);
      return true;
    }

    return await prisma.videoLikes.create({
      data: {
        userId: userId,
        videoId: videoId,
      },
    });
  } catch (exception) {
    console.error("Error liking video", exception);
    return null;
  }
}

export const dislikeVideo = async (videoId: string, userId: string) => {
  try {
    const dbVideo = await prisma.video.findUnique({
      where: {
        id: videoId,
      },
    });

    if (dbVideo == null) return null;

    const dbUser = await prisma.user.findUnique({
      where: {
        id: userId,
      },
    });
    if (dbUser == null) return null;

    if (await hasDislikedVideo(videoId, userId)) {
      await unDislikeVideo(videoId, userId);
      return true;
    }

    return await prisma.videoDislikes.create({
      data: {
        userId: userId,
        videoId: videoId,
      },
    });
  } catch (exception) {
    console.error("Error disliking video", exception);
    return null;
  }
}
export const unDislikeVideo = async (videoId: string, userId: string) => {
  try {
    const dbLike = await prisma.videoDislikes.findFirst({
      where: {
        userId: userId,
        videoId: videoId,
      },
    });

    if (dbLike == null) return;

    await prisma.videoDislikes.delete({
      where: {
        id: dbLike.id,
      },
    });
  } catch (exception) {
    console.error("Error undisliking video", exception);
  }
}
export const unlikeVideo = async (videoId: string, userId: string) => {
  try {
    const dbLike = await prisma.videoLikes.findFirst({
      where: {
        userId: userId,
        videoId: videoId,
      },
    });

    if (dbLike == null) return;

    await prisma.videoLikes.delete({
      where: {
        id: dbLike.id,
      },
    });
  } catch (exception) {
    console.error("Error unliking video", exception);
  }
}
export const hasLikedVideo = async (videoId: string, userId: string) => {
  try {
    const dbLike = await prisma.videoLikes.findFirst({
      where: {
        userId: userId,
        videoId: videoId,
      },
    });

    return dbLike != null;
  } catch (exception) {
    console.error("Error checking if user liked video", exception);
    return false;
  }
}
export const hasDislikedVideo = async (videoId: string, userId: string) => {
  try {
    const dbDislike = await prisma.videoDislikes.findFirst({
      where: {
        userId: userId,
        videoId: videoId,
      },
    });
    console.log(dbDislike)

    return dbDislike != null;
  } catch (exception) {
    console.error("Error checking if user disliked video", exception);
    return false;
  }
}

export const getVideoLikes = async (videoId: string) => {
  try {
    const dbLikes = await prisma.videoLikes.count({
      where: {
        videoId: videoId,
      },
    });

    return dbLikes;
  } catch (exception) {
    console.error("Error getting video likes", exception);
    return null;
  }
}

export const getVideoDislikes = async (videoId: string) => {
  try {
    const dbLikes = await prisma.videoDislikes.count({
      where: {
        videoId: videoId,
      },
    });

    return dbLikes;
  } catch (exception) {
    console.error("Error getting video dislikes", exception);
    return null;
  }
}

export const getCommunityVideosForApproval = async () => {
  try {
    return await prisma.video.findMany({
      where: {
        showCommunity: true,
        approvedForCommunity: false
      },
      orderBy: {
        createdAt: "desc"
      }
    })
  } catch (error) {
    console.error("Error getting community videos", error);
    return [];
  }
}