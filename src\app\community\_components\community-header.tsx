"use client";

import { type FC, useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Menu, User, LogIn, UserPlus } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { DialogTitle } from "@/components/ui/dialog";

interface CommunityHeaderProps {
  title?: string;
  isLoggedIn?: boolean;
}

export const CommunityHeader: FC<CommunityHeaderProps> = ({
  title = "Community",
  isLoggedIn: serverIsLoggedIn,
}) => {
  const router = useRouter();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    if (serverIsLoggedIn !== undefined) {
      setIsLoggedIn(serverIsLoggedIn);
      return;
    }

    const hasSessionCookie = document.cookie.includes(
      "streambliss.user-session",
    );
    setIsLoggedIn(hasSessionCookie);
  }, [serverIsLoggedIn]);

  return (
    <header className="bg-black/70 backdrop-blur-xl border-b border-white/5 shadow-lg">
      <div className="max-w-[1320px] mx-auto px-4 lg:px-[70px] py-8">
        <nav className="flex items-center justify-between">
          {/* Mobile View */}
          <div className="md:hidden flex items-center w-full justify-between">
            <div className="flex items-center gap-6">
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 hover:bg-purple-500/10 border border-purple-500/20"
                  >
                    <Menu className="h-5 w-5 text-purple-300" />
                  </Button>
                </SheetTrigger>
                <SheetContent
                  side="left"
                  className="w-[300px] sm:w-[350px] bg-slate-950/95 backdrop-blur-xl border-purple-500/20"
                >
                  <DialogTitle className="sr-only">
                    StreamBliss Navigation
                  </DialogTitle>
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between py-6">
                      <Link href="/" className="flex items-center space-x-3">
                        <Image
                          src="/assets/images/svg/footer-logo.svg"
                          alt="StreamBliss"
                          width={200}
                          height={32}
                          className="h-8 w-auto"
                        />
                      </Link>
                    </div>

                    <div className="border-t border-purple-500/20 my-6" />

                    <div className="mt-auto pt-4 space-y-4">
                      {isLoggedIn ? (
                        <Button
                          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg rounded-full px-6 py-3.5 font-semibold"
                          onClick={() => {
                            router.push("/dashboard");
                            setIsMobileMenuOpen(false);
                          }}
                        >
                          Dashboard
                        </Button>
                      ) : (
                        <>
                          <Button
                            variant="outline"
                            className="w-full border-purple-400/50 text-purple-300 hover:bg-purple-500/10 hover:border-purple-400"
                            onClick={() => {
                              router.push("/login");
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <LogIn className="w-4 h-4 mr-2" />
                            Log in
                          </Button>
                          <Button
                            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg"
                            onClick={() => {
                              router.push("/register");
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <UserPlus className="w-4 h-4 mr-2" />
                            Sign up
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              <Link href="/" className="flex items-center gap-6">
                <Image
                  src="/assets/images/svg/footer-logo.svg"
                  alt="StreamBliss"
                  width={240}
                  height={32}
                  className="h-8 w-auto min-w-[240px]"
                />
                <div className="w-px h-12 bg-white/20" />
                <span className="text-white text-lg font-semibold font-montserrat">
                  {title}
                </span>
              </Link>
            </div>

            <div className="flex items-center">
              {isLoggedIn ? (
                <Button
                  className="bg-gradient-to-b from-[#b851e0] to-[#eb489b] hover:opacity-90 text-white shadow-lg transition-all duration-300 rounded-full px-6 py-3.5 font-semibold text-lg font-montserrat"
                  onClick={() => router.push("/dashboard")}
                >
                  Dashboard
                </Button>
              ) : (
                <Button
                  className="bg-gradient-to-b from-[#b851e0] to-[#eb489b] hover:opacity-90 text-white shadow-lg transition-all duration-300 rounded-full px-6 py-3.5 font-semibold text-lg font-montserrat"
                  onClick={() => router.push("/register")}
                >
                  Dashboard
                </Button>
              )}
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-between w-full">
            <div className="flex items-center gap-6">
              <Link href="/" className="flex items-center gap-6">
                <Image
                  src="/assets/images/svg/footer-logo.svg"
                  alt="StreamBliss"
                  width={271}
                  height={40}
                  className="h-10 w-auto min-w-[271px]"
                />
                <div className="w-px h-12 bg-white/20" />
                <span className="text-white text-xl font-semibold font-montserrat whitespace-nowrap">
                  {title}
                </span>
              </Link>
            </div>

            <div className="flex items-center">
              {isLoggedIn ? (
                <Button
                  className="bg-gradient-to-b from-[#b851e0] to-[#eb489b] hover:opacity-90 text-white shadow-lg transition-all duration-300 rounded-full px-6 py-3.5 font-semibold text-lg font-montserrat min-h-[57px] min-w-[150px]"
                  onClick={() => router.push("/dashboard")}
                >
                  Dashboard
                </Button>
              ) : (
                <Button
                  className="bg-gradient-to-b from-[#b851e0] to-[#eb489b] hover:opacity-90 text-white shadow-lg transition-all duration-300 rounded-full px-6 py-3.5 font-semibold text-lg font-montserrat min-h-[57px] min-w-[150px]"
                  onClick={() => router.push("/register")}
                >
                  Dashboard
                </Button>
              )}
            </div>
          </div>
        </nav>
      </div>
    </header>
  );
};