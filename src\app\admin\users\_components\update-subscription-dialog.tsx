"use client";

import { Lock } from "lucide-react";
import UpdateSubscriptionForm from "./update-subscription-form";
import { DialogTitle } from "@/components/ui/dialog";

type UpdateSubscriptionFormProps = {
    userId: string | undefined;
    subscriptions: { name: string }[];
    onComplete: (close: boolean) => void;
};

export default function UpdateSubscriptionDialog({userId, subscriptions, onComplete}: UpdateSubscriptionFormProps) {
    return (
        <div className="grid gap-4">
            <DialogTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5 text-primary" />
                Change the users subscription!
            </DialogTitle>
            <UpdateSubscriptionForm userId={userId} subscriptions={subscriptions} onComplete={onComplete} />
        </div>
    )
}