"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { <PERSON><PERSON><PERSON>t, Bell, User, Check, <PERSON><PERSON>he<PERSON>, Shield } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistance } from "date-fns";
import submitReadNotification from "../../../dashboard/_actions/read-notification";
import submitReadAllNotifications from "../../../dashboard/_actions/read-all-notifications";
import { NewDashboardSidebar } from "../../../dashboard/_components/new-dashboard-sidebar";
import { MobileNav } from "../../../dashboard/_components/mobile-nav";
import { VideoAnalytics } from "../../../dashboard/_components/video-analytics";
import { $Enums } from "@prisma/client";

interface VideoAnalyticsClientProps {
  video: {
    id: string;
    title: string;
    views: number;
    createdAt: string | Date;
    userId: string;
  };
  userId: string;
  userName?: string;
  userImage?: string | null;
  notifications?: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  hasAccessToAdmin?: boolean;
  userSubscription: string;
}

export default function VideoAnalyticsClient({
  video,
  userId,
  userName = "",
  userImage,
  notifications = [],
  hasAccessToAdmin = false,
  userSubscription,
}: VideoAnalyticsClientProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [avatarError, setAvatarError] = useState(false);
  const [markingAsRead, setMarkingAsRead] = useState<string | null>(null);
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const unreadCount = notifications.filter((n) => !n.read).length;

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const handleMarkAsRead = async (notificationId: string) => {
    setMarkingAsRead(notificationId);
    try {
      const result = await submitReadNotification({ notificationId });
      if (result.success) {
        router.refresh();
      } else {
        error("Mark as Read Failed", result.message || "Failed to mark notification as read");
      }
    } catch (err) {
      error("Mark as Read Error", "Failed to mark notification as read");
    } finally {
      setMarkingAsRead(null);
    }
  };

  const handleMarkAllAsRead = async () => {
    setMarkingAllAsRead(true);
    try {
      const result = await submitReadAllNotifications();
      if (result.success) {
        success("All Marked as Read", "All notifications marked as read");
        router.refresh();
      } else {
        error("Mark All Failed", result.message || "Failed to mark all notifications as read");
      }
    } catch (err) {
      error("Mark All Error", "Failed to mark all notifications as read");
    } finally {
      setMarkingAllAsRead(false);
    }
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Desktop Sidebar - Only shown on desktop */}
      {!isMobile && <NewDashboardSidebar />}

      {/* Mobile Navigation - Fixed at top */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 flex items-center justify-between p-3 bg-black border-b border-white/10">
        <div className="flex items-center gap-2">
          <MobileNav userName={userName} userImage={userImage} />
          <Image
            src="/assets/images/svg/logo.svg"
            alt="StreamBliss"
            width={24}
            height={22}
            className="w-6 h-auto"
          />
          <span className="text-white font-semibold text-sm">StreamBliss</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-xs text-white/70">Analytics</div>
          {userImage && userImage.trim() !== "" && !avatarError ? (
            <Image
              src={userImage}
              alt={userName || "Avatar"}
              width={24}
              height={24}
              className="h-6 w-6 object-cover rounded-full border border-white/10"
              onError={() => setAvatarError(true)}
            />
          ) : (
            <div className="w-6 h-6 rounded-full border border-white/10 bg-white/5 flex items-center justify-center">
              <User className="h-3 w-3 text-white/70" />
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="pt-16 md:pt-0 md:ml-[72px] min-h-screen">
        <div className="p-3 md:p-6">
          <div className="rounded-xl md:rounded-[30px] bg-[rgba(184,81,223,0.06)] border border-[rgba(184,81,223,0.12)] p-3 md:p-6">
            {/* Desktop Header - Only shown on desktop */}
            <div className="hidden md:block mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <Link href="/dashboard">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 transition-all duration-200"
                    >
                      <ArrowLeft className="w-5 h-5 text-white" />
                    </Button>
                  </Link>
                  <div>
                    <h1 className="text-2xl font-semibold text-white leading-[1.4] mb-2 font-['Montserrat']">
                      Video Analytics
                    </h1>
                    <p className="text-white/70 text-sm font-normal leading-[1.6] font-['Montserrat']">
                      {video.title}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  {/* Desktop notifications and user info */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="relative w-10 h-10 rounded-full bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300 group"
                      >
                        <Bell
                          className={`h-5 w-5 transition-all duration-300 ${
                            unreadCount > 0
                              ? "text-white animate-pulse"
                              : "text-white/70 group-hover:text-white"
                          }`}
                        />
                        {unreadCount > 0 && (
                          <span className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-bold bg-gradient-to-r from-[#b851e0] to-[#eb489b] text-white rounded-full shadow-lg shadow-[#b851e0]/30 animate-pulse">
                            {unreadCount > 9 ? "9+" : unreadCount}
                          </span>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-[380px] border border-white/24 bg-[#110018] shadow-2xl shadow-black/50 rounded-xl overflow-hidden font-['Montserrat'] p-0"
                    >
                      <div className="flex items-center justify-between px-6 py-4 border-b border-white/12">
                        <h3 className="text-white text-lg font-semibold">
                          Notifications
                        </h3>
                        <div className="flex items-center gap-3">
                          {unreadCount > 0 && (
                            <button
                              onClick={handleMarkAllAsRead}
                              disabled={markingAllAsRead}
                              className="flex items-center gap-1.5 text-white/60 hover:text-white text-xs font-medium transition-colors duration-200 disabled:opacity-50"
                            >
                              <CheckCheck className="h-3.5 w-3.5" />
                              {markingAllAsRead
                                ? "Marking..."
                                : "Mark all read"}
                            </button>
                          )}
                          {unreadCount > 0 && (
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-[#b851e0] rounded-full animate-pulse"></div>
                              <span className="text-white/70 text-sm">
                                {unreadCount} new
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="max-h-[400px] overflow-y-auto">
                        {notifications.length === 0 ? (
                          <div className="px-6 py-12 text-center">
                            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-white/5 border border-white/10 flex items-center justify-center">
                              <Bell className="h-6 w-6 text-white/40" />
                            </div>
                            <h4 className="text-white text-sm font-medium mb-1">
                              No notifications yet
                            </h4>
                            <p className="text-white/60 text-xs">
                              You&apos;ll see notifications here when they
                              arrive
                            </p>
                          </div>
                        ) : (
                          <div className="py-2">
                            {[...notifications]
                              .sort(
                                (a, b) =>
                                  new Date(b.createdAt).getTime() -
                                  new Date(a.createdAt).getTime(),
                              )
                              .map((notification, index) => (
                                <div
                                  key={notification.id}
                                  onClick={() =>
                                    !notification.read &&
                                    handleMarkAsRead(notification.id)
                                  }
                                  className={`relative px-6 py-4 transition-all duration-200 group ${
                                    !notification.read
                                      ? "bg-white/[0.02] hover:bg-white/[0.04] cursor-pointer"
                                      : "hover:bg-white/[0.02] cursor-default"
                                  } ${index < notifications.length - 1 ? "border-b border-white/8" : ""} ${
                                    markingAsRead === notification.id
                                      ? "opacity-60 pointer-events-none"
                                      : ""
                                  }`}
                                >
                                  {!notification.read && (
                                    <div className="absolute left-2 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-[#b851e0] to-[#eb489b] rounded-full"></div>
                                  )}

                                  <div className="flex items-start gap-4">
                                    <div
                                      className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mt-0.5 ${
                                        !notification.read
                                          ? "bg-gradient-to-br from-[#b851e0]/20 to-[#eb489b]/20 border border-[#b851e0]/30"
                                          : "bg-white/5 border border-white/10"
                                      }`}
                                    >
                                      <Check
                                        className={`h-4 w-4 ${!notification.read ? "text-[#b851e0]" : "text-white/60"}`}
                                      />
                                    </div>

                                    <div className="flex-1 min-w-0">
                                      <p
                                        className={`text-sm leading-relaxed ${
                                          !notification.read
                                            ? "text-white font-medium"
                                            : "text-white/80 font-normal"
                                        }`}
                                      >
                                        {notification.data}
                                      </p>
                                      <div className="flex items-center justify-between mt-2">
                                        <div className="flex items-center gap-2">
                                          <p className="text-xs text-white/50">
                                            {formatDistance(
                                              new Date(notification.createdAt),
                                              new Date(),
                                              {
                                                addSuffix: true,
                                              },
                                            )}
                                          </p>
                                          {!notification.read && (
                                            <div className="w-1 h-1 bg-[#b851e0] rounded-full"></div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {hasAccessToAdmin && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="relative w-10 h-10 rounded-full bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                      onClick={() => router.push("/admin")}
                    >
                      <Shield className="h-5 w-5 text-white/70" />
                    </Button>
                  )}

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full border border-white/10 bg-white/5 overflow-hidden">
                      {userImage && userImage.trim() !== "" && !avatarError ? (
                        <Image
                          src={userImage}
                          alt={userName || "Avatar"}
                          width={40}
                          height={40}
                          className="h-full w-full object-cover rounded-full"
                          onError={() => setAvatarError(true)}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <User className="h-5 w-5 text-white/70" />
                        </div>
                      )}
                    </div>
                    <div className="text-white">
                      <p className="text-sm font-medium">Welcome Back</p>
                      <p className="text-xs text-white/70">{userName}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Header */}
            <div className="md:hidden mb-3">
              <div className="flex items-center gap-2 mb-2">
                <Link href="/dashboard">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="rounded-full bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 transition-all duration-200 p-2"
                  >
                    <ArrowLeft className="w-4 h-4 text-white" />
                  </Button>
                </Link>
                <h1 className="text-lg font-semibold text-white leading-[1.4] font-['Montserrat']">
                  Video Analytics
                </h1>
              </div>
              <p className="text-white/70 text-xs font-normal leading-[1.6] font-['Montserrat'] truncate">
                {video.title}
              </p>
            </div>

            {/* Analytics Content */}
            <div className="bg-black/20 border border-white/10 rounded-xl md:rounded-2xl shadow-[0_4px_22px_rgba(0,0,0,0.25)] overflow-hidden">
              <VideoAnalytics
                video={video}
                userId={userId}
                userSubscription={userSubscription}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}