import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {access_token} = await request.json();
    if (!access_token) return NextResponse.json({error: "Missing access_token"}, {status: 400});

    try {
        const googleLoginRequest = await fetch(process.env.VIDEO_API_URL + "/auth/oauth-login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "x-api-key": process.env.API_SERVER_KEY!,
            },
            body: JSON.stringify({
                provider: 'google',
                access_token,
            })
        });

        const data = await googleLoginRequest.json();
        if (data.statusCode === 500) {
            return NextResponse.json({status: 500, message: "Error while logging in via google"});
        }

        const session = await getUserSession();
        session.userId = data.user.id;
        session.accessToken = data.accessToken;
        await session.save();

        return NextResponse.json({status: 200, redirect: '/dashboard'});
    } catch (error) {
        console.error("Discord OAuth Error:", error);
        return NextResponse.json({error: (error as Error).message}, {status: 500});
    }
}
