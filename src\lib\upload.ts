// import sharp from "sharp"
// import path from "path"
// import fs from "fs/promises"
// import { nanoid } from "nanoid"

// const UPLOAD_DIR = path.join(process.cwd(), "public/uploads")

// // Ensure upload directory exists
// await fs.mkdir(UPLOAD_DIR, { recursive: true })

// export async function saveProfileImage(file: Buffer): Promise<string> {
//   const fileName = `profile-${nanoid()}.webp`
//   const filePath = path.join(UPLOAD_DIR, fileName)

//   // Process image with sharp
//   await sharp(file)
//     .resize(256, 256, {
//       fit: "cover",
//       position: "center",
//     })
//     .webp({ quality: 80 })
//     .toFile(filePath)

//   // Return the public URL
//   return `/uploads/${fileName}`
// }