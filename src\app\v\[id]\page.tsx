import { redirect } from "next/navigation"
import type { Metadata, ResolvingMetadata } from "next"
import { prisma } from "@/lib/prisma"
import { hasDislikedVideo, hasLikedVideo, requestVideo, requestVideoThumbnail } from "@/server/video"
import ShortLinkPageClient from "./client"
import { getUserSession } from "@/server/session"
import { getUserById } from "@/lib/db/user"
import { getCommentsByVideoId } from "@/server/comment"
import { getUserProfilePicture } from "@/server/profile"
import { hasPermission } from "@/server/admin"
import { hasChannelNotificationsEnabled, hasSubscribedChannel } from "@/server/channel"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { motion } from "framer-motion"

export async function generateMetadata(props: { params: Promise<{ id: string }> }, parent: ResolvingMetadata): Promise<Metadata> {
  const params = await props.params;
  // Fetch video data for metadata
  const video = await prisma.video.findUnique({
    where: { shortLink: params.id },
    select: {
      id: true,
      title: true,
      userId: true,
      views: true,
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      createdAt: true,
      isPrivate: true,
    },
  })

  if (!video || video.isPrivate) {
    return {
      title: "Video Unavailable | StreamBliss",
      description: "This video is currently unavailable or has been made private.",
    }
  }

  // Get thumbnail URL for metadata
  const thumbnailUrl = await requestVideoThumbnail(video.userId, video.id)
  const videoUrl = await requestVideo(video.userId, video.id)
  const previousImages = (await parent).openGraph?.images || []
  const pageUrl = `${process.env.NEXT_PUBLIC_APP_URL || "https://streambliss.cloud"}/v/${params.id}`
  const uploaderName = video.user.name || video.user.email.split("@")[0]
  const absoluteVideoUrl = videoUrl.startsWith('http') ? videoUrl : `${process.env.VIDEO_API_URL || "https://api.streambliss.cloud"}${videoUrl}`

  return {
    title: video.title,
    description: `Watch ${video.title} on StreamBliss`,
    openGraph: {
      title: video.title,
      description: `Watch ${video.title} on StreamBliss`,
      url: pageUrl,
      siteName: "StreamBliss",
      images: [
        {
          url: thumbnailUrl,
          width: 1280,
          height: 720,
          alt: video.title,
        },
        ...previousImages,
      ],
      type: "video.other",
      videos: [
        {
          url: absoluteVideoUrl,
          width: 1280,
          height: 720,
          type: "video/mp4"
        }
      ]
    },
    twitter: {
      card: "player",
      title: video.title,
      description: `Watch ${video.title} on StreamBliss`,
      images: [thumbnailUrl],
      creator: "@streambliss",
    },
    other: {
      "og:video": absoluteVideoUrl,
      "og:video:url": absoluteVideoUrl,
      "og:video:secure_url": absoluteVideoUrl,
      "og:video:type": "video/mp4",
      "og:video:width": "1280",
      "og:video:height": "720",
      "og:type": "video",
      "og:image": thumbnailUrl,
      "og:image:secure_url": thumbnailUrl,
      "og:image:width": "1280",
      "og:image:height": "720",
      "og:site_name": "StreamBliss",
      "og:locale": "en_US",
      "og:url": pageUrl,

      "twitter:image": thumbnailUrl,
      "twitter:domain": new URL(process.env.NEXT_PUBLIC_APP_URL || "https://streambliss.cloud").hostname,
      "twitter:url": pageUrl,
      "twitter:player": absoluteVideoUrl,
      "twitter:player:width": "1280",
      "twitter:player:height": "720",
      "twitter:card": "player",

      "theme-color": "#7C3AED",
      "video:release_date": video.createdAt.toISOString(),
      "video:creator": uploaderName,
    },
  }
}

export default async function ShortLinkPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const video = await prisma.video.findUnique({
    where: {
      shortLink: params.id,
    },
    select: {
      id: true,
      title: true,
      userId: true,
      views: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      createdAt: true,
      musicDisabled: true,
      commentsDisabled: true,
      isPrivate: true,
    },
  })

  if (!video) {
    return (
      <div className="min-h-screen w-full bg-black flex flex-col items-center justify-center relative overflow-hidden">
        <div className="fixed inset-0">
          <div
            className="absolute w-[800px] h-[800px] rounded-full opacity-20 blur-[120px]"
            style={{
              background:
                "radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(138, 43, 226, 0.1) 40%, transparent 70%)",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />

          <div
            className="absolute w-[400px] h-[400px] rounded-full opacity-10 blur-[80px]"
            style={{
              background:
                "radial-gradient(circle, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.05) 50%, transparent 80%)",
              top: "15%",
              right: "10%",
              transform: "translate(50%, -50%)",
            }}
          />

          <div
            className="absolute inset-0 opacity-[0.03]"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
              backgroundRepeat: "repeat",
              backgroundSize: "128px 128px",
            }}
          />
        </div>

        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-[#B066FF]/10"
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 10 + 15}s linear infinite`,
                animationDelay: `${Math.random() * 5}s`,
                opacity: Math.random() * 0.3 + 0.1,
              }}
            />
          ))}
        </div>

        <motion.div 
          className="relative z-10 text-center px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-7xl font-bold gradient-text mb-4">Video Not Found</h1>
          <h2 className="text-2xl font-semibold text-white mb-4">This video is no longer available</h2>
          <p className="text-gray-400 mb-8 max-w-md mx-auto">
            The video you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Button asChild className="bg-[#B066FF] hover:bg-[#c78aff] text-white">
            <Link href="/community">Discover Community Content</Link>
          </Button>
        </motion.div>
      </div>
    )
  }

  // Get video and thumbnail URLs
  const videoUrl = await requestVideo(video.userId, video.id)
  const thumbnailUrl = await requestVideoThumbnail(video.userId, video.id)
  const comments = await getCommentsByVideoId(video.id)

  // Get uploader avatar
  const uploaderAvatarUrl = await getUserProfilePicture(video.user.id)

  let user: Awaited<ReturnType<typeof getUserById>> | null = null
  let isAdmin = false
  let currentUserAvatarUrl: string | null = null
  let likedVideo = false
  let dislikedVideo = false
  let isSubscribed = false
  let channelNotificationsEnabled = false

  const userSession = await getUserSession()
  if (userSession?.userId) {
    user = await getUserById(userSession.userId)
    if (user) {
      isAdmin = await hasPermission(user.roleId, ["ADMIN_VIDEO_COMMENT_DELETE"]) ?? false
      currentUserAvatarUrl = await getUserProfilePicture(user.id)
      likedVideo = await hasLikedVideo(video.id, user.id) ?? false
      dislikedVideo = await hasDislikedVideo(video.id, user.id) ?? false
      isSubscribed = await hasSubscribedChannel(user.id, video.userId) ?? false
      channelNotificationsEnabled = await hasChannelNotificationsEnabled(user.id, video.userId) ?? false
    }
  }

  return (
    <ShortLinkPageClient
      videoUrl={videoUrl}
      title={video.title}
      views={video.views + 1}
      videoId={video.id}
      thumbnailUrl={thumbnailUrl}
      uploader={{
        name: video.user.name || video.user.email.split("@")[0],
        email: video.user.email,
        avatarUrl: uploaderAvatarUrl,
        id: video.user.id,
      }}
      createdAt={video.createdAt}
      musicDisabled={video.musicDisabled}
      commentsDisabled={video.commentsDisabled}
      isPrivate={video.isPrivate}
      currentUser={user ? {
        id: user.id,
        name: user.name || user.email.split("@")[0],
        email: user.email,
        image: currentUserAvatarUrl || undefined,
      } : null}
      isAdmin={isAdmin}
      comments={comments}
      hasLikedVideo={likedVideo}
      hasDislikedVideo={dislikedVideo}
      isSubscribed={isSubscribed}
      channelNotificationsEnabled={channelNotificationsEnabled}
    />
  )
}