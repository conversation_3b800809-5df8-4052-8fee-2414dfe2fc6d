import "./globals.css";
import { Providers } from "./Providers";
import { ToastProvider } from "@/components/ui/enhanced-toast";

export const metadata = {
  title: "StreamBliss - Secure Video and Image Hosting",
  description:
    "Your one-stop solution for secure video and image hosting. Fast, reliable, and easy to use.",
  metadataBase: new URL("https://dev.streambliss.cloud"),
  openGraph: {
    type: "website",
    title: "StreamBliss - Secure Video and Image Hosting",
    description:
      "Your one-stop solution for secure video and image hosting. Fast, reliable, and easy to use.",
    images: [
      {
        url: "/assets/meta.webp",
        width: 1200,
        height: 630,
        alt: "StreamBliss - Secure Video and Image Hosting",
      },
    ],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body suppressHydrationWarning={true}>
        <Providers>
          <ToastProvider>
            {children}
          </ToastProvider>
        </Providers>
      </body>
    </html>
  );
}