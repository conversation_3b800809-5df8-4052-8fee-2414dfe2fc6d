"use server";

import {prisma} from "@/lib/prisma";
import {hash, compare} from "bcrypt";
import {encryptData} from "../crypter";
import {v4} from "uuid";
import type {Package} from "@prisma/client";
import {UserWithStats} from "./admin";

interface Permission {
    id: number;
    name: string;
}

interface RolePermission {
    permission: Permission;
    roleId: number;
    permissionId: number;
}

export interface User {
    id: string;
    email: string;
    name: string | null;
    package: Package;
    verified: boolean;
    roleId: number;
    createdAt: Date;
    updatedAt: Date;
    hashedPassword: string | null;
    instagram?: string | null;
    twitch?: string | null;
    twitter?: string | null;
    website?: string | null;
    youtube?: string | null;
    UserSettings?: {
        twoFactorEnabled: boolean;
    }[];
    banReason?: {
        id: number;
        reason: string;
        createdAt: Date;
        updatedAt: Date;
    }[];
    _count?: {
        videos: number;
        Image: number;
    };
    videos?: {
        views: number;
    }[];
    stripeAccountId?: string | null;
    videosCount?: number;
    imagesCount?: number;
    totalViews?: number;
}

export interface UserWithPermission extends User {
    permissions?: RolePermission[];
}

export interface UsersTableProps {
    activeUser: UserWithPermission;
    initialUsers: UserWithStats[];
    roles: {
        id: number;
        name: string;
    }[];
    banReasons: {
        id: number;
        reason: string;
    }[];
    permissions: ({
        permission: {
            name: string;
            id: number;
        };
    } & {
        roleId: number;
        permissionId: number;
    })[];
}

export async function getUserById(userId: string) {
    return await prisma.user.findUnique({
        where: {id: userId},
        select: {
            id: true,
            name: true,
            email: true,
            hashedPassword: true,
            package: true,
            verified: true,
            roleId: true,
            createdAt: true,
            updatedAt: true,
            stripeAccountId: true,
            hasSeenTutorial: true,
            banReason: {
                select: {
                    id: true,
                    reason: true,
                    createdAt: true,
                    updatedAt: true,
                },
            },
            UserSettings: {
                select: {
                    twoFactorEnabled: true,
                },
                take: 1,
            },
            role: {
                include: {
                    permissions: {
                        select: {
                            permission: {
                                select: {
                                    id: true,
                                    name: true,
                                },
                            },
                        },
                    },
                },
            },
            _count: {
                select: {
                    videos: true,
                    Image: true,
                },
            },
        },
    });
}

export async function getUserByEmail(email: string) {
    return await prisma.user.findUnique({
        where: {email},
        select: {
            id: true,
            name: true,
            email: true,
            hashedPassword: true,
            package: true,
            verified: true,
        },
    });
}

export async function isUserBanned(userId: string) {
    const user = await prisma.user.findUnique({
        where: {id: userId},
        select: {
            banReason: {
                select: {
                    reason: true,
                },
            },
        },
    });

    return !!user?.banReason[0]?.reason;
}

export async function getUserProfile(userId: string) {
    return await prisma.user.findUnique({
        where: {id: userId},
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
            createdAt: true,
            verified: true,
            instagram: true,
            twitch: true,
            twitter: true,
            website: true,
            youtube: true,
            stripeAccountId: true,
            UserSettings: {
                select: {
                    twoFactorEnabled: true,
                },
            },
            banReason: {
                select: {
                    reason: true,
                },
            },
        },
    });
}

export async function verifyUser(email: string): Promise<boolean> {
    const user = await getUserByEmail(email);
    return user != null;
}

export async function verifyPassword(
    userId: string,
    password: string,
): Promise<boolean> {
    const user = await getUserById(userId);
    if (!user?.hashedPassword) return false;
    return await compare(password, user.hashedPassword);
}

export async function isEmailTaken(email: string) {
    const user = await prisma.user.findUnique({
        where: {email},
        select: {id: true},
    });
    return !!user;
}

export async function isUsernameTaken(name: string) {
    const user = await prisma.user.findFirst({
        where: {name},
        select: {id: true},
    });
    return !!user;
}

export async function emailExists(email: string) {
    const user = await prisma.user.findUnique({
        where: {email},
        select: {id: true},
    });
    return !!user;
}

export async function createUser(
    email: string,
    password: string,
    name: string,
) {
    const hashedPassword = await hash(password, 10);

    const userSecret = encryptData(v4());
    if (!userSecret) {
        return;
    }

    return await prisma.user.create({
        data: {
            email,
            hashedPassword,
            name,
            package: "FREE",
            UserSettings: {
                create: {
                    twoFactorEnabled: false,
                    secretToken: userSecret.encryptedData,
                    secretTokenIv: userSecret.iv,
                },
            },
        },
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
        },
    });
}

export async function updateUserProfile(
    userId: string,
    data: {
        name?: string;
        image?: string;
        package?: "FREE" | "PRO" | "CREATOR";
    },
) {
    return await prisma.user.update({
        where: {id: userId},
        data,
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
        },
    });
}

export const verifyUserAccount = async (userId: string) => {
    await prisma.user.update({
        where: {
            id: userId,
        },
        data: {
            verified: true,
        },
    });
};

export async function updateUserPassword(userId: string, newPassword: string) {
    const user = await getUserById(userId);
    if (!user) return false;

    const hashedPassword = await hash(newPassword, 10);
    await prisma.user.update({
        where: {id: userId},
        data: {hashedPassword},
    });
    return true;
}

export async function deleteUserAccount(userId: string) {
    await prisma.video.deleteMany({
        where: {userId},
    });

    return await prisma.user.delete({
        where: {id: userId},
    });
}

export async function updateUsername(
    userId: string,
    name: string,
    socialLinks?: {
        instagram?: string;
        twitch?: string;
        twitter?: string;
        website?: string;
        youtube?: string;
    },
) {
    await prisma.user.update({
        where: {
            id: userId,
        },
        data: {
            name: name,
            ...(socialLinks && {
                instagram: socialLinks.instagram,
                twitch: socialLinks.twitch,
                twitter: socialLinks.twitter,
                website: socialLinks.website,
                youtube: socialLinks.youtube,
            }),
        },
    });
}

export async function getAllUsers(search?: string) {
    const where = search
        ? {
            OR: [
                {email: {contains: search, mode: "insensitive"}},
                {name: {contains: search, mode: "insensitive"}},
            ],
        }
        : {};

    return await prisma.user.findMany({
        where,
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
            verified: true,
            createdAt: true,
            updatedAt: true,
            UserSettings: {
                select: {
                    twoFactorEnabled: true,
                },
            },
            _count: {
                select: {
                    videos: true,
                },
            },
            videos: {
                select: {
                    views: true,
                },
            },
        },
        orderBy: {
            createdAt: "desc",
        },
    });
}

export async function getBillingUser(customerId: string) {
    return await prisma.user.findFirst({
        where: {
            stripeAccountId: customerId,
        },
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
            stripeAccountId: true,
        },
    });
}

export async function getBillingUserById(userId: string) {
    return await prisma.user.findFirst({
        where: {
            id: userId,
        },
        select: {
            id: true,
            name: true,
            email: true,
            package: true,
            stripeAccountId: true,
        },
    });
}

export async function revokeSubscription(userId: string) {
    return await prisma.user.update({
        where: {
            id: userId,
        },
        data: {
            package: "FREE",
            stripeAccountId: null,
        },
    });
}