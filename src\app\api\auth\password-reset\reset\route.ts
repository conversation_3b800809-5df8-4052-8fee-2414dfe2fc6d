import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function POST(request: NextRequest) {
    const {email, requestId, password} = await request.json();
    if (!email || !requestId || !password) {
        return NextResponse.json({
            status: HttpStatusCode.UnprocessableEntity,
            message: "No data provided (Email, Request ID, Password)"
        });
    }

    const response = await fetch(process.env.VIDEO_API_URL + "/password-reset/reset", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
            requestId,
            password,
        })
    });

    const data = await response.json();
    if (!response.ok) {
        return NextResponse.json({status: HttpStatusCode.BadRequest, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok});
}