"use client";

import { useState, useRef, useEffect } from "react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Filter, MessageCircle, Check } from "lucide-react";
import { NewTicketDialog } from "../_components/new-ticket-dialog";
import { DashboardWrapper } from "../_components/dashboard-wrapper";
import { DashboardHeader } from "../_components/dashboard-header";
import { $Enums } from "@prisma/client";

interface DashboardTicketsClientProps {
  userName: string;
  userId: string;
  userImage?: string | null;
  notifications: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  hasAccessToAdmin?: boolean;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "CLOSED":
      return "bg-gray-500/20 text-gray-300";
    case "IN_PROGRESS":
      return "bg-blue-500/20 text-blue-300";
    case "RESOLVED":
      return "bg-green-500/20 text-green-300";
    case "OPEN":
    default:
      return "bg-orange-500/20 text-orange-300";
  }
};

export default function DashboardTicketsClient({
  userName,
  userId,
  userImage,
  notifications,
  hasAccessToAdmin,
}: DashboardTicketsClientProps) {
  const [tickets, setTickets] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    ALL: true,
    OPEN: false,
    IN_PROGRESS: false,
    ON_HOLD: false,
    CLOSED: false,
  });
  useEffect(() => {
    if (tickets.length == 0) {
      fetch("/api/tickets/all", {
        method: "GET",
      }).then(response => response.json()).then((data) => {
        if (data.status >= 200 && data.status < 300) {
          setTickets(data.data);
        }
      });
    }
  }, [tickets]);

  const filterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target as Node)
      ) {
        setFilterOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleFilterChange = (filterType: string) => {
    if (filterType === "ALL") {
      setSelectedFilters({
        ALL: true,
        OPEN: false,
        IN_PROGRESS: false,
        ON_HOLD: false,
        CLOSED: false,
      });
    } else {
      setSelectedFilters((prev) => {
        const newFilters = {
          ...prev,
          ALL: false,
          [filterType]: !prev[filterType as keyof typeof prev],
        };

        const hasAnySpecificFilter =
          newFilters.OPEN ||
          newFilters.IN_PROGRESS ||
          newFilters.ON_HOLD ||
          newFilters.CLOSED;
        if (!hasAnySpecificFilter) {
          newFilters.ALL = true;
        }

        return newFilters;
      });
    }
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch = ticket.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());

    if (selectedFilters.ALL) return matchesSearch;

    const statusMatches =
      (selectedFilters.OPEN && ticket.status === "OPEN") ||
      (selectedFilters.IN_PROGRESS && ticket.status === "IN_PROGRESS") ||
      (selectedFilters.ON_HOLD && ticket.status === "ON_HOLD") ||
      (selectedFilters.CLOSED && ticket.status === "CLOSED");

    return matchesSearch && statusMatches;
  });

  return (
    <DashboardWrapper
      userName={userName}
      userImage={userImage}
      notifications={notifications}
      className="font-['Montserrat']"
    >
      <DashboardHeader
        userName={userName}
        userImage={userImage}
        notifications={notifications}
        hasAccessToAdmin={hasAccessToAdmin}
        title="Support & Feedback"
        description="Questions? We're ready to help."
      />

      <div className="space-y-12">
        {/* Search and Actions Bar */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="flex items-center gap-6">
            {/* Search Bar */}
            <div className="relative flex items-center z-10">
              <div className="flex items-center gap-4 bg-white/2 border border-white/16 rounded-full px-6 py-3 w-full lg:w-[525px] relative z-10">
                <Search className="h-5 w-5 text-white/70 flex-shrink-0 z-20" />
                <Input
                  placeholder="Search for something"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-transparent border-0 text-white/90 placeholder:text-white/60 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto font-['Montserrat'] font-medium relative z-20 flex-1"
                  style={{ color: "#ffffff" }}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-5">
            <NewTicketDialog />

            <div className="relative" ref={filterRef}>
              <Button
                onClick={() => setFilterOpen(!filterOpen)}
                className="bg-white/8 border border-white/24 text-white hover:bg-white/12 rounded-full px-2.5 py-3.5 h-auto font-['Montserrat'] font-semibold text-lg"
              >
                <Filter className="h-4 w-4 mr-1" />
                Filter
              </Button>

              {/* Custom Filter Popup */}
              {filterOpen && (
                <div className="absolute top-full right-0 mt-2 w-[159px] bg-black/80 border border-white/24 rounded-lg p-3.5 z-50 font-['Montserrat']">
                  <div className="flex flex-col gap-3">
                    {/* All Ticket */}
                    <div className="flex items-center gap-2.5">
                      <div
                        onClick={() => handleFilterChange("ALL")}
                        className={`w-5 h-5 border border-white/30 rounded cursor-pointer flex items-center justify-center transition-all ${
                          selectedFilters.ALL
                            ? "bg-gradient-to-br from-[#B851E0] to-[#EB489B] border-[#B851E0]"
                            : "bg-gradient-to-br from-white/10 to-transparent"
                        }`}
                      >
                        {selectedFilters.ALL && (
                          <Check
                            className="h-3 w-3 text-white"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                      <span className="text-white text-lg font-normal leading-[160%]">
                        All Ticket
                      </span>
                    </div>

                    <div className="w-full h-px bg-white/10"></div>

                    {/* Open */}
                    <div className="flex items-center gap-2.5">
                      <div
                        onClick={() => handleFilterChange("OPEN")}
                        className={`w-5 h-5 border border-white/30 rounded cursor-pointer flex items-center justify-center transition-all ${
                          selectedFilters.OPEN
                            ? "bg-gradient-to-br from-[#B851E0] to-[#EB489B] border-[#B851E0]"
                            : "bg-gradient-to-br from-white/10 to-transparent"
                        }`}
                      >
                        {selectedFilters.OPEN && (
                          <Check
                            className="h-3 w-3 text-white"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                      <span className="text-white text-lg font-normal leading-[160%]">
                        Open
                      </span>
                    </div>

                    <div className="w-full h-px bg-white/10"></div>

                    {/* In Progress */}
                    <div className="flex items-center gap-2.5">
                      <div
                        onClick={() => handleFilterChange("IN_PROGRESS")}
                        className={`w-5 h-5 border border-white/30 rounded cursor-pointer flex items-center justify-center transition-all ${
                          selectedFilters.IN_PROGRESS
                            ? "bg-gradient-to-br from-[#B851E0] to-[#EB489B] border-[#B851E0]"
                            : "bg-gradient-to-br from-white/10 to-transparent"
                        }`}
                      >
                        {selectedFilters.IN_PROGRESS && (
                          <Check
                            className="h-3 w-3 text-white"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                      <span className="text-white text-lg font-normal leading-[160%]">
                        In Progress
                      </span>
                    </div>

                    <div className="w-full h-px bg-white/10"></div>

                    {/* On Hold */}
                    <div className="flex items-center gap-2.5">
                      <div
                        onClick={() => handleFilterChange("ON_HOLD")}
                        className={`w-5 h-5 border border-white/30 rounded cursor-pointer flex items-center justify-center transition-all ${
                          selectedFilters.ON_HOLD
                            ? "bg-gradient-to-br from-[#B851E0] to-[#EB489B] border-[#B851E0]"
                            : "bg-gradient-to-br from-white/10 to-transparent"
                        }`}
                      >
                        {selectedFilters.ON_HOLD && (
                          <Check
                            className="h-3 w-3 text-white"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                      <span className="text-white text-lg font-normal leading-[160%]">
                        On Hold
                      </span>
                    </div>

                    <div className="w-full h-px bg-white/10"></div>

                    {/* Closed */}
                    <div className="flex items-center gap-2.5">
                      <div
                        onClick={() => handleFilterChange("CLOSED")}
                        className={`w-5 h-5 border border-white/30 rounded cursor-pointer flex items-center justify-center transition-all ${
                          selectedFilters.CLOSED
                            ? "bg-gradient-to-br from-[#B851E0] to-[#EB489B] border-[#B851E0]"
                            : "bg-gradient-to-br from-white/10 to-transparent"
                        }`}
                      >
                        {selectedFilters.CLOSED && (
                          <Check
                            className="h-3 w-3 text-white"
                            strokeWidth={2}
                          />
                        )}
                      </div>
                      <span className="text-white text-lg font-normal leading-[160%]">
                        Closed
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tickets Table */}
        {filteredTickets.length === 0 ? (
          <div className="bg-white/2 border border-white/16 rounded-2xl p-12 text-center min-h-[400px] flex flex-col items-center justify-center">
            <div className="mx-auto mb-6 w-20 h-20 rounded-full bg-white/5 border border-white/10 flex items-center justify-center">
              <MessageCircle className="h-10 w-10 text-white/40" />
            </div>
            <h3 className="text-2xl font-semibold text-white mb-3 font-['Montserrat']">
              {searchQuery || !selectedFilters.ALL
                ? "No tickets found"
                : "No tickets yet"}
            </h3>
            <p className="text-white/70 mb-8 text-lg font-['Montserrat'] max-w-md">
              {searchQuery || !selectedFilters.ALL
                ? "Try adjusting your search or filter"
                : "Create your first support ticket to get help"}
            </p>
            {!searchQuery && selectedFilters.ALL && <NewTicketDialog />}
          </div>
        ) : (
          <div className="bg-white/2 border border-white/2 rounded-2xl overflow-hidden">
            {/* Table Header */}
            <div className="bg-[#110018] border-b border-white/16 px-8 py-6">
              <div className="grid grid-cols-8 gap-6 items-center">
                <div className="text-white font-['Montserrat'] text-lg">
                  S.no
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Ticket Id
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Name
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Email
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Subject
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Category
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Status
                </div>
                <div className="text-white font-['Montserrat'] text-lg">
                  Created
                </div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-white/8">
              {filteredTickets.map((ticket, index) => (
                <Link
                  key={ticket.id}
                  href={`/dashboard/tickets/${ticket.id}`}
                  className="block hover:bg-white/5 transition-colors duration-200"
                >
                  <div className="px-8 py-6">
                    <div className="grid grid-cols-8 gap-6 items-center">
                      <div className="text-white/70 font-['Montserrat'] text-lg">
                        {String(index + 1).padStart(2, "0")}
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg">
                        #{ticket.id.slice(-7)}
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg truncate">
                        {ticket.user?.name || "Anonymous"}
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg truncate">
                        {ticket.user?.email || "N/A"}
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg truncate">
                        {ticket.title}
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg">
                        {ticket.category || "General"}
                      </div>
                      <div>
                        <span
                          className={`px-4 py-1 rounded-full text-sm font-['Montserrat'] ${getStatusColor(ticket.status)}`}
                        >
                          {ticket.status === "IN_PROGRESS"
                            ? "In progress"
                            : ticket.status === "ON_HOLD"
                              ? "On Hold"
                              : ticket.status.toLowerCase()}
                        </span>
                      </div>
                      <div className="text-white/70 font-['Montserrat'] text-lg">
                        {formatDistanceToNow(new Date(ticket.createdAt), {
                          addSuffix: true,
                        }).replace("about ", "")}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </DashboardWrapper>
  );
}