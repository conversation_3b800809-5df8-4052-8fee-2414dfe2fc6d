import { getImageDataByShortId, requestImage } from "@/server/image";
import { getUserProfilePicture } from "src/server/profile";
import ImagePageClient from "./client";
import { redirect } from "next/navigation";
import type { Metadata, ResolvingMetadata } from "next";

export async function generateMetadata(props: { params: Promise<{ id: string }> }, parent: ResolvingMetadata): Promise<Metadata> {
  const params = await props.params;
  const imageData = await getImageDataByShortId(params.id);
  
  if (!imageData) {
    return {
      title: "Image Unavailable | StreamBliss",
      description: "This image is currently unavailable or has been removed.",
    }
  }

  const imageUrl = await requestImage(imageData.userId, imageData.id);
  const previousImages = (await parent).openGraph?.images || [];
  const pageUrl = `${process.env.NEXT_PUBLIC_APP_URL || "https://streambliss.cloud"}/i/${params.id}`;
  const uploaderName = imageData.user.name || imageData.user.email.split("@")[0];

  const displayName = imageData.name || "";

  return {
    title: displayName || "Image on StreamBliss",
    description: `View image on StreamBliss`,
    openGraph: {
      title: displayName || "Image on StreamBliss",
      description: `View image on StreamBliss`,
      url: pageUrl,
      siteName: "StreamBliss",
      images: [
        {
          url: imageUrl,
          alt: displayName || "Image on StreamBliss",
        },
        ...previousImages,
      ],
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: displayName || "Image on StreamBliss",
      description: `View image on StreamBliss`,
      images: [imageUrl],
      creator: "@streambliss",
    },
    other: {
      "og:image": imageUrl,
      "og:image:secure_url": imageUrl,
      "og:site_name": "StreamBliss",
      "og:locale": "en_US",
      "og:url": pageUrl,
      "og:type": "website",

      "twitter:image": imageUrl,
      "twitter:domain": new URL(process.env.NEXT_PUBLIC_APP_URL || "https://streambliss.cloud").hostname,
      "twitter:url": pageUrl,
      "twitter:card": "summary_large_image",
      
      "theme-color": "#7C3AED",
      "image:release_date": imageData.createdAt.toISOString(),
      "image:creator": uploaderName,
    },
  }
}

export default async function ImagePage(props: { params: Promise<{ id: string }>}) {
  const params = await props.params;
  const imageData = await getImageDataByShortId(params.id);
  if (imageData == null) {
    return redirect("/404");
  }

  const url = await requestImage(imageData.userId, imageData.id);
  const userAvatar = imageData.user.id ? await getUserProfilePicture(imageData.user.id) : undefined;

  return <ImagePageClient 
    data={{
      ...imageData,
      user: {
        id: imageData.user.id,
        name: imageData.user.name,
        avatar: userAvatar
      }
    }}
    url={url}
  />;
}