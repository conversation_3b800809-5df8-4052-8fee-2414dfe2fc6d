"use client";

import React, {
  useState,
  useRef,
  useEffect,
  Dispatch,
  SetStateAction,
} from "react";
import { Input } from "@/components/ui/input";

interface OTPInputProps {
  length: number;
  onComplete: (otp: string) => void;
  value?: string;
  onChange?: Dispatch<SetStateAction<string>>;
  disabled?: boolean;
}

export function OTPInput({
  length,
  onComplete,
  value,
  onChange,
  disabled = false,
}: OTPInputProps) {
  const isControlled =
    typeof value === "string" && typeof onChange === "function";
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(""));
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  useEffect(() => {
    if (isControlled && value !== undefined) {
      const arr = value.split("").slice(0, length);
      setOtp([...arr, ...Array(length - arr.length).fill("")]);
    }
  }, [value, length, isControlled]);

  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleChange = (index: number, val: string) => {
    const newOtp = [...otp];
    newOtp[index] = val.slice(0, 1);
    if (isControlled && onChange) {
      const joined = newOtp.join("");
      onChange(joined);
    } else {
      setOtp(newOtp);
    }

    if (val && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    if (newOtp.every((digit) => digit !== "")) {
      onComplete(newOtp.join(""));
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData
      .getData("text")
      .slice(0, length)
      .split("");
    const newOtp = [...otp];
    pastedData.forEach((value, index) => {
      newOtp[index] = value;
      if (inputRefs.current[index]) {
        inputRefs.current[index]!.value = value;
      }
    });
    if (isControlled && onChange) {
      onChange(newOtp.join(""));
    } else {
      setOtp(newOtp);
    }
    if (inputRefs.current[pastedData.length]) {
      inputRefs.current[pastedData.length]?.focus();
    }
    if (newOtp.every((digit) => digit !== "")) {
      onComplete(newOtp.join(""));
    }
  };

  return (
    <div className="flex justify-center gap-2">
      {otp.map((digit, index) => (
        <Input
          key={index}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={digit}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          ref={(el) => {
            inputRefs.current[index] = el;
          }}
          className={`w-12 h-12 text-center text-2xl bg-white/10 border border-white/20 text-white rounded-xl transition-all duration-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 ${
            disabled ? "opacity-50 cursor-not-allowed" : "hover:bg-white/15"
          }`}
          disabled={disabled}
        />
      ))}
    </div>
  );
}