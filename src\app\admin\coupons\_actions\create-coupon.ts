"use server";

import {getClientIp} from "@/server/geolocation";
import {rateLimiter} from "@/lib/rate-limit";
import {getUserSession} from "@/server/session";
import {getUserById} from "@/lib/db/user";
import {hasPermission} from "@/server/admin";
import {createCoupon, PromotionCode} from "@/server/coupons";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
  coupon?: PromotionCode;
};

type Props = {
    code: string;
    discount: string;
    type: "percentage" | "fixed";
    validUntil: string;
    maxUses: string;
};

export default async function submitCreateCoupon({
                                                     code,
                                                     discount,
                                                     type,
                                                     validUntil,
                                                     maxUses,
                                                 }: Props): Promise<Callback> {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
        return {
            success: false,
            message: "You have exceeded the rate limit. Please try again later.",
        };
    }

    try {
        if (!code || !discount || !type || !validUntil || !maxUses) {
            return {
                success: false,
                message:
                    "Please provide a valid coupon code, discount, type, valid until, and max uses.",
            };
        }

        const userSession = await getUserSession();
        if (!userSession) {
            return {
                success: false,
                message: "Not authenticated",
            };
        }

        const userId = userSession.userId;
        const user = await getUserById(userId);
        if (!user) {
            return {
                success: false,
                message: "User not found. Please log in again.",
            };
        }

        if (!(await hasPermission(user.roleId, ["COUPONS_MANAGE"]))) {
            return {
                success: false,
                message: "You do not have permission to manage coupons.",
            };
        }

        const percent_off =
            type === "percentage" ? parseFloat(discount) : undefined;
        const amount_off =
            type === "fixed" ? Math.round(parseFloat(discount) * 100) : undefined;
        const currency = type === "fixed" ? "usd" : undefined;

        const redeem_by = Math.floor(new Date(validUntil).getTime() / 1000);
        const max_redemptions = parseInt(maxUses);

        const coupon = await createCoupon({
            code,
            percent_off,
            amount_off,
            currency,
            duration: "once",
            redeem_by,
            max_redemptions,
        });

        if (!coupon || !coupon.id) {
            return {
                success: false,
                message: "Failed to create coupon.",
            };
        }

        await createLog(user.id, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_COUPON_CREATED, LogActions.COUPON);

        return {
            success: true,
            message: "Coupon created successfully.",
            coupon: coupon,
        };
    } catch (error: any) {
        console.error("Error creating coupon:", error);
        return {
            success: false,
            message: "An unexpected error occurred. Please try again.",
        };
    }
}
