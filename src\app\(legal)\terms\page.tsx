"use client";

import { motion } from "framer-motion";
import { useEffect } from "react";
import {
  ScrollText,
  Shield,
  Scale,
  AlertTriangle,
  Mail,
  FileText,
  CheckCircle,
  Users,
  Gavel,
  ArrowRight,
} from "lucide-react";
import AOS from "aos";
import "aos/dist/aos.css";

const sections = [
  {
    icon: ScrollText,
    title: "1. Acceptance of Terms",
    gradient: "from-blue-400 to-cyan-500",
    subSections: [
      {
        title: "Agreement to Terms",
        content:
          "By accessing or using the StreamBliss service ('Service'), you agree to be bound by these Terms of Service ('Terms'). If you do not agree to these Terms, you may not use the Service.",
      },
      {
        title: "Changes to Terms",
        content:
          "We may update these Terms from time to time, and will notify you of significant changes. Your continued use of the Service after such changes constitutes your acceptance of the new Terms.",
      },
    ],
  },
  {
    icon: Shield,
    title: "2. User Accounts and Content",
    gradient: "from-emerald-400 to-teal-500",
    subSections: [
      {
        title: "Account Responsibility",
        content:
          "You are responsible for safeguarding your account password and for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.",
      },
      {
        title: "User Content",
        content:
          "You retain ownership of the content you upload to the Service ('User Content'). By uploading User Content, you grant us a worldwide, non-exclusive, royalty-free license to use, reproduce, distribute, and display the content in connection with providing the Service.",
      },
      {
        title: "Prohibited Content",
        content:
          "You may not upload content that is illegal, infringing, hateful, or obscene. This includes content that violates copyright, promotes terrorism, or contains malware. We reserve the right to remove any content that violates these Terms without notice.",
      },
    ],
  },
  {
    icon: Scale,
    title: "3. Service Usage and Limitations",
    gradient: "from-purple-400 to-pink-500",
    subSections: [
      {
        title: "Acceptable Use",
        content:
          "You agree not to misuse the Service. This includes interfering with the Service's operation, accessing it using unauthorized methods, or using it for any illegal purpose. We may investigate and take action against any violations.",
      },
      {
        title: "Service Availability",
        content:
          "We strive to keep the Service operational, but we do not guarantee uninterrupted access. We may suspend or terminate the Service at our discretion, without liability for any resulting loss.",
      },
      {
        title: "Limitation of Liability",
        content:
          "To the fullest extent permitted by law, StreamBliss shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues.",
      },
    ],
  },
];

const highlights = [
  {
    icon: CheckCircle,
    title: "Your Rights",
    description: "You retain full ownership of your content",
    gradient: "from-green-400 to-emerald-500",
  },
  {
    icon: Users,
    title: "Fair Use",
    description: "Clear guidelines for acceptable platform usage",
    gradient: "from-blue-400 to-indigo-500",
  },
  {
    icon: Gavel,
    title: "Legal Protection",
    description: "Balanced terms that protect both users and platform",
    gradient: "from-purple-400 to-pink-500",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function TermsPage() {
  const today = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div className="space-y-16 md:space-y-20">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Legal Information
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Terms of Service
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4">
          Clear, fair terms that protect both you and our platform while
          fostering a creative community.
        </p>

        <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/5 border border-white/10 backdrop-blur-sm">
          <FileText className="h-4 w-4 text-purple-400 mr-2" />
          <span className="text-sm text-gray-400">Last updated: {today}</span>
        </div>
      </motion.div>

      {/* Important Notice */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 to-orange-500/10 rounded-2xl blur-xl" />
        <div className="relative backdrop-blur-xl bg-amber-500/5 border border-amber-500/20 rounded-2xl p-8 md:p-10">
          <div className="flex items-start gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-amber-500/20 to-orange-500/20 flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-amber-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-3 text-amber-200">
                Important Notice
              </h2>
              <p className="text-amber-100/90 text-lg leading-relaxed">
                Please read these terms carefully before using StreamBliss. By
                creating an account or using our services, you acknowledge that
                you have read, understood, and agree to be bound by these terms.
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Key Highlights */}
      <motion.div
        className="space-y-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Key Highlights
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            The most important things you should know about our terms
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {highlights.map((highlight, index) => (
            <motion.div
              key={index}
              className="group relative"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 text-center transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div
                  className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${highlight.gradient} bg-opacity-20 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}
                >
                  <highlight.icon className="h-7 w-7 text-white" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-white group-hover:text-gray-100 transition-colors">
                  {highlight.title}
                </h3>
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
                  {highlight.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Terms Sections */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {sections.map((section, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="group relative"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay={index * 50}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-8 md:p-10 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
              <div className="flex items-start gap-6">
                <div
                  className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${section.gradient} bg-opacity-20 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                >
                  <section.icon className="h-7 w-7 text-white" />
                </div>
                <div className="flex-1">
                  <h2 className="text-2xl md:text-3xl font-bold mb-6 text-white group-hover:text-gray-100 transition-colors">
                    {section.title}
                  </h2>
                  <div className="space-y-6">
                    {section.subSections &&
                      section.subSections.map((sub, i) => (
                        <div
                          key={i}
                          className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                        >
                          <h3 className="font-semibold text-white mb-3 text-lg flex items-center">
                            <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mr-3" />
                            {sub.title}
                          </h3>
                          <p className="text-gray-300 leading-relaxed pl-5">
                            {sub.content}
                          </p>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Contact Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 mx-auto mb-6">
              <Mail className="h-8 w-8 text-blue-400" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Questions About These Terms?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Our legal team is here to help clarify any questions you may have
              about our Terms of Service.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25"
              >
                <Mail className="mr-2 h-5 w-5" />
                Contact Legal Team
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
              <span className="text-gray-400 font-medium">
                <EMAIL>
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}