"use client";

import React, { useState } from "react";
import { BarChart3 } from "lucide-react";
import { motion } from "framer-motion";
import { format } from "date-fns";

interface OverviewChartProps {
  video: {
    views: number;
    createdAt: string | Date;
  };
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function OverviewChart({ video }: OverviewChartProps) {
  const [chartHoverData, setChartHoverData] = useState<{
    x: number;
    y: number;
    views: number;
  } | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<
    "7d" | "30d" | "90d"
  >("30d");

  return (
    <motion.div variants={itemVariants} className="flex-1">
      <div className="w-full max-w-[900px] h-[350px] md:h-[468px] rounded-xl border border-white/12 bg-[#110018] relative">
        {/* Header */}
        <div className="flex items-center justify-between absolute left-4 md:left-6 top-4 md:top-6 right-4 md:right-6 h-[28px] md:h-[38px]">
          <div className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat'] flex items-center gap-2 md:gap-4">
            <BarChart3 className="w-5 md:w-6 h-5 md:h-6 text-[#B851E0]" />
            Overview
          </div>
          <div className="text-white font-bold text-sm md:text-2xl leading-[160%] font-['Montserrat'] flex items-center gap-2">
            {video.views} view{video.views !== 1 ? "s" : ""}
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="absolute right-4 md:right-6 top-12 md:top-16 flex gap-1">
          {["7d", "30d", "90d"].map((range) => (
            <button
              key={range}
              onClick={() =>
                setSelectedTimeRange(range as "7d" | "30d" | "90d")
              }
              className={`px-2 py-1 text-xs rounded transition-all duration-200 ${
                selectedTimeRange === range
                  ? "bg-[#B851E0] text-white"
                  : "bg-white/5 text-white/60 hover:bg-white/10 hover:text-white/80"
              }`}
            >
              {range}
            </button>
          ))}
        </div>

        {/* Chart Area */}
        <div className="absolute left-3 md:left-7 top-[60px] md:top-[119px] right-3 md:right-7 h-[180px] md:h-[301px]">
          {/* Grid Lines */}
          <div className="absolute w-full h-full">
            {[0, 1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="w-full h-0 opacity-20 bg-white/70 absolute left-0"
                style={{ top: `${i * 36}px` }}
              />
            ))}
            <div className="w-full h-0 opacity-10 bg-white/10 absolute left-0 bottom-0" />
          </div>

          {/* Interactive Chart Line */}
          <svg
            className="w-full h-full absolute left-0 top-0 cursor-crosshair"
            viewBox="0 0 792 302"
            fill="none"
            preserveAspectRatio="none"
            onMouseMove={(e) => {
              const rect = e.currentTarget.getBoundingClientRect();
              const relativeX = ((e.clientX - rect.left) / rect.width) * 100;
              const relativeY = ((e.clientY - rect.top) / rect.height) * 100;
              const views = Math.floor((relativeX / 100) * video.views);
              setChartHoverData({
                x: relativeX,
                y: relativeY,
                views: Math.max(0, views),
              });
            }}
            onMouseLeave={() => setChartHoverData(null)}
          >
            <path
              d="M1 301L791 1"
              stroke="#B851E0"
              strokeWidth="3"
              className="drop-shadow-sm"
            />
            <path
              d="M791 1L2.49902 301.5H791V1Z"
              fill="url(#paint0_linear_chart)"
              className="opacity-80 hover:opacity-90 transition-opacity duration-200"
            />

            {/* Interactive hover line */}
            {chartHoverData && (
              <>
                <line
                  x1={chartHoverData.x * (792 / 100)}
                  y1="0"
                  x2={chartHoverData.x * (792 / 100)}
                  y2="302"
                  stroke="#ffffff"
                  strokeWidth="1"
                  strokeDasharray="4,4"
                  opacity="0.6"
                />
                <circle
                  cx={chartHoverData.x * (792 / 100)}
                  cy={301 - (chartHoverData.x / 100) * 300}
                  r="4"
                  fill="#B851E0"
                  stroke="#ffffff"
                  strokeWidth="2"
                  className="drop-shadow-lg"
                />
              </>
            )}

            <defs>
              <linearGradient
                id="paint0_linear_chart"
                x1="393.502"
                y1="5"
                x2="393.502"
                y2="300"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#B851E0" />
                <stop offset="1" stopColor="#B851E0" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>

          {/* Hover Tooltip */}
          {chartHoverData && (
            <div
              className="absolute z-10 bg-black/90 text-white px-3 py-2 rounded-lg shadow-lg border border-white/20 pointer-events-none"
              style={{
                left: `${chartHoverData.x}%`,
                top: `${chartHoverData.y}%`,
                transform:
                  chartHoverData.x > 50
                    ? "translate(-100%, -100%)"
                    : "translate(10px, -100%)",
              }}
            >
              <div className="text-sm font-medium">
                {chartHoverData.views.toLocaleString()} views
              </div>
            </div>
          )}
        </div>

        {/* Chart Labels */}
        <div className="text-white font-normal text-xs md:text-sm leading-[160%] font-['Montserrat'] absolute left-3 md:left-7 bottom-2 md:bottom-4">
          {format(new Date(video.createdAt), "MMM dd")}
        </div>
        <div className="text-white font-normal text-xs md:text-sm leading-[160%] font-['Montserrat'] absolute left-3 md:left-6 bottom-6 md:bottom-10">
          0
        </div>
        <div className="text-white font-normal text-xs md:text-sm leading-[160%] font-['Montserrat'] absolute right-3 md:right-7 bottom-2 md:bottom-4">
          Today
        </div>
      </div>
    </motion.div>
  );
}