"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useInView } from "react-intersection-observer";
import { Play, Eye, Calendar, Clock, TrendingUp, Flame } from "lucide-react";
import { cn } from "@/lib/utils";
import { shouldDisableOptimization } from "@/lib/image-utils";
import type { Video } from "@/types/video";

interface OptimizedVideoGridProps {
  videos: Video[];
  className?: string;
  soundEnabled?: boolean;
}

type GridItemSize = "small" | "medium" | "large" | "hero";

interface GridVideo extends Video {
  gridSize: GridItemSize;
  priority: boolean;
}

const LOAD_MORE_THRESHOLD = 8;

const GRID_PATTERNS = [
  { size: "hero", colSpan: 2 },
  { size: "medium", colSpan: 1 },
  { size: "small", colSpan: 1 },
  { size: "small", colSpan: 1 },
  { size: "large", colSpan: 2 },
  { size: "small", colSpan: 1 },
  { size: "medium", colSpan: 1 },
  { size: "small", colSpan: 1 },
  { size: "large", colSpan: 2 },
  { size: "small", colSpan: 1 },
  { size: "medium", colSpan: 1 },
  { size: "small", colSpan: 1 },
];

function formatViews(views: number): string {
  if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
  if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
  return views.toString();
}

function formatDuration(duration?: number): string {
  if (!duration) return "0:00";
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
}

function VideoGridItem({
  video,
  index,
  soundEnabled,
}: {
  video: GridVideo;
  index: number;
  soundEnabled: boolean;
}) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
    rootMargin: "100px",
  });

  const pattern = GRID_PATTERNS[index % GRID_PATTERNS.length];

  const handleClick = useCallback(() => {
    router.push(`/v/${video.shortLink}`);
  }, [router, video.shortLink]);

  const isNew = useMemo(() => {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return new Date(video.createdAt) > oneDayAgo;
  }, [video.createdAt]);

  const isTrending = useMemo(() => {
    return (video.views || 0) > 1000 && isNew;
  }, [video.views, isNew]);

  const videoUrl = useMemo(() => {
    const API_URL =
      process.env.NEXT_PUBLIC_VIDEO_API_URL || "https://api.streambliss.cloud";
    return `${API_URL}/video/stream/${video.userId}/${video.id}`;
  }, [video.userId, video.id]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    hoverTimeoutRef.current = setTimeout(() => {
      if (videoRef.current && inView && videoLoaded) {
        videoRef.current.currentTime = 0;
        videoRef.current.play().catch(console.error);
        setIsPlaying(true);
      }
    }, 300);
  }, [inView, videoLoaded]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setIsPlaying(false);

    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      ref={ref}
      className={cn(
        "relative group cursor-pointer overflow-hidden rounded-xl bg-zinc-900/50 border border-zinc-800/50",
        "hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10",
        pattern.size === "hero" && "min-h-[320px]",
        pattern.size === "large" && "min-h-[280px]",
        pattern.size === "medium" && "min-h-[200px]",
        pattern.size === "small" && "min-h-[160px]",
      )}
      style={{
        gridColumn: `span ${pattern.colSpan}`,
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: (index % 6) * 0.1 }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    >
      {/* Thumbnail */}
      <div className="relative w-full h-full">
        {/* Video element for hover play */}
        {inView && (
          <video
            ref={videoRef}
            src={videoUrl}
            muted={!soundEnabled}
            loop
            preload="metadata"
            className={cn(
              "absolute inset-0 w-full h-full object-cover transition-opacity duration-300",
              isPlaying ? "opacity-100 z-10" : "opacity-0 -z-10",
            )}
            onLoadedData={() => setVideoLoaded(true)}
            onError={() => console.error("Video failed to load")}
          />
        )}

        {!imageLoaded && inView && !imageError && (
          <div className="w-full h-full bg-gradient-to-br from-zinc-800 to-zinc-900 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
          </div>
        )}

        {video.thumbnailUrl && !imageError && inView ? (
          <Image
            src={video.thumbnailUrl}
            alt={video.title}
            fill
            className={cn(
              "object-cover transition-all duration-500",
              isHovered && !isPlaying && "scale-105",
              imageLoaded
                ? isPlaying
                  ? "opacity-0"
                  : "opacity-100"
                : "opacity-0",
            )}
            unoptimized={shouldDisableOptimization(video.thumbnailUrl)}
            priority={video.priority}
            loading={video.priority ? "eager" : "lazy"}
            onLoad={() => setImageLoaded(true)}
            onError={(e) => {
              console.error("Thumbnail failed to load:", video.thumbnailUrl);
              setImageError(true);
            }}
            sizes={
              pattern.size === "hero"
                ? "50vw"
                : pattern.size === "large"
                  ? "33vw"
                  : pattern.size === "medium"
                    ? "25vw"
                    : "20vw"
            }
          />
        ) : !inView ? (
          <div className="w-full h-full bg-gradient-to-br from-zinc-800 to-zinc-900 flex items-center justify-center">
            <Play className="w-12 h-12 text-zinc-600" />
          </div>
        ) : imageError ? (
          <div className="w-full h-full bg-gradient-to-br from-zinc-800 to-zinc-900 flex items-center justify-center">
            <Play className="w-12 h-12 text-zinc-600" />
          </div>
        ) : null}

        {/* Overlay gradient */}
        <div
          className={cn(
            "absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent",
            "opacity-60 group-hover:opacity-80 transition-opacity duration-300",
          )}
        />

        {!isPlaying && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={
              isHovered ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
            }
            transition={{ duration: 0.2 }}
          >
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
              <Play className="w-8 h-8 text-white ml-1" />
            </div>
          </motion.div>
        )}

        {/* Top badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {isNew && (
            <div className="bg-green-500/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium text-white flex items-center gap-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              NEW
            </div>
          )}
          {isTrending && (
            <div className="bg-orange-500/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium text-white flex items-center gap-1">
              <TrendingUp className="w-3 h-3" />
              TRENDING
            </div>
          )}
        </div>

        {/* Duration badge */}
        {video.duration && (
          <div className="absolute top-3 right-3 bg-black/70 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium text-white">
            {formatDuration(video.duration)}
          </div>
        )}

        {/* Bottom content */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="space-y-2">
            {/* Title */}
            <h3
              className={cn(
                "font-semibold text-white leading-tight line-clamp-2",
                pattern.size === "hero"
                  ? "text-lg"
                  : pattern.size === "large"
                    ? "text-base"
                    : "text-sm",
              )}
            >
              {video.title}
            </h3>

            {/* Creator and stats */}
            {pattern.size !== "small" && (
              <div className="flex items-center justify-between text-xs text-zinc-300">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  {video.user?.name && (
                    <span className="truncate font-medium">
                      {video.user.name}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-3 flex-shrink-0">
                  {video.views !== undefined && video.views > 0 && (
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      <span>{formatViews(video.views)}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Extended stats for larger cards */}
            {(pattern.size === "hero" || pattern.size === "large") && (
              <div className="flex items-center gap-4 text-xs text-zinc-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  <span>{new Date(video.createdAt).toLocaleDateString()}</span>
                </div>
                {video.duration && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDuration(video.duration)}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export function OptimizedVideoGrid({
  videos,
  className,
  soundEnabled = true,
}: OptimizedVideoGridProps) {
  const [visibleCount, setVisibleCount] = useState(LOAD_MORE_THRESHOLD);
  const [columnCount, setColumnCount] = useState(4);

  useEffect(() => {
    const updateColumnCount = () => {
      const width = window.innerWidth;
      if (width < 640) setColumnCount(2);
      else if (width < 1024) setColumnCount(3);
      else if (width < 1536) setColumnCount(4);
      else setColumnCount(5);
    };

    updateColumnCount();
    window.addEventListener("resize", updateColumnCount);
    return () => window.removeEventListener("resize", updateColumnCount);
  }, []);

  const processedVideos = useMemo<GridVideo[]>(() => {
    return videos.slice(0, visibleCount).map((video, index) => {
      const pattern = GRID_PATTERNS[index % GRID_PATTERNS.length];
      return {
        ...video,
        gridSize: pattern.size as GridItemSize,
        priority: index < 6,
      };
    });
  }, [videos, visibleCount]);

  const [loadMoreRef, loadMoreInView] = useInView({
    threshold: 0.1,
  });

  useEffect(() => {
    if (loadMoreInView && visibleCount < videos.length) {
      setVisibleCount((prev) =>
        Math.min(prev + LOAD_MORE_THRESHOLD, videos.length),
      );
    }
  }, [loadMoreInView, visibleCount, videos.length]);

  if (videos.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center mb-6">
          <Play className="w-12 h-12 text-purple-400" />
        </div>
        <h3 className="text-2xl font-bold text-white mb-2">No videos found</h3>
        <p className="text-zinc-400">Check back later for fresh content</p>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Grid Container */}
      <div
        className="grid gap-4"
        style={{
          gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
          gridAutoRows: "auto",
          gridAutoFlow: "dense",
        }}
      >
        <AnimatePresence mode="popLayout">
          {processedVideos.map((video, index) => (
            <VideoGridItem
              key={video.id}
              video={video}
              index={index}
              soundEnabled={soundEnabled}
            />
          ))}
        </AnimatePresence>
      </div>

      {visibleCount < videos.length && (
        <div
          ref={loadMoreRef}
          className="h-20 flex items-center justify-center mt-8"
        >
          <div className="flex items-center gap-2 text-zinc-400">
            <div className="w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm">Loading more videos...</span>
          </div>
        </div>
      )}

      {/* End of content indicator */}
      {visibleCount >= videos.length && videos.length > LOAD_MORE_THRESHOLD && (
        <div className="text-center py-8 text-zinc-500">
          <Flame className="w-6 h-6 mx-auto mb-2 opacity-50" />
          <p className="text-sm">You&apos;ve seen all the latest videos!</p>
        </div>
      )}
    </div>
  );
}