import {NextResponse} from "next/server";

export async function POST(request: Request): Promise<Response> {
    const {email} = await request.json();
    if (!email) {
        return new Response('Invalid email', {status: 401});
    }

    const sendEmailRequest = await fetch(process.env.VIDEO_API_URL + '/email-verification/send-verification', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
        }),
    });

    const data = await sendEmailRequest.json();
    if (data && data.accepted) {
        if (data.accepted.includes(email)) {
            return NextResponse.json({status: 200});
        }
    }

    return NextResponse.json({status: 201});
}