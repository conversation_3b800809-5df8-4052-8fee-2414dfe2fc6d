"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { createNotification } from "./notifications";
import { NotificationType } from "@prisma/client";

export const subscribeChannel = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return;
    }

    if (await hasSubscribedChannel(userId, channelId)) {
      return;
    }

    await prisma.subscribe.create({
      data: {
        channelId: channelId,
        userId: userId,
      }
    });

  } catch (error) {
    console.error("Error subscribing to channel:", error);
  }
}

export const unSubscribeChannel = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return;
    }

    if (!await hasSubscribedChannel(userId, channelId)) {
      return;
    }

    await prisma.subscribe.deleteMany({
      where: {
        channelId: channelId,
        userId: userId,
      }
    });
  } catch (error) {
    console.error("Error unsubscribing from channel:", error);
  }
}

// Channel Id = Target User Id
export const hasSubscribedChannel = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return false;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return false;
    }

    const channelData = await prisma.subscribe.findFirst({
      where: {
        channelId: channelId,
        userId: userId,
      }
    });

    return !!channelData;
  } catch (error) {
    console.error("Error checking channel-subscription:", error);
    return false;
  }
}

export const getSubscriberCount = async (channelId: string): Promise<number> => {
  try {
    const channel = await getUserById(channelId);
    if (!channel) {
      return 0;
    }

    const subscriberCount = await prisma.subscribe.count({
      where: {
        channelId: channelId,
      }
    });

    return subscriberCount;
  } catch (error) {
    console.error("Error getting subscriber count:", error);
    return 0;
  }
}

export const enableChannelNotifications = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return;
    }

    const channelData = await prisma.subscribe.findFirst({
      where: {
        channelId: channelId,
        userId: userId,
      }
    });

    if (!channelData) {
      return;
    }

    await prisma.subscribe.update({
      where: {
        id: channelData.id,
      },
      data: {
        notification: true,
      }
    });
  } catch (error) {
    console.error("Error enabling channel notifications:", error);
  }
}

export const disableChannelNotifications = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return;
    }

    const channelData = await prisma.subscribe.findFirst({
      where: {
        channelId: channelId,
        userId: userId,
      }
    });

    if (!channelData) {
      return;
    }

    await prisma.subscribe.update({
      where: {
        id: channelData.id,
      },
      data: {
        notification: false,
      }
    });
  } catch (error) {
    console.error("Error disabling channel notifications:", error);
  }
}

export const hasChannelNotificationsEnabled = async (userId: string, channelId: string) => {
  try {
    const user = await getUserById(userId);
    if (!user) {
      return false;
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return false;
    }

    const channelData = await prisma.subscribe.findFirst({
      where: {
        channelId: channelId,
        userId: userId,
      }
    });

    if (!channelData) {
      return false;
    }

    return channelData.notification;
  } catch (error) {
    console.error("Error checking channel notifications:", error);
  }
}

export const sendNotificationToSubscribers = async (channelId: string, message: string) => {
  try {
    const channel = await getUserById(channelId);
    if (!channel) {
      return;
    }

    const subscribers = await prisma.subscribe.findMany({
      where: {
        channelId: channelId,
        notification: true,
      }
    });

    for (const subscriber of subscribers) {
      await createNotification(subscriber.userId, message, NotificationType.SUBSCRIBE);
    }
  } catch (error) {
    console.error("Error sending notification to subscribers:", error);
  }
}
