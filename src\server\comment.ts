"use server";

import { prisma } from "@/lib/prisma";
import { getVideoDataById } from "./video";
import { getUserById } from "@/lib/db/user";

export type Comment = { id: string, text: string, user: { id: string, name: string }, createdAt: Date, parentId: string|null, likes: number, isLiked: boolean, children: Comment[] };

export const getCommentById = async (commentId: string) => {
  return await prisma.comments.findUnique({
    where: {
      id: commentId
    }
  });
}

export const getCommentLikes = async (commentId: string) => {
  return await prisma.commentLikes.count({
    where: {
      commentId: commentId
    }
  });
}

export const hasUserLikedComment = async (userId: string, commentId: string) => {
  return await prisma.commentLikes.findFirst({
    where: {
      commentId: commentId,
      userId: userId
    }
  });
}

export const getCommentsByVideoId = async (videoId: string) => {
  const video = await getVideoDataById(videoId);
  if (!video) {
    return [];
  }

  const comments = [] as Comment[];
  const dbComments = await prisma.comments.findMany({ where: { videoId: videoId } });

  for (const comment of dbComments) {
    const user = await getUserById(comment.userId);
    if (user == null) continue;

    if (comment.parentId != null) continue;

    comments.push({
      id: comment.id,
      text: comment.comment,
      user: {
        id: user.id,
        name: user.name!,
      },
      parentId: comment.parentId,
      createdAt: comment.createdAt,
      likes: await getCommentLikes(comment.id),
      isLiked: (await hasUserLikedComment(video.userId, comment.id)) ? true : false,
      children: [],
    });
  }

  for (const comment of dbComments) {
    if (comment.parentId == null) continue;

    const parentComment = comments.find(c => c.id == comment.parentId);
    if (parentComment == null) continue;

    const user = await getUserById(comment.userId);
    if (user == null) continue;

    parentComment.children.push({
      id: comment.id,
      text: comment.comment,
      user: {
        id: user.id,
        name: user.name!,
      },
      createdAt: comment.createdAt,
      parentId: comment.parentId,
      likes: await getCommentLikes(comment.id),
      isLiked: (await hasUserLikedComment(video.userId, comment.id)) ? true : false,
      children: [],
    });
  }

  return comments;
};

export const getLikeData = async (userId: string, commentId: string) => {
  return await prisma.commentLikes.findFirst({
    where: {
      userId: userId,
      commentId: commentId
    }
  });
}

export const likeComment = async (userId: string, commentId: string) => {
  if (await hasUserLikedComment(userId, commentId)) return;
  
  await prisma.commentLikes.create({
    data: {
      userId: userId,
      commentId: commentId,
    }
  });
}

export const unlikeComment = async (userId: string, commentId: string) => {
  const likeData = await getLikeData(userId, commentId);
  if (likeData == null) return;

  await prisma.commentLikes.delete({
    where: {
      id: likeData.id,
      userId: userId,
      commentId: commentId
    }
  })
}

export const removeComment = async (commentId: string) => {
  await prisma.comments.delete({
    where: {
      id: commentId
    }
  });
}
