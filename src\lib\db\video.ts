import { prisma } from "@/lib/prisma"

export async function getUserVideos(userId: string) {
  return await prisma.video.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: "desc",
    },
  })
}

export async function updateVideoTitle(videoId: string, title: string) {
  return await prisma.video.update({
    where: {
      id: videoId,
    },
    data: {
      title,
    },
  })
}

export async function deleteVideo(videoId: string) {
  const response = await fetch(`/api/videos/${videoId}`, {
    method: "DELETE",
  })

  if (!response.ok) {
    throw new Error("Failed to delete video")
  }

  return true
}

export async function createVideo({
  title,
  url,
  thumbnailUrl,
  shortLink,
  userId,
}: {
  title: string
  url: string
  thumbnailUrl: string
  shortLink: string
  userId: string
}) {
  return await prisma.video.create({
    data: {
      title,
      url,
      thumbnailUrl,
      shortLink,
      userId,
    },
  })
}