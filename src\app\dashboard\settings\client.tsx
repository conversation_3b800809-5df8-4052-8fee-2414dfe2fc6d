"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import * as z from "zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { $Enums } from "@prisma/client";
import { Loader2, Eye, EyeOff } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { TwoFactorSetup } from "./_components/twofa-setup";
import { DashboardWrapper } from "../_components/dashboard-wrapper";
import { DashboardHeader } from "../_components/dashboard-header";
import { TutorialRestartButton } from "../_components/tutorial-restart-button";

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(6, {
      message: "Password must be at least 6 characters.",
    }),
    newPassword: z.string().min(6, {
      message: "Password must be at least 6 characters.",
    }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type PasswordFormValues = z.infer<typeof passwordFormSchema>;

interface SettingsPageClientProps {
  userId: string;
  twoFaEnabled: boolean;
  userName: string;
  userImage?: string | null;
  notifications: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  hasAccessToAdmin?: boolean;
}

export default function SettingsPageClient({
  userId,
  twoFaEnabled,
  userName,
  userImage,
  notifications,
  hasAccessToAdmin,
}: SettingsPageClientProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [twoFactor, setTwoFactor] = useState(twoFaEnabled);
  const [twoFaSetup, setTwoFaSetup] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formKey, setFormKey] = useState(Date.now());
  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  async function onSubmit(data: PasswordFormValues) {
    setIsLoading(true);

    try {
      const response = await fetch("/api/user/password/change", {
        method: "POST",
        body: JSON.stringify(data),
      })

      const _data = await response.json();
      if (!response.ok) {
        error("Password Update Failed", _data.message);
        return;
      }

      success("Password Updated", "Password has been updated");
    } catch (err) {
      error("Update Error", "Failed to update password");
    } finally {
      setIsLoading(false);
    }
  }

  // Handle form validation errors and show them as toasts
  const handleInvalidSubmit = (errors: any) => {
    if (errors.currentPassword) {
      error("Validation Error", errors.currentPassword.message);
      return;
    }
    if (errors.newPassword) {
      error("Validation Error", errors.newPassword.message);
      return;
    }
    if (errors.confirmPassword) {
      error("Validation Error", errors.confirmPassword.message);
      return;
    }
  };

  async function handleDeleteAccount() {
    setIsDeleting(true);
    try {
      const response = await fetch("/api/user/account/delete", {
        method: "POST"
      });

      const data = await response.json();
      if (!response.ok) {
        error("Delete Failed", data.message);
        return;
      }

      success("Account Deleted", "Account deleted successfully");

      router.push("/login");
    } catch (err) {
      error("Error", "Failed to delete account");
      setIsDeleting(false);
    }
  }

  const handleToggle = async (value: boolean) => {
    if (value === false && twoFaEnabled) {
      setTwoFactor(false);

      const response = await fetch("") // TODO: add disable 2FA api route
    } else if (value === true && !twoFaEnabled) {
      setTwoFaSetup(true);
    }
  };

  const onTwoFaSetupComplete = () => {
    setTwoFactor(true);
    setTwoFaSetup(false);
    success("2FA Enabled", "Two-factor authentication enabled successfully");
    router.refresh();
  };

  return (
    <DashboardWrapper
      userName={userName || "User"}
      userImage={userImage}
      notifications={notifications}
      className="font-['Montserrat']"
    >
      <DashboardHeader
        userName={userName || "User"}
        userImage={userImage}
        notifications={notifications}
        hasAccessToAdmin={hasAccessToAdmin}
        title="Account Setting"
        description="Manage your password, security settings, and account preferences all in one place."
      />

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-[32px] p-6 lg:p-[40px]">
        {/* Left Column - Change Password */}
        <div className="flex w-full lg:w-[846px] flex-col items-start gap-[32px] flex-shrink-0 rounded-[16px] border border-[rgba(255,255,255,0.12)] bg-[#110018] p-[32px]">
          <div className="text-white font-['Montserrat'] text-[24px] font-semibold leading-[160%] relative">
            Change Password
          </div>
          <div className="flex flex-col items-start gap-[32px] w-full">
            <div className="flex flex-col items-start gap-[16px] w-full">
              <Form {...form}>
                <form
                  key={formKey}
                  onSubmit={form.handleSubmit(onSubmit, handleInvalidSubmit)}
                  className="password-form flex flex-col justify-center items-start gap-[16px] w-full"
                >
                  {/* Current Password */}
                  <FormField
                    control={form.control}
                    name="currentPassword"
                    render={({ field }) => (
                      <FormItem className="flex flex-col items-start gap-[12px] w-full">
                        <div className="flex items-center gap-[7px]">
                          <FormLabel className="text-white font-['Montserrat'] text-[16px] font-medium leading-[160%]">
                            Current Password
                          </FormLabel>
                        </div>
                        <FormControl>
                          <div className="password-container flex w-full h-[56px] px-[20px] py-[10px] justify-center items-center gap-[12px] rounded-[16px] relative bg-white/5 hover:bg-white/8 focus-within:bg-white/10 transition-all duration-300">
                            <Input
                              type={showCurrentPassword ? "text" : "password"}
                              placeholder="************"
                              {...field}
                              className="flex-1 text-white font-['Montserrat'] text-[14px] font-medium leading-[160%] opacity-70 h-auto p-0 placeholder:text-white placeholder:opacity-70 bg-transparent border-none outline-none autofill:bg-transparent autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                              autoComplete="current-password"
                              style={{
                                background: "transparent !important",
                                backgroundColor: "transparent !important",
                                backgroundImage: "none !important",
                                boxShadow: "none !important",
                                WebkitBoxShadow:
                                  "inset 0 0 0px 1000px transparent !important",
                                border: "none !important",
                                outline: "none !important",
                                WebkitTextFillColor:
                                  "rgba(255, 255, 255, 0.7) !important",
                                color: "rgba(255, 255, 255, 0.7) !important",
                              }}
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setShowCurrentPassword(!showCurrentPassword)
                              }
                              className="opacity-70 hover:opacity-100 transition-opacity"
                            >
                              {showCurrentPassword ? (
                                <Eye className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              ) : (
                                <EyeOff className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {/* New Password */}
                  <FormField
                    control={form.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem className="flex flex-col items-start gap-[12px] w-full">
                        <div className="flex items-center gap-[7px]">
                          <FormLabel className="text-white font-['Montserrat'] text-[16px] font-medium leading-[160%]">
                            New Password
                          </FormLabel>
                        </div>
                        <FormControl>
                          <div className="password-container flex w-full h-[56px] px-[20px] py-[10px] justify-center items-center gap-[12px] rounded-[16px] relative bg-white/5 hover:bg-white/8 focus-within:bg-white/10 transition-all duration-300">
                            <Input
                              type={showNewPassword ? "text" : "password"}
                              placeholder="************"
                              {...field}
                              className="flex-1 text-white font-['Montserrat'] text-[14px] font-medium leading-[160%] opacity-70 h-auto p-0 placeholder:text-white placeholder:opacity-70 bg-transparent border-none outline-none autofill:bg-transparent autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                              autoComplete="new-password"
                              style={{
                                background: "transparent !important",
                                backgroundColor: "transparent !important",
                                backgroundImage: "none !important",
                                boxShadow: "none !important",
                                WebkitBoxShadow:
                                  "inset 0 0 0px 1000px transparent !important",
                                border: "none !important",
                                outline: "none !important",
                                WebkitTextFillColor:
                                  "rgba(255, 255, 255, 0.7) !important",
                                color: "rgba(255, 255, 255, 0.7) !important",
                              }}
                              data-password-field="true"
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setShowNewPassword(!showNewPassword)
                              }
                              className="opacity-70 hover:opacity-100 transition-opacity"
                            >
                              {showNewPassword ? (
                                <Eye className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              ) : (
                                <EyeOff className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {/* Confirm Password */}
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem className="flex flex-col items-start gap-[12px] w-full">
                        <div className="flex items-center gap-[7px]">
                          <FormLabel className="text-white font-['Montserrat'] text-[16px] font-medium leading-[160%]">
                            Confirm Password
                          </FormLabel>
                        </div>
                        <FormControl>
                          <div className="password-container flex w-full h-[56px] px-[20px] py-[10px] justify-center items-center gap-[12px] rounded-[16px] relative bg-white/5 hover:bg-white/8 focus-within:bg-white/10 transition-all duration-300">
                            <Input
                              type={showConfirmPassword ? "text" : "password"}
                              placeholder="************"
                              {...field}
                              className="flex-1 text-white font-['Montserrat'] text-[14px] font-medium leading-[160%] opacity-70 h-auto p-0 placeholder:text-white placeholder:opacity-70 bg-transparent border-none outline-none autofill:bg-transparent autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_transparent]"
                              autoComplete="new-password"
                              style={{
                                background: "transparent !important",
                                backgroundColor: "transparent !important",
                                backgroundImage: "none !important",
                                boxShadow: "none !important",
                                WebkitBoxShadow:
                                  "inset 0 0 0px 1000px transparent !important",
                                border: "none !important",
                                outline: "none !important",
                                WebkitTextFillColor:
                                  "rgba(255, 255, 255, 0.7) !important",
                                color: "rgba(255, 255, 255, 0.7) !important",
                              }}
                              data-password-field="true"
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                              className="opacity-70 hover:opacity-100 transition-opacity"
                            >
                              {showConfirmPassword ? (
                                <Eye className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              ) : (
                                <EyeOff className="w-[24px] h-[24px] flex-shrink-0 aspect-square text-white" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {/* Buttons */}
                  <div className="flex flex-col sm:flex-row items-start gap-[24px] mt-[16px]">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex h-[57px] px-[24px] py-[14px] justify-center items-center gap-[10px] rounded-[65px] bg-gradient-to-b from-[#B851E0] to-[#EB489B] relative text-white text-center font-['Montserrat'] text-[18px] font-semibold leading-[160%] hover:from-[#A641D0] hover:to-[#DA3A8B]"
                    >
                      {isLoading && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Change Password
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="flex h-[57px] px-[24px] py-[14px] justify-center items-center gap-[10px] rounded-[65px] border border-white opacity-70 bg-transparent relative text-white text-center font-['Montserrat'] text-[18px] font-semibold leading-[160%] hover:bg-white/10"
                    >
                      Forgot Password
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="inline-flex flex-col items-start gap-[24px] w-full lg:w-[846px]">
          {/* Two-Factor Authentication */}
          <div className="flex w-full min-h-[232px] px-[24px] py-[28px] flex-col items-start gap-[24px] rounded-[16px] border border-[rgba(255,255,255,0.12)] bg-[#110018]">
            <div className="flex flex-col items-start gap-[24px] w-full">
              <div className="flex flex-col items-start gap-[12px] w-full">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between w-full gap-4">
                  <div className="text-white font-['Montserrat'] text-[24px] font-semibold leading-[160%] relative">
                    Two-Factor Authentication
                  </div>
                  <div className="w-[68px] h-[33px] bg-[#1E1920] rounded-[16.5px] relative flex items-center justify-center">
                    <Switch
                      checked={twoFactor}
                      onCheckedChange={handleToggle}
                      className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                    />
                  </div>
                </div>
                <div className="w-full max-w-[622px] text-white font-['Montserrat'] text-[16px] font-normal leading-[160%] opacity-70 relative">
                  Add an extra layer of security to your account. Will ask for a
                  code in addition to your password when you sign in.
                </div>
              </div>
            </div>
          </div>

          {/* Help & Tutorial */}
          <div className="flex w-full px-[24px] py-[28px] flex-col items-start gap-[24px] rounded-[16px] border border-[rgba(255,255,255,0.12)] bg-[#110018]">
            <div className="flex flex-col items-start gap-[32px] w-full">
              <div className="text-white font-['Montserrat'] text-[20px] font-medium leading-[160%]">
                Help & Tutorial
              </div>
              <div className="flex flex-col items-start gap-[16px] w-full">
                <TutorialRestartButton />
              </div>
            </div>
          </div>

          {/* Danger Zone */}
          <div className="flex w-full min-h-[232px] px-[24px] py-[28px] flex-col items-start gap-[24px] rounded-[16px] border border-[rgba(255,255,255,0.12)] bg-[#110018]">
            <div className="flex flex-col items-start gap-[32px] w-full">
              <div className="text-white font-['Montserrat'] text-[20px] font-medium leading-[160%]">
                Danger Zone
              </div>
              <div className="flex flex-col items-start gap-[16px] w-full">
                <div className="text-[rgba(255,255,255,0.60)] font-['Montserrat'] text-[16px] font-normal leading-[160%]">
                  Delete your account permanently. This action cannot be undone.
                </div>
                <div className="flex justify-center items-center gap-[12px]">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        type="button"
                        className="h-10 bg-red-600 hover:bg-red-700 text-white"
                      >
                        Delete Account
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="bg-[#110018] border border-red-500/20">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-white">
                          Are you absolutely sure?
                        </AlertDialogTitle>
                        <AlertDialogDescription className="text-white/70">
                          This action cannot be undone. This will permanently
                          delete your account and remove all your data from our
                          servers.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="border-white/20 text-white hover:bg-white/10">
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteAccount}
                          disabled={isDeleting}
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          {isDeleting && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          Delete Account
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Two Factor Setup Dialog */}
      <Dialog open={twoFaSetup} onOpenChange={setTwoFaSetup}>
        <DialogContent className="bg-[#110018] border border-white/24 max-w-lg p-6">
          <TwoFactorSetup
            onComplete={onTwoFaSetupComplete}
            onClose={() => setTwoFaSetup(false)}
            userId={userId}
          />
        </DialogContent>
      </Dialog>
    </DashboardWrapper>
  );
}