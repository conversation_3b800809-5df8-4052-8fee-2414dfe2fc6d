"use client";

import { motion } from "framer-motion";
import { useEffect } from "react";
import {
  Shield,
  Database,
  Target,
  UserCheck,
  Mail,
  Share2,
  Lock,
  Globe,
  FileClock,
  Bell,
  FileText,
  Eye,
  ArrowRight,
} from "lucide-react";
import AOS from "aos";
import "aos/dist/aos.css";

const sections = [
  {
    icon: Shield,
    title: "1. Introduction",
    content:
      "This Privacy Policy explains how StreamBliss ('we', 'us', 'our') collects, uses, and protects your personal data when you use our video hosting service. Our commitment is to handle your data transparently and securely, in compliance with data protection regulations like the GDPR and CCPA.",
    gradient: "from-blue-400 to-cyan-500",
  },
  {
    icon: Database,
    title: "2. Data We Collect",
    gradient: "from-emerald-400 to-teal-500",
    subSections: [
      {
        title: "Information You Provide",
        content:
          "When you register for an account, you provide us with personal information, including your name, email address, and password. If you subscribe to a paid plan, our third-party payment processors will collect payment information. We do not store your full payment card details.",
      },
      {
        title: "Content You Upload",
        content:
          "We collect the video files and other content you upload to our service. This includes the content itself and associated metadata, such as titles, descriptions, and tags.",
      },
      {
        title: "Information We Collect Automatically",
        content:
          "We automatically log usage data when you interact with our service. This includes your IP address, browser type, device information, operating system, pages visited, and videos viewed.",
      },
    ],
  },
  {
    icon: Target,
    title: "3. How We Use Your Data",
    content:
      "We use your data to provide, maintain, and improve our services. This includes processing and streaming your video content, personalizing your experience, providing customer support, and communicating with you about your account.",
    gradient: "from-purple-400 to-pink-500",
  },
  {
    icon: Share2,
    title: "4. Data Sharing and Disclosure",
    content:
      "We do not sell your personal data. We may share your information with trusted third-party service providers who help us operate our service, such as for payment processing, cloud hosting, and customer support.",
    gradient: "from-orange-400 to-red-500",
  },
  {
    icon: Lock,
    title: "5. Data Security",
    content:
      "We implement robust technical and organizational security measures, including encryption, to protect your data from unauthorized access, alteration, or disclosure. However, no method of transmission over the Internet is 100% secure.",
    gradient: "from-green-400 to-emerald-500",
  },
  {
    icon: Globe,
    title: "6. International Data Transfers",
    content:
      "Your information may be transferred to, and maintained on, computers located outside of your state, province, or country. We ensure that appropriate safeguards are in place to protect your data when it is transferred internationally.",
    gradient: "from-indigo-400 to-purple-500",
  },
  {
    icon: FileClock,
    title: "7. Data Retention",
    content:
      "We retain your personal data for as long as your account is active or as needed to provide you with our services. We may retain certain information for longer periods for legal, tax, or legitimate business purposes.",
    gradient: "from-amber-400 to-orange-500",
  },
  {
    icon: UserCheck,
    title: "8. Your Rights",
    content:
      "Depending on your location, you may have rights to access, correct, or delete your personal data, as well as to object to or restrict its processing. You can exercise these rights by contacting us.",
    gradient: "from-pink-400 to-rose-500",
  },
  {
    icon: Bell,
    title: "9. Changes to This Policy",
    content:
      "We may update this privacy policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the 'Last updated' date.",
    gradient: "from-cyan-400 to-blue-500",
  },
  {
    icon: FileText,
    title: "10. Children's Privacy",
    content:
      "Our service is not directed to children under the age of 13. We do not knowingly collect personal information from children. If we become aware that we have collected personal data from a child without parental consent, we will take steps to remove that information.",
    gradient: "from-violet-400 to-purple-500",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function PrivacyPage() {
  const today = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div className="space-y-16 md:space-y-20">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Your Privacy Matters
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Privacy Policy
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4">
          We&apos;re committed to protecting your privacy and being transparent
          about how we handle your data.
        </p>

        <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/5 border border-white/10 backdrop-blur-sm">
          <Eye className="h-4 w-4 text-purple-400 mr-2" />
          <span className="text-sm text-gray-400">Last updated: {today}</span>
        </div>
      </motion.div>

      {/* Quick Overview */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Privacy at a Glance
            </h2>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="space-y-3">
                <div className="w-12 h-12 mx-auto bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-lg font-semibold text-white">We Protect</h3>
                <p className="text-sm text-gray-400">
                  Your data is encrypted and securely stored with
                  industry-leading security measures.
                </p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 mx-auto bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-xl flex items-center justify-center">
                  <UserCheck className="h-6 w-6 text-emerald-400" />
                </div>
                <h3 className="text-lg font-semibold text-white">
                  You Control
                </h3>
                <p className="text-sm text-gray-400">
                  Access, update, or delete your personal information anytime
                  through your account settings.
                </p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 mx-auto bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-purple-400" />
                </div>
                <h3 className="text-lg font-semibold text-white">
                  We&apos;re Transparent
                </h3>
                <p className="text-sm text-gray-400">
                  Clear policies and no hidden practices. You always know how
                  your data is used.
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Privacy Sections */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {sections.map((section, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="group relative"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay={index * 50}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-8 md:p-10 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
              <div className="flex items-start gap-6">
                <div
                  className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${section.gradient} bg-opacity-20 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                >
                  <section.icon className="h-7 w-7 text-white" />
                </div>
                <div className="flex-1">
                  <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white group-hover:text-gray-100 transition-colors">
                    {section.title}
                  </h2>
                  <div className="space-y-4 text-gray-300 leading-relaxed">
                    {section.content && (
                      <p className="text-lg group-hover:text-gray-200 transition-colors">
                        {section.content}
                      </p>
                    )}
                    {section.subSections &&
                      section.subSections.map((sub, i) => (
                        <div
                          key={i}
                          className="bg-white/5 rounded-xl p-6 border border-white/10"
                        >
                          <h3 className="font-semibold text-white mb-2 text-lg">
                            {sub.title}
                          </h3>
                          <p className="text-gray-300 leading-relaxed">
                            {sub.content}
                          </p>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Contact DPO Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 mx-auto mb-6">
              <Mail className="h-8 w-8 text-blue-400" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Questions About Your Privacy?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Our Data Protection Officer is here to help with any
              privacy-related questions or concerns.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25"
              >
                <Mail className="mr-2 h-5 w-5" />
                Contact Our DPO
              </a>
              <span className="text-gray-400 font-medium">
                <EMAIL>
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}