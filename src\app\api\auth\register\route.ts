import {NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function POST(request: Request): Promise<Response> {
    const {email, password, name} = await request.json();
    if (!email || !password || !name) {
        return new Response('Invalid name, email or password', {status: 401});
    }

    const registerRequest = await fetch(process.env.VIDEO_API_URL + '/auth/register', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
            password,
            name,
        }),
    });

    const data = await registerRequest.json();
    if (data && data.statusCode === 401) {
        return new Response(data.message, {status: 401});
    }

    if (data && data.statusCode === 409) {
        return new Response(data.message, {status: 401});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, message: "Successfully registered."});
}