"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { removeComment } from "@/server/comment";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogActions} from "@prisma/client";
import {LogConstants} from "@/server/log-constants";

type Callback = {
  success: boolean;
  message: string;
}

type Props = {
  commentId: string;
};

export default async function submitRemoveComment({ commentId }: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later.",
    }
  }

  const userSession = await getUserSession();
  if (!userSession || !userSession.userId) {
    return {
      success: false,
      message: "You must be logged in to remove a comment.",
    }
  }

  const user = await getUserById(userSession.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found.",
    }
  }

  const allowed = await hasPermission(user.roleId, ["ADMIN_VIDEO_COMMENT_DELETE"]);
  if (!allowed) {
    return {
      success: false,
      message: "You do not have permission to remove comments.",
    }
  }

  await removeComment(commentId);
  await createLog(user.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_VIDEO_DELETE_COMMENT, LogActions.VIDEO);

  return {
    success: true,
    message: "Comment removed successfully",
  }
}
