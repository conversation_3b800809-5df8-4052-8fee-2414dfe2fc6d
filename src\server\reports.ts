"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { getVideoDataById } from "./video";

export const getOpenReports = async () => {
  return await prisma.reports.findMany({
    select: {
      id: true,
      reportReason: true,
      reportedUserId: true,
      createdAt: true,
      reportVideoId: true,
      details: true,
      reportCase: {
        take: 1,
        select: {
          status: true,
          adminUserId: true
        }
      }
    }
  });
}

type ReportData = {
  id: string;
  reportedUser: string;
  reportedUserId: string;
  reportReason: string;
  createdAt: string;
  status: string;
  moderator: string;
  shortLink: string;
  videoId: string;
  details: string | null;
}

export const getDashboardReports = async (): Promise<ReportData[]> => {
  const reports = await getOpenReports();
  const reportPromises = reports.filter(x => x.reportCase[0] != null && x.reportCase[0].status != "CLOSED").map(async (report) => {
    const user = await getUserById(report.reportedUserId);
    if (!user) return null;

    const videoData = await getVideoDataById(report.reportVideoId);

    let moderatorName = "";
    if (report.reportCase[0].adminUserId) {
      const moderator = await getUserById(report.reportCase[0].adminUserId);
      moderatorName = moderator?.name || "";
    }

    return {
      id: report.id,
      reportedUser: user.name!,
      reportedUserId: user.id,
      reportReason: report.reportReason,
      createdAt: report.createdAt.toISOString(),
      status: report.reportCase[0].status,
      moderator: moderatorName,
      shortLink: videoData == null ? "DELETED" : videoData.shortLink,
      videoId: report.reportVideoId,
      details: report.details
    };
  });
  const _newReports = (await Promise.all(reportPromises)).filter(report => report !== null) as ReportData[];
  return _newReports;
}

export const getReportData = async (reportId: string) => {
  return await prisma.reports.findUnique({
    where: {
      id: reportId
    }
  });
};

export const getReportCase = async (reportId: string) => {
  return await prisma.reportCase.findFirst({
    where: {
      reportId
    }
  });
}
