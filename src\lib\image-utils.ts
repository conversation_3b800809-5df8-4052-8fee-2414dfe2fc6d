export function isAnimatedImage(imageUrl: string | null | undefined): boolean {
  if (!imageUrl) return false;

  const url = imageUrl.toLowerCase();

  return (
    url.endsWith(".gif") ||
    url.endsWith(".webp") ||
    url.includes(".gif?") ||
    url.includes(".webp?")
  );
}

export function shouldDisableOptimization(
  imageUrl: string | null | undefined,
): boolean {
  if (!imageUrl) return false;

  if (
    imageUrl.includes("api.streambliss.cloud/image/") ||
    imageUrl.includes("/image/")
  ) {
    return true;
  }

  return isAnimatedImage(imageUrl);
}