"use server";

import { getUserById, updateUserPassword } from "@/lib/db/user";
import { sendEmail } from "@/lib/email-sender";
import { prisma } from "@/lib/prisma";
import { isBefore, subHours } from "date-fns";
import { v4 } from "uuid";
import { generatePasswordResetTemplate } from "@/lib/email-templates";

type PasswordResetData = {
  id: number
  userId: string;
  token: string;
  requestedAt: Date;
  expired: boolean;
};

type PasswordResetDataResponse = {
  state: "success" | "error" | "expired";
}

export const validatePasswordResetToken = async (token: string): Promise<PasswordResetDataResponse> => {
  const tokenData = await getPasswordReset(token);
  if (!tokenData) return { state: "error" };

  const today = new Date();
  const threeHoursAgo = subHours(today, 3);
  if (!isBefore(threeHoursAgo, tokenData.requestedAt)) {
    await prisma.passwordReset.update({
      where: { id: tokenData.id },
      data: { expired: true }
    });
    return { state: "expired" };
  }

  return { state: "success" };
}

export const resetPassword = async (userId: string) => {
  const user = await getUserById(userId);
  if (!user) return false;

  const data = await prisma.passwordReset.create({
    data: {
      userId: userId,
      token: v4(),
      requestedAt: new Date(),
      expired: false
    }
  });

  const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/password-reset/${data.token}`;
  const emailHtml = generatePasswordResetTemplate(resetLink);
  
  const success = await sendEmail(user.email, "Password Reset", emailHtml, true);
  if (!success) {
    await prisma.passwordReset.delete({
      where: {
        id: data.id
      }
    });
    return false;
  }

  return data != null;
};

export const getPasswordReset = async (token: string) => {
  const data = await prisma.passwordReset.findFirst({
    where: {
      token: token
    }
  });

  return data;
};

export const closePasswordReset = async (token: string) => {
  const data = await getPasswordReset(token);
  if (!data) return false;

  await prisma.passwordReset.delete({
    where: {
      id: data.id
    }
  });

  return data != null;
}

export const updatePassword = async (userId: string, password: string, resetId: string) => {
  const user = await getUserById(userId);
  if (!user) {
    return {
      success: false,
      message: "User not found",
    }
  }

  await updateUserPassword(userId, password);

  if (!await closePasswordReset(resetId)) {
    return {
      success: false,
      message: "Failed to update password",
    }
  }


  return {
    success: true,
    message: "Password updated successfully",
  }
}
