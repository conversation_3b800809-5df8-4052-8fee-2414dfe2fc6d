"use client";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Icons } from "@/components/icons";
import { useGoogleLogin } from "@react-oauth/google";
import { useEffect, useState } from "react";
import CustomButton from "@/components/common/CustomButton";

export function LoginForm({
  clientId,
  redirectUri,
  banned,
  maintenance,
}: {
  clientId: string;
  redirectUri: string;
  banned: boolean;
  maintenance: boolean;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isBanned, setIsBanned] = useState(banned);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (searchParams?.get("banned") === "true") {
      setIsBanned(true);
    }
  }, [searchParams]);

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData(event.currentTarget);
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      const request = await fetch('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email,
          password,
        }),
      });

      const data = await request.json();

      if (data.status === 200) {
        success('Login Successful', 'Successfully logged in');
        router.push('/dashboard');
        return;
      }

      if (data.status === 401) {
        const message = await request.text();
        alert(message);
        error('Login Failed', message);
      }

      if (data.status === 201 && data.redirect) {
        router.push(data.redirect);
      }
    } catch (err) {
      error('Login Error', 'Something went wrong');
      console.error("Error during login: ", err);
    } finally {
      setIsLoading(false);
    }
  }

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (response: any) => {
      if (maintenance) {
        router.push('/maintenance');
      }

      const request = await fetch("/api/auth/google", {
        method: "POST",
        body: JSON.stringify({
          access_token: response.access_token,
        })
      });

      const data = await request.json();
      if (data.status === 200 && data.redirect) {
        router.push('/dashboard');
      }
    },
    onError: (_error) => {
      console.error('error trying to sign in with google: ', _error);
    },
  });

  const handleDiscordLogin = () => {
    const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&response_type=code&redirect_uri=${encodeURIComponent(
        redirectUri,
    )}&scope=identify+email`;
    window.location.href = discordAuthUrl;
  };

  return (
    <div className="space-y-6">
      {/* Social Login Buttons */}
      <div className="space-y-3">
        <button
          type="button"
          onClick={() => handleGoogleLogin()}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-3 px-6 py-3.5 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 rounded-xl text-white font-medium transition-all duration-300 backdrop-blur-sm"
        >
          <Icons.google className="h-5 w-5" />
          Continue with Google
        </button>

        <button
          type="button"
          onClick={handleDiscordLogin}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-3 px-6 py-3.5 bg-[#5865F2]/20 hover:bg-[#5865F2]/30 border border-[#5865F2]/30 hover:border-[#5865F2]/50 rounded-xl text-white font-medium transition-all duration-300 backdrop-blur-sm"
        >
          <Icons.discord className="h-5 w-5" />
          Continue with Discord
        </button>
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-white/20" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-transparent px-4 text-white/60 font-medium tracking-wider">
            or continue with email
          </span>
        </div>
      </div>

      {/* Email & Password Form */}
      <form onSubmit={onSubmit} className="space-y-5">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-white/90 font-medium">
            Email Address
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            disabled={isLoading}
            placeholder="Enter your email"
            className="h-12 bg-white/5 border-white/20 focus:border-custom-purple focus:ring-1 focus:ring-custom-purple text-white placeholder:text-white/50 rounded-xl backdrop-blur-sm transition-all duration-300"
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="text-white/90 font-medium">
              Password
            </Label>
            <Link
              href="/password-forgot"
              className="text-sm text-custom-purple hover:text-custom-pink transition-colors font-medium"
            >
              Forgot password?
            </Link>
          </div>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="current-password"
              required
              disabled={isLoading}
              placeholder="Enter your password"
              className="h-12 pr-12 bg-white/5 border-white/20 focus:border-custom-purple focus:ring-1 focus:ring-custom-purple text-white placeholder:text-white/50 rounded-xl backdrop-blur-sm transition-all duration-300"
            />
            <span
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 -translate-y-1/2 px-2 py-1 text-xs text-white bg-gradient-to-r from-custom-purple/80 to-custom-pink/80 hover:from-custom-purple hover:to-custom-pink transition-all cursor-pointer select-none font-medium rounded-md shadow-sm backdrop-blur-sm"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  setShowPassword(!showPassword);
                }
              }}
            >
              {showPassword ? "Hide" : "Show"}
            </span>
          </div>
        </div>

        {/* Sign In Button */}
        <CustomButton
          variant="gradiant"
          className="w-full !py-[13px] !text-base !font-semibold !mt-6 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
          disabled={isLoading}
        >
          {isLoading ? "Signing In..." : "Sign In"}
        </CustomButton>
      </form>

      {/* Ban Message */}
      {isBanned && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
          <p className="text-red-400 text-sm text-center font-medium">
            Your account has been banned. Please contact support for more
            information.
          </p>
        </div>
      )}
    </div>
  );
}