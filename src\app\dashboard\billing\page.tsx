import { getUser } from "@/server/session";
import BillingPageClient from "./client";
import { redirect } from "next/navigation";
import { getUserNotifications } from "@/server/notifications";
import { getUserById } from "@/lib/db/user";
import { hasPermission } from "@/server/admin";
import { getUserProfilePicture } from "@/server/profile";

export default async function BillingPage() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }

  const dbUser = await getUserById(user.id);
  const hasAccessToAdmin = dbUser
    ? await hasPermission(dbUser.roleId, ["ADMIN_PAGE"])
    : false;
  const notifications = await getUserNotifications(user.id);
  const userProfileImage = await getUserProfilePicture(user.id);

  return (
    <BillingPageClient
      currentPackage={user.package}
      userName={user.name || ""}
      userId={user.id}
      userImage={userProfileImage}
      notifications={notifications}
      hasAccessToAdmin={hasAccessToAdmin}
    />
  );
}