"use client";

import { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Download,
  Share2,
  User,
  Loader2,
  ZoomIn,
  ZoomOut,
  RefreshCw,
  RotateCw,
  Flag,
} from "lucide-react";
import { format } from "date-fns";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { ReportDialog } from "./_components/report-dialog";
import Link from "next/link";
import NextImage from "next/image";
import Image from "next/image";

type ImagePageClientProps = {
  data: {
    id: string;
    name: string;
    shortLink: string;
    createdAt: Date;
    user: {
      id: string | null;
      name: string | null;
      avatar?: string;
    };
  };
  url: string;
};

export default function ImagePageClient({ data, url }: ImagePageClientProps) {
  const { success, error } = useEnhancedToast();
  const [isLoading, setIsLoading] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(
    null,
  );
  const [isDragging, setIsDragging] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [isReportOpen, setIsReportOpen] = useState(false);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageWrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (containerRef.current?.contains(e.target as Node)) {
        e.preventDefault();
        showControlsTemporarily();

        const delta = -e.deltaY / 500;
        const newZoom = Math.max(1, Math.min(5, zoom + delta));

        if (zoom !== newZoom) {
          const rect =
            imageWrapperRef.current?.getBoundingClientRect() ||
            containerRef.current.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          const zoomRatio = newZoom / zoom;

          setPosition((prev) => ({
            x: (x - prev.x) * (1 - zoomRatio) + prev.x,
            y: (y - prev.y) * (1 - zoomRatio) + prev.y,
          }));

          setZoom(newZoom);
        }
      }
    };

    const handleMouseMove = () => {
      showControlsTemporarily();
    };

    window.addEventListener("wheel", handleWheel, { passive: false });
    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("wheel", handleWheel);
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [zoom]);

  const showControlsTemporarily = () => {
    setShowControls(true);
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }
    hideControlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  const resetZoomAndPosition = () => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  };

  const onMouseDown = (e: React.MouseEvent) => {
    if (
      zoom === 1 ||
      e.target instanceof HTMLButtonElement ||
      (e.target as HTMLElement).closest("button") ||
      (e.target as HTMLElement).closest(".control-overlay")
    ) {
      return;
    }

    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    setIsDragging(true);
  };

  const onMouseMove = (e: React.MouseEvent) => {
    if (!dragStart) return;

    const newPosition = {
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y,
    };

    if (imageWrapperRef.current && imgRef.current) {
      const wrapperRect = imageWrapperRef.current.getBoundingClientRect();
      const imgRect = imgRef.current.getBoundingClientRect();
      const scaledImgWidth = imgRect.width / zoom;
      const scaledImgHeight = imgRect.height / zoom;

      const horizontalLimit = Math.max(
        0,
        (scaledImgWidth * zoom - wrapperRect.width) / 2 / zoom,
      );
      const verticalLimit = Math.max(
        0,
        (scaledImgHeight * zoom - wrapperRect.height) / 2 / zoom,
      );

      setPosition({
        x: Math.min(horizontalLimit, Math.max(-horizontalLimit, newPosition.x)),
        y: Math.min(verticalLimit, Math.max(-verticalLimit, newPosition.y)),
      });
    }
  };

  const onMouseUp = () => {
    setDragStart(null);
    setIsDragging(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    success("Link Copied", "Image link copied to clipboard");
  };

  const handleDownload = async () => {
    try {
      const response = await fetch("/api/user/media/image/download", {
        method: "POST",
        body: JSON.stringify({
          imageId: data.id
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        error("Download Failed", errorText);
        return;
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = `${data.id}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      success("Download Complete", "The image has been downloaded!");
    } catch {
      error("Download Error", "Failed to download image");
    }
  };

  const incrementZoom = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setZoom((prev) => Math.min(prev + 0.5, 5));
  };

  const decrementZoom = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    if (zoom > 1.5) {
      setZoom((prev) => prev - 0.5);
    } else if (zoom > 1) {
      setZoom(1);
      setPosition({ x: 0, y: 0 });
    }
  };

  const handleZoomReset = (e: React.MouseEvent) => {
    e.stopPropagation();
    resetZoomAndPosition();
  };

  const handleImageClick = (e: React.MouseEvent) => {
    if (
      zoom === 1 &&
      !(e.target instanceof HTMLButtonElement) &&
      !(e.target as HTMLElement).closest("button") &&
      !(e.target as HTMLElement).closest(".control-overlay")
    ) {
      incrementZoom();
    }
  };

  const handleRotate = (e: React.MouseEvent) => {
    e.stopPropagation();
    setRotation((prev) => (prev + 90) % 360);
  };

  return (
    <div className="h-screen bg-black overflow-hidden flex flex-col">
      <nav className="bg-black border-b border-gray-800 flex-shrink-0">
        <div className="w-full px-4 py-3 flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <Image
              src="/assets/images/webp/logo.webp"
              alt="StreamBliss"
              width={120}
              height={24}
              className="h-6 w-auto"
              priority
            />
          </Link>

          <Link href="/dashboard">
            <Button className="bg-gradient-to-r from-custom-pink to-custom-purple hover:scale-105 transition-transform duration-300 text-white text-sm font-semibold px-4 py-2 h-9 cursor-pointer">
              Open StreamBliss
            </Button>
          </Link>
        </div>
      </nav>

      <div className="bg-gradient-to-r from-purple-600 to-pink-600 flex-shrink-0">
        <div className="max-w-[1200px] mx-auto px-4 py-3 flex items-center justify-center gap-3">
          <div className="flex items-center gap-2 text-white">
            <Image
              src="/assets/images/webp/logo.webp"
              alt="StreamBliss"
              width={100}
              height={20}
              className="h-5 w-auto filter brightness-0 invert"
            />
            <span className="text-sm font-medium">Image hosting made easy</span>
          </div>
          <Link href="/dashboard">
            <Button className="bg-white hover:bg-gray-100 hover:scale-105 transition-all duration-300 text-purple-600 text-sm font-semibold px-4 py-1.5 h-8 cursor-pointer border border-white/20 shadow-lg">
              Get started
            </Button>
          </Link>
        </div>
      </div>

      <main className="flex-1 bg-black px-8 py-8 min-h-0">
        <div className="max-w-[1200px] mx-auto h-full flex flex-col">
          <div
            ref={containerRef}
            className={`relative rounded-lg overflow-hidden mb-4 flex-1 bg-gray-900 ${zoom > 1 ? (isDragging ? "cursor-grabbing" : "cursor-grab") : "cursor-zoom-in"}`}
            onMouseDown={onMouseDown}
            onMouseMove={onMouseMove}
            onMouseUp={onMouseUp}
            onMouseLeave={onMouseUp}
            onClick={handleImageClick}
          >
            <div
              ref={imageWrapperRef}
              className="relative flex items-center justify-center w-full h-full"
            >
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/40">
                  <Loader2 className="h-8 w-8 animate-spin text-white" />
                </div>
              )}
              <NextImage
                ref={imgRef}
                src={url}
                alt={decodeURIComponent(data.name)}
                onLoad={handleImageLoad}
                className={`
                  max-w-full max-h-full w-auto h-auto object-contain select-none
                  ${isLoading ? "opacity-0" : "opacity-100"}
                  transition-opacity duration-300
                `}
                style={{
                  transform: `scale(${zoom}) translate(${position.x}px, ${position.y}px) rotate(${rotation}deg)`,
                  transition: zoom === 1 ? "transform 0.2s ease-out" : "none",
                  transformOrigin: "center center",
                }}
                draggable={false}
                unoptimized
                width={1920}
                height={1080}
                onDoubleClick={resetZoomAndPosition}
              />
            </div>

            {!isLoading && (
              <div className="control-overlay absolute inset-0 pointer-events-none">
                {/* Zoom percentage - always visible but fades */}
                <div
                  className={`absolute top-4 left-1/2 -translate-x-1/2 bg-black/60 text-white text-sm py-1 px-3 rounded-full font-medium border border-white/10 z-10 transition-opacity duration-300 ${showControls ? "opacity-80" : "opacity-30"}`}
                >
                  {Math.round(zoom * 100)}%
                </div>

                <div
                  className={`absolute top-4 left-4 bg-black/60 text-white text-xs py-2 px-3 rounded-lg border border-white/10 z-10 max-w-[200px] transition-opacity duration-300 ${showControls ? "opacity-100" : "opacity-0"}`}
                >
                  <div className="space-y-1">
                    <div className="text-white/90 font-medium">Controls:</div>
                    <div className="text-white/70">• Click to zoom in</div>
                    <div className="text-white/70">• Scroll wheel to zoom</div>
                    <div className="text-white/70">
                      • Drag to move when zoomed
                    </div>
                    <div className="text-white/70">• Double-click to reset</div>
                  </div>
                </div>

                <div
                  className={`absolute bottom-4 right-4 flex gap-2 z-10 pointer-events-auto transition-opacity duration-300 ${showControls ? "opacity-100" : "opacity-0"}`}
                >
                  <Button
                    size="icon"
                    onClick={handleZoomReset}
                    className="h-9 w-9 rounded-full bg-black/60 border border-white/10 text-white hover:bg-gray-800 cursor-pointer group relative"
                    title="Reset zoom and rotation"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      Reset
                    </div>
                  </Button>
                  <Button
                    size="icon"
                    onClick={handleRotate}
                    className="h-9 w-9 rounded-full bg-black/60 border border-white/10 text-white hover:bg-gray-800 cursor-pointer group relative"
                    title="Rotate image 90°"
                  >
                    <RotateCw className="h-4 w-4" />
                    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      Rotate
                    </div>
                  </Button>
                  <Button
                    size="icon"
                    onClick={decrementZoom}
                    disabled={zoom <= 1}
                    className="h-9 w-9 rounded-full bg-black/60 border border-white/10 text-white hover:bg-gray-800 cursor-pointer group relative disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Zoom out"
                  >
                    <ZoomOut className="h-5 w-5" />
                    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      Zoom Out
                    </div>
                  </Button>
                  <Button
                    size="icon"
                    onClick={incrementZoom}
                    disabled={zoom >= 5}
                    className="h-9 w-9 rounded-full bg-black/60 border border-white/10 text-white hover:bg-gray-800 cursor-pointer group relative disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Zoom in"
                  >
                    <ZoomIn className="h-5 w-5" />
                    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      Zoom In
                    </div>
                  </Button>
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between flex-shrink-0">
            <div className="flex-1 min-w-0">
              <h1 className="text-base font-normal text-white mb-1">
                {decodeURIComponent(data.name)}
              </h1>
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <span>
                  Uploaded {format(new Date(data.createdAt), "MMM d, yyyy")}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3 ml-4">
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white/5 text-white border-white/20 hover:bg-white/10 hover:border-white/30 hover:scale-105 h-9 px-4 cursor-pointer transition-all duration-200 font-medium"
                onClick={handleShare}
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-blue-500/10 text-blue-400 border-blue-500/30 hover:bg-blue-500/20 hover:border-blue-500/50 hover:text-blue-300 hover:scale-105 h-9 px-4 cursor-pointer transition-all duration-200 font-medium"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
                Download
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-red-500/10 text-red-400 border-red-500/30 hover:bg-red-500/20 hover:border-red-500/50 hover:text-red-300 hover:scale-105 h-9 px-4 cursor-pointer transition-all duration-200 font-medium"
                onClick={() => setIsReportOpen(true)}
              >
                <Flag className="h-4 w-4" />
                Report
              </Button>
            </div>
          </div>
        </div>
      </main>

      <ReportDialog
        open={isReportOpen}
        onOpenChange={setIsReportOpen}
        imageId={data.shortLink}
      />
    </div>
  );
}