import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function DELETE(request: NextRequest) {
    const {videoId} = await request.json();
    if (!videoId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No Video ID provided."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: "No valid user-session found."});
    }

    const videoDeleteRequest = await fetch(process.env.VIDEO_API_URL + "/videos/" + videoId, {
        method: "DELETE",
        headers: {
            "Authorization": `Bearer ${userSession.accessToken}`,
            "x-api-key": process.env.API_SERVER_KEY!,
        }
    });

    if (!videoDeleteRequest.ok) {
        const data = await videoDeleteRequest.json();
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, message: "Video successfully deleted."});
}