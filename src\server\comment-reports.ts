"use server";

import { prisma } from "@/lib/prisma";
import { getVideoDataById } from "./video";
import { getUserById } from "@/lib/db/user";
import { createNotification } from "./notifications";
import { ReportStatus } from "@prisma/client";
import { ReportCommentDialog } from "src/app/v/[id]/_components/report-comment-dialog";

export async function getCommentById(commentId: string) {
  return await prisma.comments.findUnique({
    where: { id: commentId }
  });
}

export async function createReportComment(userId: string, commentId: string, reason: string, details?: string) {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error("User not found");
    }

    if (!reason) {
      throw new Error("Reason is required");
    }

    if (!commentId) {
      throw new Error("Comment ID is required");
    }

    if (reason.length < 3) {
      throw new Error("Reason must be at least 3 characters long");
    }

    if (details && details.length > 150) {
      throw new Error("Details must be less than 150 characters long");
    }

    const comment = await getCommentById(commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    const video = await getVideoDataById(comment.videoId);
    if (!video) {
      throw new Error("Video not found");
    }

    const reportedUser = await getUserById(comment.userId);
    if (!reportedUser) {
      throw new Error("Reported user not found");
    }

    // if (userId === comment.userId || userId === reportedUser.id) {
    //   throw new Error("You cannot report your own comment");
    // }

    return await prisma.commentReports.create({
      data: {
        userId,
        commentId,
        commentText: comment.comment,
        reason,
        details,
        reportedVideoId: video.id,
        reportedVideoTitle: video.title,
        reportedVideoUrl: video.shortLink,
        status: "PENDING",
        reportedUserId: reportedUser.id,
        reportedUserEmail: reportedUser.email,
        reportedUserName: reportedUser.name!
      }
    })
  } catch (error) {
    throw new Error("Error reporting comment: " + error.message);
  }
}

export async function deleteCommentById(commentId: string) {
  try {
    const comment = await getCommentById(commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    const commentOwner = await getUserById(comment.userId);
    if (!commentOwner) {
      throw new Error("Owner not found");
    }

    await prisma.comments.delete({
      where: { id: commentId },
    });

    await createNotification(commentOwner.id, `Your comment has been deleted due to a report.`, "INFO");
  } catch (error) {
    throw new Error("Error fetching comment reports: " + error.message);
  }
}

export async function updateCommentReportStatus(reportId: string, status: ReportStatus) {
  try {
    const report = await prisma.commentReports.findUnique({
      where: { id: reportId }
    });
    if (!report) {
      throw new Error("Report not found");
    }

    if (report.status === status) {
      throw new Error("Report already has this status");
    }

    await prisma.commentReports.update({
      where: { id: reportId },
      data: { status }
    });

    return report;
  } catch (error) {
    throw new Error("Error updating comment report status: " + error.message);
  }
}

type CommentReport = {
  id: string;
  reason: string;
  details?: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  reportedComment: {
    id: string;
    message: string;
  },
  reportedBy: {
    id: string;
    name: string;
    email: string;
  }
  reportedUser: {
    id: string;
    name: string;
    email: string;
  },
  reportedVideo: {
    id: string;
    title: string;
    shortLink: string;
  }
}

export async function getCommentReports() {
  try {
    const reports = await prisma.commentReports.findMany({
      orderBy: {
        createdAt: "desc"
      }
    });

    const reportsWithDetails = await Promise.all(reports.map(async (report) => {
      const user = await getUserById(report.userId);
      if (!user) {
        throw new Error("User not found");
      }

      return {
        ...report,
        reportedComment: {
          id: report.commentId,
          message: report.commentText,
        },
        reportedBy: {
          id: report.userId,
          name: user.name,
          email: user.email,
        },
        reportedUser: {
          id: report.reportedUserId,
          name: report.reportedUserName,
          email: report.reportedUserEmail,
        },
        reportedVideo: {
          id: report.reportedVideoId,
          title: report.reportedVideoTitle,
          shortLink: report.reportedVideoUrl,
        }
      } as CommentReport;
    }));

    return reportsWithDetails || [] as CommentReport[];
  } catch (error) {
    throw new Error("Error fetching comment reports: " + error.message);
  }
}

export async function getCommentReportById(reportId: string) {
  try {
    const report = await prisma.commentReports.findUnique({
      where: { id: reportId }
    });
    if (!report) {
      throw new Error("Report not found");
    }

    const user = await getUserById(report.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const comment = await getCommentById(report.commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    const video = await getVideoDataById(comment.videoId);
    if (!video) {
      throw new Error("Video not found");
    }

    const reportedUser = await getUserById(report.reportedUserId);
    if (!reportedUser) {
      throw new Error("Reported user not found");
    }

    return {
      ...report,
      reportedComment: {
        id: comment.id,
        message: comment.comment,
      },
      reportedBy: {
        id: report.userId,
        name: user.name,
        email: user.email,
      },
      reportedUser: {
        id: reportedUser.id,
        name: reportedUser.name,
        email: reportedUser.email,
      },
      reportedVideo: {
        id: video.id,
        title: video.title,
        shortLink: video.shortLink,
      }
    } as CommentReport;
  } catch (error) {
    throw new Error("Error fetching comment report by ID: " + error.message);
  }
}
