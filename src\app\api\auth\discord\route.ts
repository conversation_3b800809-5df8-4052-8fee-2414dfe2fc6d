import {encryptData} from "@/lib/crypter";
import {createUser, getUserByEmail, verifyUserAccount} from "@/lib/db/user";
import {NextRequest, NextResponse} from "next/server";
import {isProviderLinked, linkProvider, update<PERSON><PERSON>ider} from "src/server/provider";
import {getTwoFactorSession, getUserSession} from "src/server/session";
import {createUserSettings, getUserSettings} from "src/server/user-settings";
import {getClientIp, getGeoLocation} from "@/server/geolocation";
import {createLoginHistoryLog} from "@/server/login-history";
import {rateLimiter} from "@/lib/rate-limit";

export async function GET(req: NextRequest) {
    const {searchParams} = new URL(req.url);
    const code = searchParams.get("code");

    if (!code) return NextResponse.json({error: "Missing code"}, {status: 400});

    try {
        const discordLoginRequest = await fetch(process.env.VIDEO_API_URL + "/auth/oauth-login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "x-api-key": process.env.API_SERVER_KEY!,
            },
            body: JSON.stringify({
                provider: 'discord',
                access_token: code,
            })
        });

        const data = await discordLoginRequest.json();
        if (data.statusCode === 500) {
            return NextResponse.json({message: "Error logging in via discord"}, {status: 500});
        }

        const session = await getUserSession();
        session.userId = data.user.id;
        session.accessToken = data.accessToken;
        await session.save();

        return NextResponse.json({status: 200, redirect: '/dashboard'});
    } catch (error) {
        console.error("Discord OAuth Error:", error);
        return NextResponse.json({error: (error as Error).message}, {status: 500});
    }
}
