import { getVideos, getTrendingVideos } from "./_actions/get-videos";
import Footer from "@/components/Footer";
import { ImprovedCommunityClient } from "./_components/improved-community-client";
import { CommunityHeaderWrapper } from "./_components/community-header-wrapper";
import { Suspense } from "react";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Community Videos - StreamBliss",
  description:
    "Discover amazing videos from the StreamBliss community. Smart recommendations, trending content, and the best creators all in one place.",
  openGraph: {
    title: "Community Videos - StreamBliss",
    description:
      "Discover amazing videos from the StreamBliss community. Smart recommendations, trending content, and the best creators all in one place.",
    type: "website",
    url: "/community",
    images: [
      {
        url: "/assets/meta.webp",
        width: 1200,
        height: 630,
        alt: "StreamBliss Community Videos",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Community Videos - StreamBliss",
    description:
      "Discover amazing videos from the StreamBliss community. Smart recommendations, trending content, and the best creators all in one place.",
  },
};

export const dynamic = "force-dynamic";
export const revalidate = 30;

function VideoLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4">
        {/* Hero skeleton */}
        <div className="bg-gradient-to-br from-zinc-800/50 to-zinc-900/50 animate-pulse h-96 w-full max-w-6xl mx-auto rounded-xl mb-8"></div>

        {/* Search and filter skeleton */}
        <div className="max-w-4xl mx-auto mb-8 space-y-4">
          <div className="bg-zinc-800/50 animate-pulse h-12 rounded-xl"></div>
          <div className="flex gap-2 justify-center">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <div
                  key={i}
                  className="bg-zinc-800/50 animate-pulse h-8 w-20 rounded-full"
                ></div>
              ))}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 auto-rows-[120px]">
          <div className="col-span-2 row-span-2 bg-zinc-800/50 animate-pulse rounded-xl"></div>
          <div className="row-span-2 bg-zinc-800/50 animate-pulse rounded-xl"></div>
          {Array(6)
            .fill(0)
            .map((_, i) => (
              <div
                key={i}
                className="bg-zinc-800/50 animate-pulse rounded-xl"
              ></div>
            ))}
          {/* Wide card */}
          <div className="col-span-2 bg-zinc-800/50 animate-pulse rounded-xl"></div>
          {/* More small cards */}
          {Array(4)
            .fill(0)
            .map((_, i) => (
              <div
                key={i + 6}
                className="bg-zinc-800/50 animate-pulse rounded-xl"
              ></div>
            ))}
        </div>
      </div>
    </div>
  );
}

export default async function CommunityPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <Suspense fallback={<VideoLoadingSkeleton />}>
        <VideosContent />
      </Suspense>
      <Footer />
    </div>
  );
}

async function VideosContent() {
  const [videos, trendingVideos] = await Promise.all([
    getVideos(),
    getTrendingVideos(),
  ]);

  return (
    <ImprovedCommunityClient videos={videos} trendingVideos={trendingVideos}>
      <CommunityHeaderWrapper />
    </ImprovedCommunityClient>
  );
}