"use client";

import { Lock } from "lucide-react";
import ChangeRoleForm from "./change-role-form";

type ChangeRoleFormProps = {
  userId: string | undefined;
  roles: { id: number; name: string }[];
  onComplete: (close: boolean) => void;
};

export default function ChangeRoleDialog({userId, roles, onComplete}: ChangeRoleFormProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Lock className="h-5 w-5 text-primary" />
        <h2 className="text-lg font-semibold">Change the users role!</h2>
      </div>
      <ChangeRoleForm userId={userId} roles={roles} onComplete={onComplete} />
    </div>
  )
}
