"use server";

import { getUserById, isUsernameTaken, updateUsername } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
}

type ProfileData = {
  userId: string;
  name: string;
  instagram?: string;
  twitch?: string;
  twitter?: string;
  website?: string;
  youtube?: string;
};

const nameRegex = /^[a-zA-Z0-9.,\-#;:_?!]{3,}$/;
const urlRegex = /^[a-zA-Z0-9.@_\-/]*$/;

export default async function updateProfile({ 
  userId, 
  name, 
  instagram = "",
  twitch = "", 
  twitter = "", 
  website = "", 
  youtube = "" 
}: ProfileData): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!userId || !name) {
    return {
      success: false,
      message: "Missing required fields"
    }
  };

  if (!nameRegex.test(name)) {
    return {
      success: false,
      message: "Name must be at least 3 characters, and can only contain letters, numbers, and the following characters: . , - # ; : _ ? !"
    }
  }

  const socialLinks = [instagram, twitch, twitter, website, youtube].filter(Boolean);
  for (const link of socialLinks) {
    if (link && !urlRegex.test(link)) {
      return {
        success: false,
        message: "Social media links can only contain letters, numbers, and basic symbols"
      }
    }
  }

  const user = await getUserById(userId);
  if (!user) {
    return {
      success: false,
      message: "User not found"
    }
  };

  if (user.name !== name) {
    const taken = await isUsernameTaken(name);
    if (taken) {
      return {
        success: false,
        message: "Username is already taken"
      }
    };
  }

  // Update username and social media links
  await updateUsername(userId, name, { instagram, twitch, twitter, website, youtube });
  await createLog(userId, LogConstants.PROFILE_UPDATED, LogActions.ACCOUNT);

  return {
    success: true,
    message: "Profile updated successfully"
  }
}
