"use client";

import React from "react";
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react";
import { cn } from "@/lib/utils";

// Toast Context
const ToastContext = React.createContext<{
  addToast: (toast: Omit<EnhancedToastProps, "onClose">) => string;
  removeToast: (id: string) => void;
  success: (title: string, description?: string) => string;
  error: (title: string, description?: string) => string;
  warning: (title: string, description?: string) => string;
  info: (title: string, description?: string) => string;
} | null>(null);

interface EnhancedToastProps {
  title: string;
  description?: string;
  variant?: "default" | "success" | "error" | "warning" | "info";
  duration?: number;
  onClose?: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const toastIcons = {
  default: Info,
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
};

const toastStyles = {
  default: {
    container: "bg-grey border-white/15 shadow-black/60",
    icon: "text-custom-purple",
    progress: "from-custom-purple to-custom-pink",
  },
  success: {
    container: "bg-grey border-emerald-400/30 shadow-emerald-500/20",
    icon: "text-emerald-400",
    progress: "from-emerald-500 to-green-500",
  },
  error: {
    container: "bg-grey border-red-400/30 shadow-red-500/20",
    icon: "text-red-400",
    progress: "from-red-500 to-pink-500",
  },
  warning: {
    container: "bg-grey border-amber-400/30 shadow-amber-500/20",
    icon: "text-amber-400",
    progress: "from-amber-500 to-orange-500",
  },
  info: {
    container: "bg-grey border-blue-400/30 shadow-blue-500/20",
    icon: "text-blue-400",
    progress: "from-blue-500 to-cyan-500",
  },
};

export const EnhancedToast: React.FC<EnhancedToastProps> = ({
  title,
  description,
  variant = "default",
  duration = 5000,
  onClose,
  action,
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [isExiting, setIsExiting] = React.useState(false);

  const Icon = toastIcons[variant];
  const styles = toastStyles[variant];

  React.useEffect(() => {
    // Entrance animation
    const timer = setTimeout(() => setIsVisible(true), 50);
    
    // Auto-dismiss
    if (duration > 0) {
      const dismissTimer = setTimeout(() => {
        handleClose();
      }, duration);
      
      return () => {
        clearTimeout(timer);
        clearTimeout(dismissTimer);
      };
    }
    
    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose?.();
    }, 300);
  };

  const getIconAnimation = () => {
    switch (variant) {
      case "success":
        return "toast-success-icon";
      case "error":
        return "toast-error-icon";
      default:
        return "toast-icon";
    }
  };

  return (
    <div
      className={cn(
        "toast-card pointer-events-auto relative flex w-full max-w-[440px] items-start gap-4 overflow-hidden rounded-2xl p-5 shadow-2xl backdrop-blur-xl border font-montserrat transition-all duration-500 ease-out hover:shadow-[0px_4px_62.1px_0px_#E649A21F] hover:border-light-purple",
        styles.container,
        isVisible && !isExiting
          ? "opacity-100 translate-x-0 scale-100"
          : "opacity-0 translate-x-full scale-95",
        isExiting && "animate-out slide-out-to-right-full"
      )}
    >
      {/* Icon */}
      <div className={cn("flex-shrink-0 mt-0.5", getIconAnimation())}>
        <Icon className={cn("h-5 w-5", styles.icon)} />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="text-base font-semibold text-white font-montserrat tracking-wide leading-tight">
          {title}
        </div>
        {description && (
          <div className="text-sm text-white/80 mt-1 font-montserrat leading-relaxed">
            {description}
          </div>
        )}
        
        {/* Action Button */}
        {action && (
          <button
            onClick={action.onClick}
            className="inline-flex h-8 items-center justify-center rounded-lg bg-gradient-to-r from-custom-purple to-custom-pink text-white px-3 text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 focus:outline-none focus:ring-2 focus:ring-custom-purple/50 mt-3"
          >
            {action.label}
          </button>
        )}
      </div>

      {/* Close Button */}
      <button
        onClick={handleClose}
        className="absolute right-4 top-4 rounded-full p-2 text-white/60 hover:text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-custom-purple/50 transition-all duration-300 hover:scale-110 hover:rotate-90"
      >
        <X className="h-4 w-4" />
      </button>

      {/* Progress Bar */}
      {duration > 0 && (
        <div
          className={cn(
            "toast-progress absolute bottom-0 left-0 h-0.5 bg-gradient-to-r rounded-b-2xl",
            `bg-gradient-to-r ${styles.progress}`
          )}
          style={{
            animationDuration: `${duration}ms`,
          }}
        />
      )}

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/0 to-blue-500/0 hover:from-purple-500/5 hover:to-blue-500/5 transition-all duration-500 pointer-events-none" />
    </div>
  );
};

// Toast Provider Component
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = React.useState<Array<EnhancedToastProps & { id: string }>>([]);

  const addToast = React.useCallback((toast: Omit<EnhancedToastProps, "onClose">) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = {
      ...toast,
      id,
      onClose: () => {
        setToasts(prev => prev.filter(t => t.id !== id));
      },
    };

    setToasts(prev => [newToast, ...prev]);

    return id;
  }, []);

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  const success = React.useCallback((title: string, description?: string) => {
    return addToast({ title, description, variant: "success" });
  }, [addToast]);

  const error = React.useCallback((title: string, description?: string) => {
    return addToast({ title, description, variant: "error" });
  }, [addToast]);

  const warning = React.useCallback((title: string, description?: string) => {
    return addToast({ title, description, variant: "warning" });
  }, [addToast]);

  const info = React.useCallback((title: string, description?: string) => {
    return addToast({ title, description, variant: "info" });
  }, [addToast]);

  const contextValue = React.useMemo(() => ({
    addToast,
    removeToast,
    success,
    error,
    warning,
    info,
  }), [addToast, removeToast, success, error, warning, info]);

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      {/* Toast Container */}
      {toasts.length > 0 && (
        <div className="fixed top-6 right-6 z-[100] flex max-h-screen w-full flex-col gap-4 sm:max-w-[440px] pointer-events-none">
          {toasts.map((toast) => (
            <EnhancedToast key={toast.id} {...toast} />
          ))}
        </div>
      )}
    </ToastContext.Provider>
  );
};

// Toast Hook
export const useEnhancedToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error("useEnhancedToast must be used within a ToastProvider");
  }
  return context;
};

// Legacy ToastContainer for backward compatibility
export const ToastContainer: React.FC = () => {
  return null;
};