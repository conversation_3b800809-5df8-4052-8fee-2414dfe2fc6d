import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";
import {response} from "express";

export async function POST(request: NextRequest) {
    const {id, type, reason, details} = await request.json();

    if (!id || !type || !reason) {
        return NextResponse.json({
            status: HttpStatusCode.UnprocessableEntity,
            message: "No valid id, type or reason provided."
        });
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No user session found."});
    }

    const reportResponse = await fetch(process.env.VIDEO_API_URL + "/reports/create", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.API_SERVER_KEY!,
            "Authorization": "Bearer " + userSession.accessToken,
        },
        body: JSON.stringify({
            objectId: id,
            reason,
            objectType: type,
            details
        })
    });

    const data = await reportResponse.json();
    if (!reportResponse.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok});
}