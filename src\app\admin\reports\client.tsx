"use client";

import { Suspense } from "react";
import ReportsTable from "./_components/reports-table";
import { CommentReportsTable } from "./_components/comment-reports-table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Flag, FileText, MessageSquare } from "lucide-react";

type Report = {
  id: string;
  reportedUser: string;
  reportedUserId: string;
  reportReason: string;
  createdAt: string;
  status: string;
  moderator: string;
  shortLink: string;
  videoId: string;
  details: string | null;
  contentType: "video" | "image";
};

interface ReportsPageClientProps {
  reports: Report[];
  commentReports: any[];
  currentUser: any;
}

export default function ReportsPageClient({
  reports,
  commentReports,
  currentUser,
}: ReportsPageClientProps) {
  const contentReportsStats = {
    total: reports.length,
    open: reports.filter((r) => r.status === "OPEN").length,
    pending: reports.filter((r) => r.status === "PENDING").length,
    videos: reports.filter((r) => r.contentType === "video").length,
    images: reports.filter((r) => r.contentType === "image").length,
  };

  const commentReportsStats = {
    total: commentReports.length,
    open: commentReports.filter((r) => r.status === "OPEN").length,
    pending: commentReports.filter((r) => r.status === "PENDING").length,
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-red-500/20 to-orange-500/20 border border-red-500/30">
            <Flag className="h-4 w-4 text-red-400" />
          </div>
          <h1 className="text-xl font-bold text-white">Report Management</h1>
        </div>
        <p className="text-sm text-gray-400">
          Monitor and manage user reports for content moderation
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500/20 border border-blue-500/30">
              <FileText className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Content Reports</p>
              <p className="text-xl font-bold text-white">
                {contentReportsStats.total}
              </p>
            </div>
          </div>
          <div className="mt-3 flex items-center gap-4 text-xs">
            <span className="text-red-400">
              Open: {contentReportsStats.open}
            </span>
            <span className="text-yellow-400">
              Pending: {contentReportsStats.pending}
            </span>
          </div>
        </div>

        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500/20 border border-purple-500/30">
              <MessageSquare className="h-5 w-5 text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Comment Reports</p>
              <p className="text-xl font-bold text-white">
                {commentReportsStats.total}
              </p>
            </div>
          </div>
          <div className="mt-3 flex items-center gap-4 text-xs">
            <span className="text-red-400">
              Open: {commentReportsStats.open}
            </span>
            <span className="text-yellow-400">
              Pending: {commentReportsStats.pending}
            </span>
          </div>
        </div>

        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500/20 border border-green-500/30">
              <svg
                className="h-5 w-5 text-green-400"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14,2 14,8 20,8" />
                <line x1="16" y1="13" x2="8" y2="13" />
                <line x1="16" y1="17" x2="8" y2="17" />
                <polyline points="10,9 9,9 8,9" />
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-400">Video Reports</p>
              <p className="text-xl font-bold text-white">
                {contentReportsStats.videos}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-pink-500/20 border border-pink-500/30">
              <svg
                className="h-5 w-5 text-pink-400"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                <circle cx="9" cy="9" r="2" />
                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
              </svg>
            </div>
            <div>
              <p className="text-sm text-gray-400">Image Reports</p>
              <p className="text-xl font-bold text-white">
                {contentReportsStats.images}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Reports Content */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <Tabs defaultValue="content" className="w-full">
          <TabsList className="w-full justify-start overflow-x-auto mb-6 bg-gray-900/30 border border-gray-800/40 h-11 rounded-lg p-1">
            <TabsTrigger
              value="content"
              className="flex items-center gap-2 text-sm data-[state=active]:bg-gray-700/60 data-[state=active]:text-white data-[state=active]:shadow-sm text-gray-400 hover:text-gray-300 px-4 py-2 rounded-md transition-all duration-200"
            >
              <FileText className="h-4 w-4" />
              Content Reports
              <span className="bg-gray-700 text-gray-300 px-2 py-0.5 rounded-full text-xs ml-1">
                {contentReportsStats.total}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="comments"
              className="flex items-center gap-2 text-sm data-[state=active]:bg-gray-700/60 data-[state=active]:text-white data-[state=active]:shadow-sm text-gray-400 hover:text-gray-300 px-4 py-2 rounded-md transition-all duration-200"
            >
              <MessageSquare className="h-4 w-4" />
              Comment Reports
              <span className="bg-gray-700 text-gray-300 px-2 py-0.5 rounded-full text-xs ml-1">
                {commentReportsStats.total}
              </span>
            </TabsTrigger>
          </TabsList>

          <div className="mt-4">
            <TabsContent value="content" className="m-0">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center py-12">
                    <div className="flex items-center gap-2 text-gray-400">
                      <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">
                        Loading content reports...
                      </span>
                    </div>
                  </div>
                }
              >
                <ReportsTable reports={reports} />
              </Suspense>
            </TabsContent>

            <TabsContent value="comments" className="m-0">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center py-12">
                    <div className="flex items-center gap-2 text-gray-400">
                      <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">
                        Loading comment reports...
                      </span>
                    </div>
                  </div>
                }
              >
                <CommentReportsTable
                  currentUser={currentUser}
                  initCommentReports={commentReports}
                />
              </Suspense>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}