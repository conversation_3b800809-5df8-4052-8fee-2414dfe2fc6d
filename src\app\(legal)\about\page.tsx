"use client";

import { motion } from "framer-motion";
import {
  Lightbulb,
  Shield,
  Zap,
  Clock,
  Users,
  Target,
  ArrowRight,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const values = [
  {
    icon: Lightbulb,
    title: "Simplicity",
    description:
      "We believe in making technology accessible to everyone, regardless of technical expertise.",
    gradient: "from-amber-400 to-orange-500",
  },
  {
    icon: Shield,
    title: "Security",
    description:
      "Your content's security is our top priority. We use the latest measures to protect your videos.",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    icon: Zap,
    title: "Innovation",
    description:
      "We constantly push the boundaries of what's possible in video hosting and streaming.",
    gradient: "from-purple-400 to-pink-500",
  },
  {
    icon: Clock,
    title: "Reliability",
    description:
      "Our infrastructure is built for 99.9% uptime, ensuring your content is always accessible.",
    gradient: "from-blue-400 to-indigo-500",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function AboutPage() {
  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              About StreamBliss
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Revolutionizing
          <br />
          Video Hosting
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          We&apos;re building the future of content creation and sharing,
          <br className="hidden md:block" />
          one innovative feature at a time.
        </p>
      </motion.div>

      {/* Mission & Story Cards */}
      <motion.div
        className="grid gap-8 lg:gap-12 md:grid-cols-2"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <motion.div
          variants={itemVariants}
          className="group relative"
          data-aos="fade-right"
          data-aos-duration="800"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-10 h-full transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 mb-6 group-hover:scale-110 transition-transform duration-300">
              <Target className="h-8 w-8 text-purple-400" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Our Mission
            </h2>
            <p className="text-gray-300 text-lg leading-relaxed">
              To make video hosting and sharing as simple and secure as
              possible. We believe that everyone should have access to reliable,
              high-quality video streaming services without compromise.
            </p>
            <div className="flex items-center mt-6 text-purple-400 group-hover:text-purple-300 transition-colors">
              <span className="text-sm font-medium">Learn more</span>
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </div>
          </div>
        </motion.div>
        <motion.div
          variants={itemVariants}
          className="group relative"
          data-aos="fade-left"
          data-aos-duration="800"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-10 h-full transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-pink-500/20 to-purple-500/20 mb-6 group-hover:scale-110 transition-transform duration-300">
              <Users className="h-8 w-8 text-pink-400" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Our Story
            </h2>
            <p className="text-gray-300 text-lg leading-relaxed">
              Founded in 2023, StreamBliss emerged from a simple idea: video
              hosting shouldn&apos;t be complicated. What started as a personal
              project has grown into a platform trusted by creators worldwide.
            </p>
            <div className="flex items-center mt-6 text-pink-400 group-hover:text-pink-300 transition-colors">
              <span className="text-sm font-medium">Our journey</span>
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </div>
          </div>
        </motion.div>
      </motion.div>
      <div className="space-y-12">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-block mb-4">
            <div className="px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20">
              <span className="text-sm font-medium text-purple-400">
                What Drives Us
              </span>
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
            Our Core Values
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            The principles that guide everything we do
          </p>
        </motion.div>

        <motion.div
          className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              variants={itemVariants}
              className="group text-center"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="relative mb-6">
                <div
                  className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"
                  style={{
                    background: `linear-gradient(to right, var(--tw-gradient-stops))`,
                  }}
                />
                <div
                  className={`relative mx-auto flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-r ${value.gradient} bg-opacity-10 backdrop-blur-sm border border-white/10 group-hover:scale-110 group-hover:border-white/20 transition-all duration-300`}
                >
                  <value.icon className="h-10 w-10 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-gray-100 transition-colors">
                {value.title}
              </h3>
              <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors">
                {value.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* CTA Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-12 md:p-16 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
              Join Our Journey
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              We&apos;re excited to build the future of video hosting together.
              <br className="hidden md:block" />
              Ready to be part of something amazing?
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-8 py-3 rounded-full text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
            >
              <Link href="/contact" className="inline-flex items-center">
                Get in Touch
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}