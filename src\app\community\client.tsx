"use client";

import { useState, useEffect } from "react";
import { VideoMasonryGrid } from "./_components/VideoMasonryGrid";
import { VideoModal } from "./_components/video-modal";
import { FeaturedVideoHero } from "./_components/featured-video-hero";
import type { Video } from "@/types/video";
import { useRouter } from "next/navigation";

type CommunityClientProps = {
  videos: Video[];
  children?: React.ReactNode;
};

export function CommunityClient({ videos, children }: CommunityClientProps) {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const router = useRouter();

  const topVideo = videos.length > 0 ? videos[0] : null;
  const restVideos = videos.length > 0 ? videos.slice(1) : [];

  // Enable sound after first user interaction
  useEffect(() => {
    const enableSound = () => {
      setSoundEnabled(true);
      // Store in sessionStorage so we don't ask again this session
      sessionStorage.setItem("soundEnabled", "true");
    };

    // Check if sound was already enabled this session
    if (sessionStorage.getItem("soundEnabled") === "true") {
      setSoundEnabled(true);
    } else {
      const events = ["click", "touchstart", "keydown"];
      events.forEach((event) => {
        document.addEventListener(event, enableSound, { once: true });
      });

      return () => {
        events.forEach((event) => {
          document.removeEventListener(event, enableSound);
        });
      };
    }
  }, []);

  const handlePlayVideo = (video: Video) => {
    router.push(`/v/${video.shortLink}`);
  };

  return (
    <>
      {/* Header */}
      {children}

      {/* Sound Enable Notification */}
      {!soundEnabled && (
        <div className="fixed top-4 right-4 z-50 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium animate-pulse">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
            </svg>
            Click anywhere to enable sound
          </div>
        </div>
      )}

      {/* Featured video hero section */}
      {topVideo && <FeaturedVideoHero video={topVideo} />}

      {/* Content sections */}
      <main className="relative z-10">
        <div className="container mx-auto px-4 py-12">
          <div className="mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 font-montserrat">
              <span className="bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                Discover Amazing Content
              </span>
            </h2>
            <p className="text-slate-400 text-lg text-center max-w-2xl mx-auto">
              Explore the latest videos from our creative community
            </p>
          </div>

          {/* Video Grid */}
          <VideoMasonryGrid onPlayVideo={handlePlayVideo} videos={restVideos} />
        </div>
      </main>

      {/* Video Modal - Only for special cases if needed */}
      {selectedVideo && (
        <VideoModal
          isOpen={isVideoModalOpen}
          onClose={() => {
            setIsVideoModalOpen(false);
            setSelectedVideo(null);
          }}
          video={selectedVideo}
        />
      )}
    </>
  );
}