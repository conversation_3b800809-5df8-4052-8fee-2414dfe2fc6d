import { Suspense } from "react";
import CommunityApprovalClient from "./client";
import { getCommunityVideosForApproval } from "@/server/video";
import { getAdminUser } from "@/server/session";
import { VideoIcon } from "lucide-react";

export default async function CommunityApprovalPage() {
  const user = await getAdminUser(["ADMIN_APPROVAL_PAGE"]);
  const videos = await getCommunityVideosForApproval();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-800/60">
            <VideoIcon className="h-4 w-4 text-gray-300" />
          </div>
          <h1 className="text-xl font-bold text-white">Community Videos</h1>
        </div>
        <p className="text-sm text-gray-400">
          Manage and approve community videos for public display
        </p>
      </div>

      {/* Videos Content */}
      <Suspense
        fallback={
          <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-8">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2 text-gray-400">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Loading videos...</span>
              </div>
            </div>
          </div>
        }
      >
        <CommunityApprovalClient videos={videos} />
      </Suspense>
    </div>
  );
}