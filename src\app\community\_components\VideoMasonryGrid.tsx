"use client";

import { useState, useEffect, useMemo } from "react";
import { VideoCard } from "./video-card";
import type { Video } from "@/types/video";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";

interface VideoMasonryGridProps {
  videos: Video[];
  hideUploader?: boolean;
  onPlayVideo: (video: Video) => void;
}

type VideoSize = "normal" | "large" | "wide" | "tall";

interface ProcessedVideo extends Video {
  size: VideoSize;
}

export function VideoMasonryGrid({
  videos,
  hideUploader = false,
  onPlayVideo,
}: VideoMasonryGridProps) {
  const [columnCount, setColumnCount] = useState(4);
  const [visibleCount, setVisibleCount] = useState(12);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 640) setColumnCount(1);
      else if (width < 960) setColumnCount(2);
      else if (width < 1366) setColumnCount(3);
      else if (width < 1680) setColumnCount(4);
      else setColumnCount(5);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const processedVideos = useMemo(() => {
    if (!videos.length) return [];

    const videosWithSizes = [...videos].map((video, index) => {
      const position = index % 12;

      let size: VideoSize = "normal";

      if (position === 0 || position === 5) size = "large";
      else if (position === 2 || position === 7) size = "wide";
      else if (position === 4 || position === 9) size = "tall";
      else size = "normal";

      return { ...video, size };
    });

    if (videosWithSizes.length > 0) {
      videosWithSizes[0].size = "large";
    }

    return videosWithSizes;
  }, [videos]);

  const visibleVideos = useMemo(() => {
    return processedVideos.slice(0, visibleCount);
  }, [processedVideos, visibleCount]);

  const hasMore = visibleCount < videos.length;

  if (!videos.length) {
    return (
      <div className="text-center py-16">
        <div className="mb-4">
          <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center mb-6">
            <svg
              className="w-12 h-12 text-purple-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
              />
            </svg>
          </div>
        </div>
        <h3 className="text-2xl font-bold text-white mb-2 font-montserrat">
          No videos found
        </h3>
        <p className="text-lg text-zinc-400">
          Check back later for new content from our community
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Masonry Grid */}
      <div
        className="grid gap-4 auto-rows-[minmax(200px,auto)]"
        style={{
          gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
          gridAutoFlow: "dense",
        }}
      >
        {visibleVideos.map((video, index) => {
          let colSpan = 1;
          let rowSpan = 1;

          switch (video.size) {
            case "large":
              colSpan = columnCount === 1 ? 1 : Math.min(2, columnCount);
              rowSpan = 2;
              break;
            case "wide":
              colSpan = columnCount === 1 ? 1 : Math.min(2, columnCount);
              rowSpan = 1;
              break;
            case "tall":
              colSpan = 1;
              rowSpan = columnCount === 1 ? 1 : 2;
              break;
            default:
              colSpan = 1;
              rowSpan = 1;
          }

          return (
            <div
              key={video.id}
              className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
              style={{
                gridColumn: `span ${colSpan}`,
                gridRow: `span ${rowSpan}`,
                minHeight:
                  video.size === "large"
                    ? "400px"
                    : video.size === "tall"
                      ? "350px"
                      : "200px",
              }}
            >
              <VideoCard
                video={video}
                hideUploader={hideUploader}
                onClick={onPlayVideo}
                size={video.size}
              />
            </div>
          );
        })}
      </div>

      {/* Load More Section */}
      {hasMore && (
        <div className="mt-12 text-center">
          <div className="w-full py-8 flex justify-center">
            <Button
              onClick={() =>
                setVisibleCount((prev) => Math.min(prev + 8, videos.length))
              }
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              Load More Videos ({videos.length - visibleCount} remaining)
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}