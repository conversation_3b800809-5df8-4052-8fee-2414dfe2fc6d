"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, RefreshCw, Shield, Smartphone, QrCode, X } from "lucide-react"
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import NextImage from "next/image"
import { <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"

interface TwoFactorSetupProps {
  onComplete: (success: boolean) => void
  onClose?: () => void
  userId: string
}

export function TwoFactorSetup({ onComplete, onClose, userId }: TwoFactorSetupProps) {
  const { success, error } = useEnhancedToast();
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [verificationCode, setVerificationCode] = useState(["", "", "", "", "", ""])
  const [isLoading, setIsLoading] = useState(true)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isGeneratingNew, setIsGeneratingNew] = useState(false)
  const [setupError, setSetupError] = useState<string | null>(null)
  const [step, setStep] = useState<'setup' | 'verify'>('setup')
  const inputRefs = useRef<HTMLInputElement[]>([])

  const fetchQrCode = async () => {
    try {
      setIsLoading(true)
      setSetupError(null)

      // First, cleanup any existing 2FA setup
      const cleanupResponse = await fetch("/api/user/twofactor/cleanup", {
        method: "POST"
      });

      // Then generate new QR code
      const response = await fetch("/api/user/twofactor/generate", {
        method: "POST"
      })

      const data = await response.json()

      if (!response.ok || data.status === 500) {
        const errorMessage = data.message || "Unknown error occurred";
        setSetupError(`Backend Error: ${errorMessage}`);
        error("2FA Setup Failed", errorMessage);
        return;
      }

      if (!data.data) {
        setSetupError("No QR code data received from server.");
        return;
      }

      const qrCodeSrc = data.data;

      if (qrCodeSrc && qrCodeSrc.startsWith('data:image/')) {
        setQrCode(qrCodeSrc);
      } else {
        setSetupError("Invalid QR code format received from server.");
      }
    } catch (error) {
      setSetupError("Failed to connect to 2FA service. Please check your connection and try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewCode = async () => {
    setIsGeneratingNew(true)
    setVerificationCode(["", "", "", "", "", ""])
    await fetchQrCode()
    setIsGeneratingNew(false)
  }

  useEffect(() => {
    fetchQrCode()
  }, [userId])

  const handleInputChange = (index: number, value: string) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) return

    const newCode = [...verificationCode]
    newCode[index] = value

    setVerificationCode(newCode)

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData("text").slice(0, 6)
    const newCode = [...verificationCode]

    pastedData.split("").forEach((char, index) => {
      if (index < 6 && /^\d*$/.test(char)) {
        newCode[index] = char
      }
    })

    setVerificationCode(newCode)

    const nextEmptyIndex = newCode.findIndex((digit) => !digit)
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus()
    } else {
      inputRefs.current[5]?.focus()
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsVerifying(true)
    setSetupError(null)

    try {
      const code = verificationCode.join("")
      const response = await fetch("/api/user/twofactor/verify", {
        method: "POST",
        body: JSON.stringify({
          token: code
        })
      })

      const data = await response.json()
      if (!response.ok) {
        error("Verification Failed", data.message);
        setVerificationCode(["", "", "", "", "", ""])
        inputRefs.current[0]?.focus()
        return;
      }

      success("2FA Enabled", "Two-factor authentication has been successfully enabled for your account.");
      onComplete(true)
    } catch (error) {
      setSetupError("Failed to verify code. Please try again.")
      setVerificationCode(["", "", "", "", "", ""])
      inputRefs.current[0]?.focus()
    } finally {
      setIsVerifying(false)
    }
  }

  return (
    <div className="w-full">
      {/* Header */}
      <DialogHeader className="text-center space-y-3 pb-4">
        <div className="mx-auto w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-2">
          <Shield className="w-6 h-6 text-white" />
        </div>
        <DialogTitle className="text-xl font-bold text-white">
          Enable Two-Factor Authentication
        </DialogTitle>
        <p className="text-white/70 text-sm">
          Add an extra layer of security to your account.
        </p>
      </DialogHeader>



      {/* Error State */}
      {setupError && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-center gap-3">
          <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0">
            <X className="w-3 h-3" />
          </div>
          <span>{setupError}</span>
          <Button
            onClick={generateNewCode}
            variant="ghost"
            size="sm"
            className="ml-auto text-red-400 hover:text-red-300 hover:bg-red-500/10"
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-8 space-y-3">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-purple-500/20 border-t-purple-500 rounded-full animate-spin"></div>
            <QrCode className="w-5 h-5 text-purple-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="text-white/70 text-sm">Generating your QR code...</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Step 1: QR Code */}
          <div className="space-y-4">
            <div className="flex items-center gap-3 text-white">
              <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-xs font-bold">
                1
              </div>
              <div>
                <h3 className="font-semibold">Scan QR Code</h3>
                <p className="text-white/70 text-xs">Use your authenticator app</p>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white p-4 rounded-xl mx-auto w-fit">
                {qrCode ? (
                  <img
                    src={qrCode}
                    alt="2FA QR Code"
                    className="w-40 h-40"
                    onError={(e) => {
                      console.error("QR Code image failed to load:", e);
                      console.log("QR Code src:", qrCode);
                    }}
                  />
                ) : (
                  <div className="w-40 h-40 bg-gray-100 rounded flex items-center justify-center">
                    <QrCode className="w-12 h-12 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Generate New Code Button */}
              <div className="flex justify-center mt-3">
                <Button
                  onClick={generateNewCode}
                  disabled={isGeneratingNew}
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white hover:bg-white/10 text-xs"
                >
                  {isGeneratingNew ? (
                    <>
                      <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Generate New Code
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Smartphone className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-xs">
                  <p className="text-blue-400 font-medium mb-1">Recommended Apps:</p>
                  <p className="text-white/70">
                    Google Authenticator, Authy, Microsoft Authenticator
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Step 2: Verification */}
          <div className="space-y-4">
            <div className="flex items-center gap-3 text-white">
              <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-xs font-bold">
                2
              </div>
              <div>
                <h3 className="font-semibold">Enter Verification Code</h3>
                <p className="text-white/70 text-xs">Enter the 6-digit code</p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="flex justify-center gap-2">
                {verificationCode.map((digit, index) => (
                  <Input
                    key={index}
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleInputChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    onPaste={handlePaste}
                    className="w-10 h-10 text-center text-lg bg-[#0A0A0F] border border-white/20 hover:border-purple-400/50
                              text-white rounded-lg focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500
                              transition-all duration-200"
                    ref={(el) => {
                      if (el) {
                        inputRefs.current[index] = el
                      }
                    }}
                  />
                ))}
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  onClick={onClose}
                  variant="ghost"
                  className="flex-1 h-10 text-white/70 hover:text-white hover:bg-white/10 border border-white/20 text-sm"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600
                           text-white font-semibold h-10 text-sm shadow-lg shadow-purple-500/25 transition-all duration-200
                           disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isVerifying || verificationCode.some((digit) => !digit)}
                >
                  {isVerifying ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <Shield className="mr-2 h-4 w-4" />
                      Enable 2FA
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}