"use client";

import { Check } from "lucide-react";
import { useState, useEffect, useRef } from "react";

export type SortOption = "date" | "duration" | "views" | "name";

interface SortPopupProps {
  isOpen: boolean;
  onClose: () => void;
  currentSort: SortOption;
  onSortChange: (sort: SortOption) => void;
  triggerRef?: React.RefObject<HTMLElement | null>;
}

const sortOptions: { value: SortOption; label: string }[] = [
  { value: "date", label: "Date" },
  { value: "duration", label: "Duration" },
  { value: "views", label: "Views" },
  { value: "name", label: "Name" },
];

export function SortPopup({
  isOpen,
  onClose,
  currentSort,
  onSortChange,
  triggerRef,
}: SortPopupProps) {
  const popupRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    if (isOpen && triggerRef?.current && popupRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const popupRect = popupRef.current.getBoundingClientRect();

      let top = triggerRect.bottom + 8;
      let left = triggerRect.left;

      const screenPadding = window.innerWidth < 640 ? 16 : 8;

      if (left + popupRect.width > window.innerWidth - screenPadding) {
        left = window.innerWidth - popupRect.width - screenPadding;
      }

      if (left < screenPadding) {
        left = screenPadding;
      }

      if (top + popupRect.height > window.innerHeight - screenPadding) {
        top = triggerRect.top - popupRect.height - 8;
      }

      setPosition({ top, left });
    }
  }, [isOpen, triggerRef]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const CheckboxIcon = ({ checked }: { checked: boolean }) => (
    <div className="relative w-[19.513px] h-[19.513px]">
      <svg
        width="20"
        height="21"
        viewBox="0 0 20 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        <path
          d="M15.6954 0.743652H3.81733C1.70908 0.743652 0 2.45273 0 4.56099V16.439C0 18.5473 1.70908 20.2563 3.81733 20.2563H15.6954C17.8036 20.2563 19.5127 18.5473 19.5127 16.439V4.56099C19.5127 2.45273 17.8036 0.743652 15.6954 0.743652Z"
          fill="url(#paint0_linear)"
          fillOpacity="0.2"
        />
        <path
          d="M15.6955 0.938843H3.81752C1.81703 0.938843 0.195312 2.56056 0.195312 4.56105V16.4391C0.195312 18.4396 1.81703 20.0613 3.81752 20.0613H15.6955C17.696 20.0613 19.3178 18.4396 19.3178 16.4391V4.56105C19.3178 2.56056 17.696 0.938843 15.6955 0.938843Z"
          stroke="url(#paint1_linear)"
          strokeWidth="0.390254"
        />
        {checked && (
          <path
            d="M6.02197 11.0336L8.51187 13.5235L13.4917 8.18802"
            stroke="white"
            strokeWidth="1.0671"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        )}
        <defs>
          <linearGradient
            id="paint0_linear"
            x1="15.3949"
            y1="20.2563"
            x2="1.19294"
            y2="2.5382"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.161" stopColor="white" />
            <stop offset="0.781" stopColor="white" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint1_linear"
            x1="13.0737"
            y1="-4.71984"
            x2="-1.17058"
            y2="26.5005"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.161" stopColor="white" />
            <stop offset="0.781" stopColor="white" stopOpacity="0" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40" onClick={onClose} />

      {/* Popup */}
      <div
        ref={popupRef}
        className="fixed z-50 w-[152px] sm:w-[152px] p-[12px] sm:p-[14px] flex flex-col items-start gap-[8px] sm:gap-[10px] rounded-lg border border-white/24 bg-black/80 shadow-xl"
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
      >
        <div className="flex flex-col items-start gap-3 w-full">
          {sortOptions.map((option, index) => (
            <div key={option.value}>
              <button
                onClick={() => {
                  onSortChange(option.value);
                  onClose();
                }}
                className="flex items-center gap-[10px] w-full hover:bg-white/5 rounded transition-colors duration-200 group"
              >
                <CheckboxIcon checked={currentSort === option.value} />
                <span
                  className="text-white font-['Montserrat'] text-[18px] font-normal leading-[160%] group-hover:text-white/90 transition-colors duration-200"
                  style={{ lineHeight: "28.8px" }}
                >
                  {option.label}
                </span>
              </button>
              {index < sortOptions.length - 1 && (
                <div className="w-[124px] h-px bg-white/10 mt-3" />
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
}