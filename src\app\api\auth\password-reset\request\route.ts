import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getEmailSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {email} = await request.json();
    if (!email) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No email provided"});
    }

    const response = await fetch(process.env.VIDEO_API_URL + "/password-reset/request", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
        })
    });

    const data = await response.json();
    if (!response.ok) {
        return NextResponse.json({status: HttpStatusCode.BadRequest, message: data.message});
    }

    const emailSession = await getEmailSession();
    emailSession.email = email;
    await emailSession.save();

    return NextResponse.json({status: HttpStatusCode.Ok});
}