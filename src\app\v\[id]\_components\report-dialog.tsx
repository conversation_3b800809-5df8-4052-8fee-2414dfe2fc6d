"use client";

import {useState} from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>nt,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {Label} from "@/components/ui/label";
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group";
import {Textarea} from "@/components/ui/textarea";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import {submitReport} from "../_actions/submit-report";
import {Flag, AlertTriangle} from "lucide-react";

const reportReasons = [
    {id: "inappropriate", label: "Inappropriate content", icon: "🚫"},
    {id: "copyright", label: "Copyright violation", icon: "©️"},
    {id: "spam", label: "Spam or misleading", icon: "⚠️"},
    {id: "other", label: "Other", icon: "❓"},
];

interface ReportDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    videoId: string;
}

export function ReportDialog({
                                 open,
                                 onOpenChange,
                                 videoId,
                             }: ReportDialogProps) {
    const [reason, setReason] = useState("");
    const [details, setDetails] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { success, error } = useEnhancedToast();

    const handleSubmit = async () => {
        if (!reason) {
            error("Reason Required", "Please select a reason");
            return;
        }

        setIsSubmitting(true);
        try {
            const response = await fetch("/api/report/submit", {
                method: "POST",
                body: JSON.stringify({
                    id: videoId,
                    reason,
                    details,
                    type: 'video'
                }),
            })

            const data = await response.json();
            if (!(data.status >= 200 && data.status < 300)) {
                error("Report Failed", data.message);
                return;
            }

            success("Report Submitted", "Report submitted successfully");
            onOpenChange(false);
            setReason("");
            setDetails("");
        } catch (err) {
            error("Submit Error", "Failed to submit report");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent
                className="sm:max-w-lg bg-gradient-to-br from-gray-900 via-gray-900 to-purple-900/20 border border-purple-500/20 text-white shadow-2xl">
                <DialogHeader className="pb-4">
                    <div className="flex items-center gap-3">
                        <div
                            className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30">
                            <Flag className="h-5 w-5 text-red-400"/>
                        </div>
                        <div>
                            <DialogTitle className="text-xl font-semibold text-white">
                                Report Content
                            </DialogTitle>
                            <p className="text-sm text-gray-400 mt-1">
                                Help us maintain a safe community
                            </p>
                        </div>
                    </div>
                </DialogHeader>

                <div className="space-y-6">
                    <div className="space-y-4">
                        <Label className="text-sm font-medium text-gray-300">
                            What&apos;s the issue?
                        </Label>
                        <RadioGroup
                            value={reason}
                            onValueChange={setReason}
                            className="space-y-3"
                        >
                            {reportReasons.map((item) => (
                                <div
                                    key={item.id}
                                    className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-200 hover:bg-white/5 cursor-pointer ${
                                        reason === item.id
                                            ? "border-purple-500/50 bg-gradient-to-r from-purple-500/10 to-pink-500/10"
                                            : "border-gray-700 hover:border-gray-600"
                                    }`}
                                    onClick={() => setReason(item.id)}
                                >
                                    <RadioGroupItem
                                        value={item.id}
                                        id={item.id}
                                        className="border-gray-600 text-purple-400 focus:ring-purple-500"
                                    />
                                    <span className="text-lg">{item.icon}</span>
                                    <Label
                                        htmlFor={item.id}
                                        className="text-sm text-gray-200 font-medium cursor-pointer flex-1"
                                    >
                                        {item.label}
                                    </Label>
                                </div>
                            ))}
                        </RadioGroup>
                    </div>

                    <div className="space-y-3">
                        <Label
                            htmlFor="details"
                            className="text-sm font-medium text-gray-300"
                        >
                            Additional details{" "}
                            <span className="text-gray-500">(optional)</span>
                        </Label>
                        <Textarea
                            id="details"
                            placeholder="Please provide any additional context that would help us review this content..."
                            value={details}
                            onChange={(e) => setDetails(e.target.value)}
                            className="bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 min-h-[80px] focus:border-purple-500/50 focus:ring-purple-500/20 resize-none"
                        />
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-amber-400 flex-shrink-0"/>
                        <p className="text-xs text-amber-200">
                            False reports may result in action against your account. Only
                            report content that violates our guidelines.
                        </p>
                    </div>

                    <div className="flex justify-end gap-3 pt-2">
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            className="border-gray-600 text-gray-300 hover:bg-gray-800/50 hover:text-white"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            disabled={isSubmitting || !reason}
                            className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting ? (
                                <>
                                    <div
                                        className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Submitting...
                                </>
                            ) : (
                                <>
                                    <Flag className="h-4 w-4 mr-2"/>
                                    Submit Report
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}