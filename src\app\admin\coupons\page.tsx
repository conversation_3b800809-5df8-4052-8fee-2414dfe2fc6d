import { getAdminUser } from "@/server/session";
import { CouponsTable } from "./_components/coupons-table";
import { getCoupons } from "@/server/coupons";
import { TicketPercent, AlertTriangle } from "lucide-react";

export default async function CouponsPage() {
  const user = await getAdminUser(["COUPONS_PAGE"]);

  // Only allow role ID 2
  if (user.role.id !== 2) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="bg-red-600/20 border border-red-500/30 rounded-xl p-8 text-center">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-600/40 mx-auto mb-4">
            <AlertTriangle className="h-6 w-6 text-red-400" />
          </div>
          <h1 className="text-xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-red-300 text-sm">
            You don&apos;t have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  const coupons = await getCoupons();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-800/60">
            <TicketPercent className="h-4 w-4 text-gray-300" />
          </div>
          <h1 className="text-xl font-bold text-white">Coupon Management</h1>
        </div>
        <p className="text-sm text-gray-400">
          Manage and create discount coupons for users
        </p>
      </div>

      {/* Coupons Content */}
      <CouponsTable initialCoupons={coupons} />
    </div>
  );
}