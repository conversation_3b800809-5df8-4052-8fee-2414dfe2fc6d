"use client";

import { User, <PERSON> } from "lucide-react";
import { NewDashboardSidebar } from "./new-dashboard-sidebar";
import { MobileNav } from "./mobile-nav";
import { DashboardNotifications } from "./dashboard-notifications";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { $Enums } from "@prisma/client";

type Notification = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type: $Enums.NotificationType;
  data: string;
  read: boolean;
};

interface DashboardWrapperProps {
  userName: string;
  userImage?: string | null;
  children: React.ReactNode;
  className?: string;
  notifications?: Notification[];
  hasAccessToAdmin?: boolean;
}

export function DashboardWrapper({
  userName,
  userImage,
  children,
  className = "",
  notifications = [],
  hasAccessToAdmin = false,
}: DashboardWrapperProps) {
  const [avatarError, setAvatarError] = useState(false);
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black flex flex-col md:flex-row">
      <NewDashboardSidebar />

      {/* Mobile Header */}
      <div className="md:hidden flex items-center justify-between p-4 bg-black border-b border-white/10">
        <div className="flex items-center gap-3">
          <MobileNav userName={userName} userImage={userImage} />
          <Image
            src="/assets/images/svg/logo.svg"
            alt="StreamBliss"
            width={32}
            height={29}
            className="w-8 h-auto"
          />
          <span className="text-white font-semibold text-lg">StreamBliss</span>
        </div>
        <div className="flex items-center gap-3">
          {/* Mobile Notifications */}
          <DashboardNotifications notifications={notifications} />

          {/* Mobile Admin Button */}
          {hasAccessToAdmin && (
            <Button
              variant="ghost"
              size="icon"
              className="relative w-10 h-10 rounded-full bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
              onClick={() => router.push("/admin")}
            >
              <Shield className="h-5 w-5 text-white/70" />
            </Button>
          )}

          {userImage && userImage.trim() !== "" && !avatarError ? (
            <Image
              src={userImage}
              alt={userName || "Avatar"}
              width={32}
              height={32}
              className="h-8 w-8 object-cover rounded-full border border-white/10"
              onError={() => setAvatarError(true)}
            />
          ) : (
            <div className="w-8 h-8 rounded-full border border-white/10 bg-white/5 flex items-center justify-center">
              <User className="h-4 w-4 text-white/70" />
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 ml-0 lg:ml-[70px] overflow-x-hidden">
        <div
          className={`rounded-[16px] lg:rounded-[20px] bg-[rgba(184,81,223,0.06)] border border-[rgba(184,81,223,0.12)] m-2 lg:m-6 p-3 lg:p-6 min-h-[calc(100vh-80px)] lg:min-h-[calc(100vh-64px)] ${className}`}
        >
          {children}
        </div>
      </div>
    </div>
  );
}