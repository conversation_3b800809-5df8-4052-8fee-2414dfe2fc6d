import {NextRequest, NextResponse} from "next/server"
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {currentPassword, newPassword, confirmPassword} = await request.json();

    if (!currentPassword || !newPassword || !confirmPassword) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "Invalid data props."});
    }

    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "Invalid session or access token."});
    }

    const response = await fetch(process.env.VIDEO_API_URL + "/password-reset/update", {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.API_SERVER_KEY!,
            "Authorization": "Bearer " + userSession.accessToken
        },
        body: JSON.stringify({
            oldPassword: currentPassword,
            newPassword: newPassword,
        })
    });

    const data = await response.json();
    if (!response.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, success: true});
}