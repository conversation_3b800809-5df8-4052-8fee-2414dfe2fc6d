"use server";

import { prisma } from "@/lib/prisma";
import { getUserSession } from "@/server/session";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export async function restartTutorial() {
  const session = await getUserSession();

  if (!session?.userId) {
    throw new Error("Unauthorized");
  }

  try {
    await prisma.user.update({
      where: {
        id: session.userId,
      },
      data: {
        hasSeenTutorial: false,
      },
    });

    revalidatePath("/dashboard");
    return { success: true };
  } catch (error) {
    console.error("Error restarting tutorial:", error);
    throw new Error("Failed to restart tutorial");
  }
}