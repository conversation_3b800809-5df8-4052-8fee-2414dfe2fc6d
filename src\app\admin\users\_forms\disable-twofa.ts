"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { createNotification } from "@/server/notifications";
import { getAdminUser, getUser } from "@/server/session";
import { getTwoFaData, isTwoFaEnabled } from "@/server/twofa";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type Props = {
  userId: string;
};

export default async function submitDisableTwoFa({ userId }: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!userId) {
    return { success: false, message: "User ID is required" };
  }

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_DISABLE_2FA"]);
  if (!user) {
    return {
      success: false,
      message: "Not authorized to disable 2FA."
    }
  }

  const target = await getUserById(userId);
  if (!target) {
    return { success: false, message: "User not found." };
  }

  if (!target.UserSettings[0].twoFactorEnabled && !await isTwoFaEnabled(target.id)) {
    return { success: false, message: "2FA is already disabled." };
  }

  const twoFaData = await getTwoFaData(target.id);
  if (!twoFaData) {
    return { success: false, message: "2FA data not found." };
  }

  await prisma.userSettings.update({
    where: { id: twoFaData.id },
    data: {
      twoFactorEnabled: false
    }
  });

  await createNotification(target.id, "Your 2FA has been disabled by an admin.", "INFO");
  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_DISABLED_TWO_FACTOR, LogActions.ACCOUNT);

  return {
    success: true,
    message: "2FA has been disabled."
  }
}

