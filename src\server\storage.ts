"use server";

import { check } from "diskusage";

function formatBytes(bytes: number): string {
  const gb = bytes / 1024 ** 3;
  if (gb >= 1000) {
    const tb = bytes / 1024 ** 4;
    return `${tb.toFixed(2)} TB`;
  } else {
    return `${gb.toFixed(2)} GB`;
  }
}

export async function getDiskUsage(path: string = "/") {
  try {
    // Skip disk usage check during build/static generation
    if (process.env.NODE_ENV === "production" && !process.env.VERCEL) {
      return {
        available: "N/A",
        total: "N/A",
        used: "N/A",
      };
    }

    const { available, total } = await check(path);
    const used = total - available;

    return {
      available: formatBytes(available),
      total: formatBytes(total),
      used: formatBytes(used),
    };
  } catch (error) {
    console.error("Error checking disk usage:", error);
    return {
      available: "Error",
      total: "Error",
      used: "Error",
    };
  }
}