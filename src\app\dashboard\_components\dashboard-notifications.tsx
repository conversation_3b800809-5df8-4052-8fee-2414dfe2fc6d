"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Check, CheckCircle2, Al<PERSON><PERSON>riangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistance } from "date-fns";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitReadNotification from "../_actions/read-notification";
import submitReadAllNotifications from "../_actions/read-all-notifications";
import { $Enums } from "@prisma/client";

type Notification = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type: $Enums.NotificationType;
  data: string;
  read: boolean;
};

interface DashboardNotificationsProps {
  notifications: Notification[];
}

export function DashboardNotifications({
  notifications,
}: DashboardNotificationsProps) {
  const [markingAsRead, setMarkingAsRead] = useState<string | null>(null);
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false);
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const unreadCount = notifications.filter((n) => !n.read).length;

  const handleMarkAsRead = async (notificationId: string) => {
    setMarkingAsRead(notificationId);
    try {
      const result = await submitReadNotification({ notificationId });
      if (result.success) {
        router.refresh();
      } else {
        error("Mark as Read Failed", result.message || "Failed to mark notification as read");
      }
    } catch (err) {
      error("Mark as Read Error", "Failed to mark notification as read");
    } finally {
      setMarkingAsRead(null);
    }
  };

  const handleMarkAllAsRead = async () => {
    setMarkingAllAsRead(true);
    try {
      const result = await submitReadAllNotifications();
      if (result.success) {
        success("All Marked as Read", "All notifications marked as read");
        router.refresh();
      } else {
        error("Mark All Failed", result.message || "Failed to mark all notifications as read");
      }
    } catch (err) {
      error("Mark All Error", "Failed to mark all notifications as read");
    } finally {
      setMarkingAllAsRead(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative w-10 h-10 rounded-full bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 hover:shadow-lg hover:shadow-white/5 transition-all duration-300 group cursor-pointer"
        >
          <Bell
            className={`h-5 w-5 transition-all duration-300 ${
              unreadCount > 0
                ? "text-white animate-pulse"
                : "text-white/70 group-hover:text-white group-hover:scale-110"
            }`}
          />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-bold bg-gradient-to-r from-[#b851e0] to-[#eb489b] text-white rounded-full shadow-lg shadow-[#b851e0]/30 animate-pulse">
              {unreadCount > 9 ? "9+" : unreadCount}
            </span>
          )}
          {unreadCount > 0 && (
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#b851e0]/10 to-[#eb489b]/10 animate-pulse"></div>
          )}
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#b851e0]/0 to-[#eb489b]/0 group-hover:from-[#b851e0]/5 group-hover:to-[#eb489b]/5 transition-all duration-300"></div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-[calc(100vw-32px)] sm:w-[320px] max-w-[320px] border border-white/24 bg-[#110018] shadow-2xl shadow-black/50 rounded-xl overflow-hidden font-['Montserrat'] p-0"
      >
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-white/12">
          <h3 className="text-white text-lg font-semibold">Notifications</h3>
          <div className="flex items-center gap-3">
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                disabled={markingAllAsRead}
                className="flex items-center gap-1.5 text-white/60 hover:text-white text-xs font-medium transition-colors duration-200 disabled:opacity-50 hover:bg-white/5 px-2 py-1 rounded"
              >
                <Check className="h-4 w-4 flex-shrink-0" />
                {markingAllAsRead ? "Marking..." : "Mark all read"}
              </button>
            )}
            {unreadCount > 0 && (
              <div className="flex items-center gap-1.5 whitespace-nowrap">
                <div className="w-2 h-2 bg-[#b851e0] rounded-full animate-pulse flex-shrink-0"></div>
                <span className="text-white/70 text-sm font-medium">
                  {unreadCount} new
                </span>
              </div>
            )}
          </div>
        </div>
        {/* Notifications List */}
        <div className="max-h-[400px] overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-white/5 border border-white/10 flex items-center justify-center">
                <Bell className="h-6 w-6 text-white/40" />
              </div>
              <h4 className="text-white text-sm font-medium mb-1">
                No notifications yet
              </h4>
              <p className="text-white/60 text-xs">
                You&apos;ll see notifications here when they arrive
              </p>
            </div>
          ) : (
            <div className="py-2">
              {[...notifications]
                .sort(
                  (a, b) =>
                    new Date(b.createdAt).getTime() -
                    new Date(a.createdAt).getTime(),
                )
                .map((notification, index) => (
                  <div
                    key={notification.id}
                    onClick={() =>
                      !notification.read && handleMarkAsRead(notification.id)
                    }
                    className={`relative px-4 py-4 transition-all duration-200 group ${
                      !notification.read
                        ? "bg-white/[0.02] hover:bg-white/[0.04] cursor-pointer"
                        : "hover:bg-white/[0.02] cursor-default"
                    } ${index < notifications.length - 1 ? "border-b border-white/8" : ""} ${
                      markingAsRead === notification.id
                        ? "opacity-60 pointer-events-none"
                        : ""
                    }`}
                  >
                    {/* Unread indicator - positioned correctly on the left edge */}
                    {!notification.read && (
                      <div className="absolute left-0 top-4 bottom-4 w-1 bg-gradient-to-b from-[#b851e0] to-[#eb489b] rounded-r-full"></div>
                    )}

                    <div
                      className={`flex items-start gap-3 ${!notification.read ? "pl-3" : ""}`}
                    >
                      {/* Notification Icon */}
                      <div
                        className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                          !notification.read
                            ? "bg-gradient-to-br from-[#b851e0]/20 to-[#eb489b]/20 border border-[#b851e0]/30"
                            : "bg-white/5 border border-white/10"
                        }`}
                      >
                        {notification.type === "INFO" && (
                          <Check
                            className={`h-5 w-5 ${!notification.read ? "text-[#b851e0]" : "text-white/60"}`}
                          />
                        )}
                        {notification.type === "WARNING" && (
                          <Bell
                            className={`h-5 w-5 ${!notification.read ? "text-[#b851e0]" : "text-white/60"}`}
                          />
                        )}
                        {notification.type === "ERROR" && (
                          <Bell
                            className={`h-5 w-5 ${!notification.read ? "text-[#b851e0]" : "text-white/60"}`}
                          />
                        )}
                        {notification.type === "REPORT" && (
                          <AlertTriangle
                            className={`h-5 w-5 ${!notification.read ? "text-[#b851e0]" : "text-white/60"}`}
                          />
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2 mb-1">
                          <p
                            className={`text-sm font-medium leading-snug ${
                              !notification.read
                                ? "text-white"
                                : "text-white/80"
                            }`}
                          >
                            {notification.data}
                          </p>
                        </div>

                        <div className="flex items-center justify-between">
                          <p className="text-xs text-white/50">
                            {formatDistance(
                              new Date(notification.createdAt),
                              new Date(),
                              {
                                addSuffix: true,
                              },
                            )}
                          </p>

                          {/* Action indicators */}
                          {!notification.read &&
                            markingAsRead !== notification.id && (
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <span className="text-xs text-white/40">
                                  Click to mark as read
                                </span>
                              </div>
                            )}

                          {markingAsRead === notification.id && (
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 border border-[#b851e0] border-t-transparent rounded-full animate-spin"></div>
                              <span className="text-xs text-white/60">
                                Marking as read...
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-[#b851e0]/0 via-[#b851e0]/[0.01] to-[#eb489b]/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg"></div>
                  </div>
                ))}
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}