/* Animations and Keyframes */

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.1;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) translateX(-10px);
    opacity: 0.2;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.25;
  }
}

@keyframes gradient-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes check {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes error {
  0% {
    transform: scale(0.5) rotate(-45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(184, 81, 224, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(184, 81, 224, 0.5);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes staggerFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(138, 43, 226, 0.6);
  }
}

@keyframes progressGlow {
  0% { box-shadow: 0 0 5px rgba(139, 92, 246, 0.5); }
  100% { box-shadow: 0 0 15px rgba(139, 92, 246, 0.8); }
}

@keyframes stepPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
  }
}

@keyframes dotPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes framerFadeIn {
  from {
    opacity: 0;
    transform: translateY(24px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Animation utility classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out forwards;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-gradient-move {
  background-size: 200% auto;
  animation: gradient-move 8s ease infinite;
}

.animate-gradient-text {
  background: linear-gradient(-45deg, #ffffff, #a855f7, #ec4899, #ffffff);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.animate-check {
  animation: check 0.5s ease-in-out forwards;
}

.animate-error {
  animation: error 0.5s ease-in-out forwards;
}

/* Staggered animation utility */
.stagger-animation {
  opacity: 0;
  transform: translateY(30px);
  animation: staggerFadeIn 0.6s ease-out forwards;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation:nth-child(6) { animation-delay: 0.6s; }

/* Pulse animation for important elements */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Framer-style animations */
.framer-entrance {
  animation: framerFadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Toast animations */
@keyframes slide-in-from-right-full {
  from {
    transform: translateX(calc(100% + 24px)) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes slide-out-to-right-full {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(calc(100% + 24px)) scale(0.95);
    opacity: 0;
  }
}

@keyframes toast-progress {
  from { width: 100%; }
  to { width: 0%; }
}

@keyframes toast-icon-bounce {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes toast-success {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes toast-error {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}
