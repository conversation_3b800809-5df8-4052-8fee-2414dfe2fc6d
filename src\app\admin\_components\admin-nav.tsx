"use client";

import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Settings,
  UserIcon as UserList,
  MessageSquareWarning,
  Menu,
  X,
  TicketPercent,
  VideoIcon,
  ArrowLeft,
  Home,
} from "lucide-react";

const items = [
  { title: "Overview", href: "/admin", icon: Bar<PERSON><PERSON> },
  { title: "Users", href: "/admin/users", icon: UserList },
  { title: "Videos", href: "/admin/community-approval", icon: VideoIcon },
  { title: "Reports", href: "/admin/reports", icon: MessageSquareWarning },
  { title: "Tickets", href: "/admin/tickets", icon: MessageSquareWarning },
  { title: "Coupons", href: "/admin/coupons", icon: TicketPercent },
  { title: "Settings", href: "/admin/settings", icon: Settings },
];

export function AdminNav() {
  const pathname = usePathname();
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="lg:hidden fixed top-3 left-3 z-50 bg-black border border-gray-800 hover:bg-gray-900 text-white h-8 w-8"
        onClick={() => setIsMobileOpen(true)}
        aria-label="Open menu"
      >
        <Menu className="h-4 w-4" />
      </Button>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/80 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-black border-r border-gray-800 transform transition-transform duration-300 ease-in-out lg:hidden",
          isMobileOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex flex-col h-full p-4">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-3 top-3 bg-gray-900 border border-gray-800 hover:bg-gray-800 text-white h-6 w-6"
            onClick={() => setIsMobileOpen(false)}
            aria-label="Close menu"
          >
            <X className="h-3 w-3" />
          </Button>

          {/* Mobile header */}
          <div className="mt-8 mb-4">
            <h2 className="text-sm font-bold text-white">Admin Panel</h2>
          </div>

          {/* Mobile navigation items */}
          <nav className="flex-1 space-y-1">
            {items.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setIsMobileOpen(false)}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md transition-colors text-sm",
                    isActive
                      ? "bg-gray-800 text-white border border-gray-700"
                      : "text-gray-400 hover:bg-gray-900 hover:text-white",
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              );
            })}
          </nav>

          {/* Mobile back to dashboard */}
          <div className="mt-4 pt-4 border-t border-gray-800">
            <Link
              href="/dashboard"
              onClick={() => setIsMobileOpen(false)}
              className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-400 hover:bg-gray-900 hover:text-white transition-colors text-sm"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Desktop sidebar - Compact fixed position */}
      <div className="hidden lg:block fixed left-0 top-0 w-16 h-screen bg-black border-r border-gray-800 z-30">
        <div className="flex flex-col h-full py-4">
          {/* Logo */}
          <div className="px-2 mb-6">
            <Link href="/dashboard" className="block">
              <Image
                src="/assets/images/svg/logo.svg"
                alt="StreamBliss"
                width={48}
                height={48}
                className="object-contain"
              />
            </Link>
          </div>

          {/* Desktop navigation items */}
          <nav className="flex-1 space-y-2 px-2">
            {items.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center justify-center w-12 h-12 rounded-lg transition-colors relative group",
                    isActive
                      ? "bg-gray-800 text-white border border-gray-700"
                      : "text-gray-400 hover:bg-gray-900 hover:text-white",
                  )}
                  title={item.title}
                >
                  <item.icon className="h-5 w-5" />

                  {/* Tooltip */}
                  <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50 pointer-events-none">
                    {item.title}
                  </div>
                </Link>
              );
            })}
          </nav>

          {/* Desktop back to dashboard */}
          <div className="px-2 pt-4 border-t border-gray-800 mt-4">
            <Link
              href="/dashboard"
              className="flex items-center justify-center w-12 h-12 rounded-lg text-gray-400 hover:bg-gray-900 hover:text-white transition-colors relative group"
              title="Back to Dashboard"
            >
              <Home className="h-5 w-5" />

              {/* Tooltip */}
              <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50 pointer-events-none">
                Dashboard
              </div>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}