"use client";

import { useState, useEffect, use<PERSON><PERSON>back, ReactElement } from "react";
import { User, UsersTableProps, verifyUser } from "@/lib/db/user";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MoreVertical,
  Trash,
  Shield,
  Lock,
  Check,
  Ban,
  MailWarning,
  LockKeyholeOpen,
  ChevronLeft,
  ChevronRight,
  Search,
  NotebookIcon,
  CheckIcon,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import ChangeRoleDialog from "../users/_components/change-role-dialog";
import { useRouter, useSearchPara<PERSON>, usePathname } from "next/navigation";
import submitDeleteUser from "../users/_forms/delete-user";
import BanUserDialog from "../users/_components/ban-user-dialog";
import { cn } from "@/lib/utils";
import sendPasswordReset from "../users/_forms/send-password-reset";
import submitDisableTwoFa from "../users/_forms/disable-twofa";
import Link from "next/link";
import { UserWithStats } from "@/lib/db/admin";
import UpdateSubscriptionDialog from "../users/_components/update-subscription-dialog";
import { createLog } from "@/server/logs";
import { LogConstants } from "@/server/log-constants";
import { LogActions } from "@prisma/client";

// Number of users per page
const USERS_PER_PAGE = 9;

type AdminActionItem = {
  id: number;
  name: string;
  icon: ReactElement;
  className?: string;
  onClick: (...data) => void;
  neededPermission: string;
};

export function UsersTable({
  activeUser,
  initialUsers,
  roles,
  banReasons,
  permissions,
}: UsersTableProps) {
  const { success, error } = useEnhancedToast();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const initialSearch = searchParams?.get("search") || "";
  const [allUsers, setAllUsers] = useState<UserWithStats[]>(initialUsers);
  const [filteredUsers, setFilteredUsers] =
    useState<UserWithStats[]>(initialUsers);
  const [users, setUsers] = useState<UserWithStats[]>([]);
  const [search, setSearch] = useState(initialSearch);
  const [debouncedSearch, setDebouncedSearch] = useState(search);
  const [selectedUser, setSelectedUser] = useState<UserWithStats | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(
    Math.ceil(initialUsers.length / USERS_PER_PAGE),
  );
  const [changingRole, setChangingRole] = useState(false);
  const [banningUser, setBanningUser] = useState(false);
  const [changeSubscription, setChangeSubscription] = useState(false);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  const [adminActionItems, setAdminActionItems] = useState<AdminActionItem[]>([
    {
      id: 0,
      name: "Reset Password",
      icon: <MailWarning className="w-4 h-4 mr-2" />,
      className: "text-orange-500",
      onClick: (_user: UserWithStats) => sendResetPasswordForm(_user.id),
      neededPermission: "ADMIN_USER_MANAGE_PASSWORD",
    },
    {
      id: 1,
      name: "Disable 2FA",
      icon: <LockKeyholeOpen className="w-4 h-4 mr-2" />,
      className: "text-orange-600",
      onClick: (_user: UserWithStats) => disableTwoFactor(_user.id),
      neededPermission: "ADMIN_USER_MANAGE_2FA",
    },
    {
      id: 2,
      name: "Verify User's Account",
      icon: <CheckIcon className="w-4 h-4 mr-2" />,
      className: "text-green-600",
      onClick: (_user: UserWithStats) =>
        updateUser(_user.id, { verified: true }),
      neededPermission: "ADMIN_USER_MANAGE_VERIFY",
    },
    {
      id: 3,
      name: "Change User's Role",
      icon: <Shield className="w-4 h-4 mr-2" />,
      className: "text-green-400",
      onClick: (_user: UserWithStats) => {
        setSelectedUser(_user);
        setChangingRole(true);
      },
      neededPermission: "ADMIN_USER_MANAGE_ROLE",
    },
    {
      id: 4,
      name: "Change Subscription",
      icon: <NotebookIcon className="w-4 h-4 mr-2" />,
      className: "text-yellow-500",
      onClick: (_user: UserWithStats) => {
        setSelectedUser(_user);
        setChangeSubscription(true);
      },
      neededPermission: "ADMIN_USER_MANAGE_SUBSCRIPTION",
    },
    {
      id: 5,
      name: "Ban User",
      icon: <Ban className="w-4 h-4 mr-2" />,
      className: "text-red-600",
      onClick: (_user: UserWithStats) => {
        setSelectedUser(_user);
        setBanningUser(true);
      },
      neededPermission: "ADMIN_USER_MANAGE_BAN",
    },
    {
      id: 6,
      name: "Delete User",
      icon: <Trash className="w-4 h-4 mr-2" />,
      className: "text-red-600",
      onClick: (_user: UserWithStats) => {
        deleteUser(_user.id);
      },
      neededPermission: "ADMIN_USER_MANAGE_DELETE",
    },
  ]);

  const updatePageData = useCallback(
    (page: number, sourceUsers: UserWithStats[]) => {
      const startIndex = (page - 1) * USERS_PER_PAGE;
      const endIndex = startIndex + USERS_PER_PAGE;
      setUsers(sourceUsers.slice(startIndex, endIndex));
    },
    [],
  );

  const hasPermission = (permission) => {
    return permissions.some(
      (perm) =>
        perm.permission.name === permission || perm.permission.name === "ALL",
    );
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortedUsers = useCallback(
    (users: UserWithStats[]) => {
      if (!sortField) return users;

      return [...users].sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        switch (sortField) {
          case "name":
            aValue = a.name || "";
            bValue = b.name || "";
            break;
          case "email":
            aValue = a.email || "";
            bValue = b.email || "";
            break;
          case "verified":
            aValue = a.verified ? 1 : 0;
            bValue = b.verified ? 1 : 0;
            break;
          case "package":
            aValue = a.package || "FREE";
            bValue = b.package || "FREE";
            break;
          case "twoFactorEnabled":
            aValue = a.twoFactorEnabled ? 1 : 0;
            bValue = b.twoFactorEnabled ? 1 : 0;
            break;
          case "videosCount":
            aValue = a.videosCount || 0;
            bValue = b.videosCount || 0;
            break;
          case "imagesCount":
            aValue = a.imagesCount || 0;
            bValue = b.imagesCount || 0;
            break;
          case "totalViews":
            aValue = a.totalViews || 0;
            bValue = b.totalViews || 0;
            break;
          case "createdAt":
            aValue = new Date(a.createdAt || 0).getTime();
            bValue = new Date(b.createdAt || 0).getTime();
            break;
          case "roleId":
            aValue = a.roleId || 0;
            bValue = b.roleId || 0;
            break;
        }

        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        if (typeof aValue === "string" && typeof bValue === "string") {
          const result = aValue
            .toLowerCase()
            .localeCompare(bValue.toLowerCase());
          return sortDirection === "asc" ? result : -result;
        } else {
          const result = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
          return sortDirection === "asc" ? result : -result;
        }
      });
    },
    [sortField, sortDirection],
  );

  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ChevronUp className="w-4 h-4 text-gray-500" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUp className="w-4 h-4 text-purple-400" />
    ) : (
      <ChevronDown className="w-4 h-4 text-purple-400" />
    );
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);

  useEffect(() => {
    const filtered = allUsers.filter(
      (user) =>
        user.name?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        user.email?.toLowerCase().includes(debouncedSearch.toLowerCase()),
    );
    const sorted = getSortedUsers(filtered);
    setFilteredUsers(sorted);
    setTotalPages(Math.ceil(sorted.length / USERS_PER_PAGE));
    setCurrentPage(1);
  }, [debouncedSearch, allUsers, sortField, sortDirection, getSortedUsers]);

  useEffect(() => {
    updatePageData(currentPage, filteredUsers);
  }, [currentPage, filteredUsers, updatePageData]);

  const handleSearch = (value: string) => {
    setSearch(value);
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("search", value);
    } else {
      params.delete("search");
    }
    router.replace(`${pathname}?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onChangeRoleComplete = () => {
    setChangingRole(false);
    router.refresh();
  };

  const onBanUserComplete = () => {
    setBanningUser(false);
    router.refresh();
  };

  const onSubscriptionUpdateComplete = () => {
    setChangeSubscription(false);
    router.refresh();
  };

  const sendResetPasswordForm = async (userId) => {
    try {
      const response = await sendPasswordReset({ userId: userId });
      if (response.success) {
        success("Password Reset Sent", response.message);
        router.refresh();
      } else {
        error("Password Reset Failed", response.message);
      }
    } catch (err) {
      error("Password Reset Error", "Failed to send password reset email");
      console.error("Send password reset error:", err);
    }
  };

  const disableTwoFactor = async (userId) => {
    try {
      const response = await submitDisableTwoFa({ userId });
      if (response.success) {
        success("2FA Disabled", response.message);
      } else {
        error("2FA Disable Failed", response.message);
      }
      router.refresh();
    } catch (err) {
      error("2FA Disable Error", "Failed to disable 2FA");
      console.error("Disable 2FA error:", err);
    }
  };

  const deleteUser = async (userId) => {
    try {
      const response = await submitDeleteUser({ userId });
      if (response.success) {
        success("User Deleted", response.message);
        setAllUsers((prevUsers) =>
          prevUsers.filter((user) => user.id !== userId),
        );
      } else {
        error("Delete Failed", response.message);
      }
      router.refresh();
    } catch (err) {
      console.error("Delete user error:", err);
      error("Delete Error", "Failed to delete user");
    }
  };

  const updateUser = async (userId, updatedData) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updatedData),
      });

      if (response.ok) {
        setAllUsers((prevUsers) =>
          prevUsers.map((user) =>
            user.id === userId ? { ...user, ...updatedData } : user,
          ),
        );
        success("User Updated", "User updated successfully");
        await createLog(
          activeUser.id,
          LogConstants.ADMIN_USER_VERIFIED,
          LogActions.ACCOUNT,
          "Verified: " + userId,
        );
      } else {
        throw new Error("Failed to update user");
      }
    } catch (err) {
      console.error("Update user error:", err);
      error("Update Failed", "Failed to update user");
    }
  };

  const getPackageBadge = (pkg) => {
    switch (pkg) {
      case "PRO":
        return (
          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
            Pro
          </Badge>
        );
      case "CREATOR":
        return (
          <Badge className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white border-0">
            Creator
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="border-gray-600 text-gray-300">
            Free
          </Badge>
        );
    }
  };

  const getRoleBadge = (roleId) => {
    const roleConfig = {
      1: { name: "User", class: "border-gray-600 text-gray-300" },
      2: {
        name: "Control",
        class: "bg-gradient-to-r from-red-500 to-red-600 text-white border-0",
      },
      3: {
        name: "Helper",
        class: "bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0",
      },
      4: {
        name: "Supporter",
        class:
          "bg-gradient-to-r from-green-500 to-green-600 text-white border-0",
      },
      5: {
        name: "Admin",
        class:
          "bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0",
      },
      6: {
        name: "Creator",
        class:
          "bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0",
      },
      7: {
        name: "Developer",
        class:
          "bg-gradient-to-r from-indigo-500 to-indigo-600 text-white border-0",
      },
    };

    const config = roleConfig[roleId] || roleConfig[1];
    return <Badge className={config.class}>{config.name}</Badge>;
  };

  const formatDate = (dateValue) => {
    if (!dateValue) return "N/A";

    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return "Invalid date";
      return date.toLocaleDateString();
    } catch (error) {
      console.error("Date formatting error:", error);
      return "Invalid date";
    }
  };

  return (
    <div className="bg-white/5 border border-white/10 rounded-xl p-8">
      {/* Search and Stats Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
        <div className="relative max-w-sm w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search users..."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 bg-white/5 border-white/10 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400 rounded-xl h-12"
          />
        </div>
        <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30">
          <div className="w-2 h-2 rounded-full bg-emerald-400" />
          <span className="text-sm font-medium text-emerald-400">
            {filteredUsers.length} users found
          </span>
        </div>
      </div>

      {/* Table Container */}
      <div className="bg-white/5 border border-white/10 rounded-xl overflow-hidden">
        <div className="w-full overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-white/10 hover:bg-white/5">
                <TableHead className="text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("name")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    User
                    {getSortIcon("name")}
                  </button>
                </TableHead>
                <TableHead className="hidden sm:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("verified")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Status
                    {getSortIcon("verified")}
                  </button>
                </TableHead>
                <TableHead className="hidden md:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("package")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Package
                    {getSortIcon("package")}
                  </button>
                </TableHead>
                <TableHead className="hidden lg:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("twoFactorEnabled")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Security
                    {getSortIcon("twoFactorEnabled")}
                  </button>
                </TableHead>
                <TableHead className="hidden md:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("videosCount")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Videos
                    {getSortIcon("videosCount")}
                  </button>
                </TableHead>
                <TableHead className="hidden md:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("imagesCount")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Images
                    {getSortIcon("imagesCount")}
                  </button>
                </TableHead>
                <TableHead className="hidden md:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("totalViews")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Views
                    {getSortIcon("totalViews")}
                  </button>
                </TableHead>
                <TableHead className="hidden sm:table-cell text-gray-300 font-semibold">
                  <button
                    onClick={() => handleSort("createdAt")}
                    className="flex items-center gap-2 hover:text-purple-400 transition-colors"
                  >
                    Joined
                    {getSortIcon("createdAt")}
                  </button>
                </TableHead>
                <TableHead className="text-gray-300 font-semibold">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length > 0 ? (
                users.map((user, index) => (
                  <TableRow
                    key={user.id}
                    className="border-white/10 hover:bg-white/5 transition-colors duration-200"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <TableCell>
                      <div>
                        <Link
                          href={`/admin/users/${user.id}`}
                          className="font-medium hover:underline text-purple-400 hover:text-purple-300 transition-colors"
                        >
                          {user.name || "Unnamed User"}
                        </Link>
                        <div className="text-sm text-gray-400">
                          {user.email}
                        </div>

                        {/* Mobile-only badges */}
                        <div className="flex flex-wrap gap-1 mt-2 sm:hidden">
                          {getRoleBadge(user.roleId)}
                          {user.verified ? (
                            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 text-xs">
                              Verified
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="border-gray-600 text-gray-400 text-xs"
                            >
                              Unverified
                            </Badge>
                          )}
                          {getPackageBadge(user.package)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="flex flex-wrap gap-2">
                        {getRoleBadge(user.roleId)}
                        {user.verified ? (
                          <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0">
                            Verified
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="border-gray-600 text-gray-400"
                          >
                            Unverified
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {getPackageBadge(user.package)}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex gap-2">
                        {user.twoFactorEnabled && (
                          <Badge className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0">
                            <Lock className="w-3 h-3 mr-1" />
                            2FA
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-gray-300">
                      {user.videosCount || 0}
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-gray-300">
                      {user.imagesCount || 0}
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-gray-300">
                      {user.totalViews?.toLocaleString("en-US") || 0}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell text-gray-300">
                      {formatDate(user.createdAt)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="hover:bg-white/10 text-gray-300 hover:text-white"
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="bg-black border border-white/20"
                        >
                          {adminActionItems.map((actionItem, index) => {
                            if (!hasPermission(actionItem.neededPermission))
                              return null;

                            return (
                              <DropdownMenuItem
                                key={index}
                                className={cn(
                                  actionItem.className,
                                  "hover:bg-white/10",
                                )}
                                onClick={actionItem.onClick.bind(null, user)}
                              >
                                {actionItem.icon}
                                {actionItem.name}
                              </DropdownMenuItem>
                            );
                          })}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow className="border-white/10">
                  <TableCell
                    colSpan={9}
                    className="h-24 text-center text-gray-400"
                  >
                    No users found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-8">
          <div className="text-sm text-gray-400">
            Showing {(currentPage - 1) * USERS_PER_PAGE + 1} to{" "}
            {Math.min(currentPage * USERS_PER_PAGE, filteredUsers.length)} of{" "}
            {filteredUsers.length} users
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => handlePageChange(currentPage - 1)}
              className="bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:text-white disabled:opacity-50"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum = i + 1;
                if (totalPages > 5) {
                  if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                }
                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={cn(
                      "w-8 h-8 p-0",
                      pageNum === currentPage
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0"
                        : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:text-white",
                    )}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              onClick={() => handlePageChange(currentPage + 1)}
              className="bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:text-white disabled:opacity-50"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Dialogs */}
      <Dialog open={changingRole} onOpenChange={setChangingRole}>
        <DialogContent className="bg-black border border-white/20">
          <ChangeRoleDialog
            onComplete={onChangeRoleComplete}
            roles={roles}
            userId={selectedUser?.id}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={banningUser} onOpenChange={setBanningUser}>
        <DialogContent className="bg-black border border-white/20">
          <BanUserDialog
            banReasons={banReasons}
            userId={selectedUser?.id}
            onComplete={onBanUserComplete}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={changeSubscription} onOpenChange={setChangeSubscription}>
        <DialogContent className="bg-black border border-white/20">
          <UpdateSubscriptionDialog
            userId={selectedUser?.id}
            subscriptions={[{ name: "PRO" }, { name: "CREATOR" }]}
            onComplete={onSubscriptionUpdateComplete}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}