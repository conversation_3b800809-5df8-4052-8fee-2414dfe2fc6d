"use client";

import { Logs } from "@prisma/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

type Props = {
  logs: Logs[];
};

export default function UserLogsContent({ logs }: Props) {
  if (logs.length <= 0) {
    return (
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          User Activity Logs
        </h3>
        <div className="text-center p-8 bg-gray-900/30 border border-gray-800/40 rounded-xl">
          <p className="text-gray-400">No logs found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">
        User Activity Logs
      </h3>
      <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-800/40 hover:bg-gray-800/20">
              <TableHead className="text-gray-300 font-semibold">
                Action
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Info
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Details
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Created At
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {logs
              .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
              .map((log) => (
                <TableRow
                  key={log.id}
                  className="border-gray-800/40 hover:bg-gray-800/20"
                >
                  <TableCell>
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                      {log.action}
                    </span>
                  </TableCell>
                  <TableCell>
                    <p className="text-white font-medium">{log.info}</p>
                  </TableCell>
                  <TableCell>
                    <p className="text-gray-300">{log.furtherInfo || "-"}</p>
                  </TableCell>
                  <TableCell>
                    <p className="text-gray-300">
                      {log.createdAt.toLocaleDateString()}
                    </p>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}