"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";
import submitUpdateTicketStatus from "../_actions/update-status";
interface TicketStatusActionsProps {
  ticketId: string;
  currentStatus: "OPEN" | "IN_PROGRESS" | "CLOSED" | "RESOLVED";
}

export function TicketStatusActions({
  ticketId,
  currentStatus,
}: TicketStatusActionsProps) {
  const { success, error } = useEnhancedToast();
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const updateStatus = async (
    status: "OPEN" | "IN_PROGRESS" | "CLOSED" | "RESOLVED",
  ) => {
    setLoading(true);

    try {
      const response = await submitUpdateTicketStatus({
        ticketId,
        status,
      });

      if (!response.success) throw new Error(response.message);

      success("Status Updated", "Ticket status updated successfully.");
      router.refresh();
    } catch (err) {
      error("Update Failed", "Failed to update ticket status. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      className="w-full bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40 h-9 text-sm"
      disabled={loading}
      onClick={() => {
        // You can implement a simple status cycle or keep the dropdown
        const statusCycle = {
          OPEN: "IN_PROGRESS",
          IN_PROGRESS: "RESOLVED",
          RESOLVED: "CLOSED",
          CLOSED: "OPEN",
        };
        const nextStatus = statusCycle[currentStatus] as any;
        if (nextStatus) updateStatus(nextStatus);
      }}
    >
      Update Status
    </Button>
  );
}