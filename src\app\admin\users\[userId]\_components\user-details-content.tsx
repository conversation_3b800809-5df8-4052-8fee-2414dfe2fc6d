"use client";

import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";
import { User } from "@/lib/db/user";
import { LoginHistory } from "@prisma/client";
import { format } from "date-fns";

function formatDate(dateValue, formatString = "dd.MM.yyyy") {
  if (!dateValue) return "N/A";

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid date";
    return format(date, formatString);
  } catch (error) {
    console.error("Date formatting error:", error);
    return "Invalid date";
  }
}

type Props = {
  user: User;
  loginHistory: LoginHistory[];
  canSeeLoginHistory: boolean;
};

export default function UserDetailsContent({
  user,
  loginHistory,
  canSeeLoginHistory,
}: Props) {
  const getRoleBadge = (roleId) => {
    const roleConfig = {
      1: { name: "User", class: "border-gray-600 text-gray-300" },
      2: {
        name: "Control",
        class: "bg-gradient-to-r from-red-500 to-red-600 text-white border-0",
      },
      3: {
        name: "Helper",
        class: "bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0",
      },
      4: {
        name: "Supporter",
        class:
          "bg-gradient-to-r from-green-500 to-green-600 text-white border-0",
      },
      5: {
        name: "Admin",
        class:
          "bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0",
      },
      6: {
        name: "Creator",
        class:
          "bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0",
      },
      7: {
        name: "Developer",
        class:
          "bg-gradient-to-r from-indigo-500 to-indigo-600 text-white border-0",
      },
    };

    const config = roleConfig[roleId] || roleConfig[1];
    return <Badge className={config.class}>{config.name}</Badge>;
  };

  const getPackageBadge = (pkg) => {
    switch (pkg) {
      case "PRO":
        return (
          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
            PRO
          </Badge>
        );
      case "CREATOR":
        return (
          <Badge className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white border-0">
            CREATOR
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="border-gray-600 text-gray-300">
            FREE
          </Badge>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Info Card */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-400">
              Basic Information
            </h3>
            <div className="space-y-2">
              <p className="text-lg font-semibold text-white">
                {user.name || user.id}
              </p>
              <p className="text-sm text-gray-400">{user.email}</p>
              <div className="flex flex-wrap gap-2">
                {user.verified ? (
                  <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0">
                    Verified
                  </Badge>
                ) : (
                  <Badge
                    variant="outline"
                    className="border-gray-600 text-gray-400"
                  >
                    Unverified
                  </Badge>
                )}
                {getPackageBadge(user.package)}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-400">
              Account Status
            </h3>
            <div className="space-y-2">
              <p className="text-lg font-semibold text-white">
                {user.banReason != undefined &&
                user.banReason[0] != null &&
                user.banReason[0].reason.length > 0
                  ? "Banned"
                  : "Active"}
              </p>
              <p className="text-sm text-gray-400">
                2FA{" "}
                {user.UserSettings?.[0]?.twoFactorEnabled
                  ? "Enabled"
                  : "Disabled"}
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-400">Dates</h3>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-gray-400">Joined: </span>
                <span className="text-sm font-medium text-white">
                  {formatDate(user.createdAt)}
                </span>
              </div>
              <div>
                <span className="text-sm text-gray-400">Last Updated: </span>
                <span className="text-sm font-medium text-white">
                  {formatDate(user.updatedAt)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ban Status and Login History */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Ban Status</h3>
          {user.banReason != undefined &&
          user.banReason[0] != null &&
          user.banReason[0].reason.length > 0 ? (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-400 mb-1">
                  Ban Reason
                </h4>
                <p className="font-medium text-red-400">
                  {user.banReason[0]?.reason}
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-400 mb-1">
                  Ban Date
                </h4>
                <p className="text-white">
                  {formatDate(user.banReason[0]?.createdAt)}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-green-400 font-medium">User is not banned</p>
            </div>
          )}
        </div>

        <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Login History
          </h3>
          {loginHistory.length > 0 && canSeeLoginHistory ? (
            <div className="space-y-2 max-h-[300px] overflow-y-auto">
              {loginHistory.slice(0, 5).map((history, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 rounded-lg bg-gray-900/30 border border-gray-800/40"
                >
                  <div className="flex-shrink-0">
                    {history.success ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white">
                      {formatDate(history.createdAt, "dd.MM.yyyy")}
                    </p>
                    <p className="text-xs text-gray-400 truncate">
                      {history.ip} - {history.location || "Unknown"}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-gray-400">No login history available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}