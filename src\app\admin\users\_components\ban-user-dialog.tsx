"use client";

import { Lock } from "lucide-react";
import BanUserForm from "./ban-user-form";

type Params = {
  userId: string | undefined;
  onComplete: (close: boolean) => void;
  banReasons: {reason: string}[];
}

export default function BanUserDialog({userId, onComplete, banReasons}: Params) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Lock className="h-5 w-5 text-primary" />
        <h2 className="text-lg font-semibold">Ban the user!</h2>
      </div>
      <BanUserForm banReasons={banReasons} userId={userId} onComplete={onComplete} />
    </div>
  )
}
