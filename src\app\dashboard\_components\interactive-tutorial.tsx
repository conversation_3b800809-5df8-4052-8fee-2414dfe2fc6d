"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  ChevronLeft,
  ChevronRight,
  Upload,
  Video,
  Image,
  Settings,
  User,
  Search,
  Filter,
  Sparkles,
  MousePointer,
  ArrowR<PERSON>,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { completeTutorial } from "../_actions/complete-tutorial";

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  content: string;
  targetSelector: string;
  position: "top" | "bottom" | "left" | "right" | "center";
  action?: "click" | "hover" | "scroll" | "none";
  actionText?: string;
  icon: React.ElementType;
  gradient: string;
  spotlight: {
    width: number;
    height: number;
    borderRadius?: number;
  };
}

const tutorialSteps: TutorialStep[] = [
  {
    id: "welcome",
    title: "Welcome to StreamBliss!",
    description: "Let's explore your dashboard together",
    content:
      "Your dashboard is command center for all your content. We'll show you the essential features to get you started creating and sharing!",
    targetSelector: "[data-tutorial='welcome-area']",
    position: "bottom",
    action: "none",
    icon: Sparkles,
    gradient: "from-purple-500 to-pink-500",
    spotlight: { width: 350, height: 80, borderRadius: 12 },
  },
  {
    id: "upload-button",
    title: "Start Creating",
    description: "Upload your first video or image",
    content:
      "This magical button is where everything begins! Upload videos up to 10GB (50GB with Premium), images in any format, and we'll handle all the processing automatically. Your content will be ready to share in minutes!",
    targetSelector: "[data-tutorial='upload-button']",
    position: "bottom",
    action: "click",
    actionText: "Go ahead, click it! We'll wait... 😊",
    icon: Upload,
    gradient: "from-blue-500 to-cyan-500",
    spotlight: { width: 180, height: 45, borderRadius: 25 },
  },
  {
    id: "media-grid",
    title: "Your Content Hub",
    description: "Where all your creations live",
    content:
      "Every video and image you upload appears here as beautiful cards. Click any item to play videos, view images, edit privacy settings, copy share links, or dive into detailed analytics. It's your creative portfolio!",
    targetSelector: "[data-tutorial='media-grid']",
    position: "top",
    action: "hover",
    actionText: "Hover here to see where your content will appear",
    icon: Video,
    gradient: "from-emerald-500 to-teal-500",
    spotlight: { width: 500, height: 300, borderRadius: 16 },
  },
  {
    id: "filter-controls",
    title: "Stay Organized",
    description: "Find and organize like a pro",
    content:
      "Never lose track of your content! Filter by videos or images, toggle between public and private items, search by title, and sort by date, popularity, or name. Perfect for managing large collections!",
    targetSelector: "[data-tutorial='filter-controls']",
    position: "bottom",
    action: "click",
    actionText: "Try the filter dropdown to see your options",
    icon: Filter,
    gradient: "from-orange-500 to-red-500",
    spotlight: { width: 900, height: 100, borderRadius: 50 },
  },
  {
    id: "user-menu",
    title: "Your Control Center",
    description: "Profile, settings, and more",
    content:
      "Your personal hub for everything account-related. Access your profile settings, manage billing and subscriptions, create support tickets, and even restart this tutorial anytime. It's all here!",
    targetSelector: "[data-tutorial='user-menu']",
    position: "right",
    action: "hover",
    actionText: "Hover to see your account options",
    icon: User,
    gradient: "from-purple-500 to-indigo-500",
    spotlight: { width: 220, height: 80, borderRadius: 40 },
  },
];

interface InteractiveTutorialProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  userId: string;
}

export function InteractiveTutorial({
  isOpen,
  onClose,
  onComplete,
  userId,
}: InteractiveTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isCompleting, setIsCompleting] = useState(false);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [spotlightPosition, setSpotlightPosition] = useState({ x: 0, y: 0 });
  const { success, error } = useEnhancedToast();

  const currentStepData = tutorialSteps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === tutorialSteps.length - 1;

  // Calculate spotlight and tooltip positions
  useEffect(() => {
    if (!isOpen || !currentStepData) return;

    const findAndPositionElements = () => {
      const element = document.querySelector(
        currentStepData.targetSelector,
      ) as HTMLElement;

      if (element) {
        setTargetElement(element);
        const rect = element.getBoundingClientRect();

        // Calculate spotlight position (center of element)
        const spotlightX = rect.left + rect.width / 2;
        const spotlightY = rect.top + rect.height / 2;
        setSpotlightPosition({ x: spotlightX, y: spotlightY });

        // Calculate tooltip position based on preferred position
        let tooltipX = 0;
        let tooltipY = 0;

        switch (currentStepData.position) {
          case "top":
            tooltipX = Math.min(
              rect.left + rect.width / 2,
              window.innerWidth - 200,
            );
            tooltipY = rect.top - 20;
            break;
          case "bottom":
            // For upload button, position more to the left to avoid going off-screen
            if (currentStepData.id === "upload-button") {
              tooltipX = Math.max(
                200,
                Math.min(rect.left - 50, window.innerWidth - 350),
              );
            } else if (currentStepData.id === "user-menu") {
              // For user menu, position significantly more to the left
              tooltipX = Math.max(20, rect.left - 200);
            } else {
              tooltipX = Math.min(
                rect.left + rect.width / 2,
                window.innerWidth - 200,
              );
            }
            tooltipY = rect.bottom + 20;
            break;
          case "left":
            tooltipX = rect.left - 20;
            tooltipY = rect.top + rect.height / 2;
            break;
          case "right":
            tooltipX = rect.right + 20;
            tooltipY = rect.top + rect.height / 2;
            break;
          case "center":
            tooltipX = window.innerWidth / 2;
            tooltipY = window.innerHeight / 2;
            break;
        }

        setTooltipPosition({ x: tooltipX, y: tooltipY });

        // Add pulsing effect and bring element to front
        element.classList.add("tutorial-active");
        element.style.zIndex = "1000";
        element.style.position = "relative";
      }
    };

    // Initial positioning
    findAndPositionElements();

    // Update on scroll/resize
    const handleUpdate = () => findAndPositionElements();
    window.addEventListener("scroll", handleUpdate);
    window.addEventListener("resize", handleUpdate);

    return () => {
      window.removeEventListener("scroll", handleUpdate);
      window.removeEventListener("resize", handleUpdate);

      // Clean up tutorial styles
      if (targetElement) {
        targetElement.classList.remove("tutorial-active");
        targetElement.style.zIndex = "";
        targetElement.style.position = "";
      }
    };
  }, [currentStep, isOpen, currentStepData, targetElement]);

  const handleNext = () => {
    if (isLastStep) {
      handleComplete();
    } else {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsCompleting(true);
    try {
      await completeTutorial(userId);
      success("Tutorial Completed!", "Welcome to StreamBliss! You're all set to start creating.");
      onComplete();
    } catch (err) {
      error("Tutorial Error", "Failed to save tutorial progress. Please try again.");
    } finally {
      setIsCompleting(false);
    }
  };

  // Add styles to head when component mounts
  useEffect(() => {
    if (!isOpen) return;

    const style = document.createElement("style");
    style.textContent = `
      .tutorial-active {
        animation: tutorial-pulse 2s ease-in-out infinite;
      }

      @keyframes tutorial-pulse {
        0%, 100% {
          box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.4);
        }
        50% {
          box-shadow: 0 0 0 10px rgba(168, 85, 247, 0);
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <>
      {/* Floating tooltip */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: -20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="fixed z-[10000] pointer-events-auto"
          style={{
            left:
              currentStepData.position === "center" ? "50%" : tooltipPosition.x,
            top:
              currentStepData.position === "center" ? "50%" : tooltipPosition.y,
            transform:
              currentStepData.position === "center"
                ? "translate(-50%, -50%)"
                : currentStepData.position === "top"
                  ? "translate(-50%, -100%)"
                  : currentStepData.position === "bottom"
                    ? "translate(-50%, 0%)"
                    : currentStepData.position === "left"
                      ? "translate(-100%, -50%)"
                      : "translate(0%, -50%)",
          }}
        >
          <div className="relative max-w-sm">
            {/* Arrow pointing to element */}
            {currentStepData.position !== "center" && (
              <div
                className={`absolute w-4 h-4 bg-black/90 border border-white/20 rotate-45 ${
                  currentStepData.position === "top"
                    ? "bottom-[-8px] left-1/2 -translate-x-1/2"
                    : currentStepData.position === "bottom"
                      ? "top-[-8px] left-1/2 -translate-x-1/2"
                      : currentStepData.position === "left"
                        ? "right-[-8px] top-1/2 -translate-y-1/2"
                        : "left-[-8px] top-1/2 -translate-y-1/2"
                }`}
              />
            )}

            {/* Tooltip content */}
            <div className="relative bg-black/95 border border-white/20 rounded-2xl p-6 shadow-2xl">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-xl bg-gradient-to-r ${currentStepData.gradient} bg-opacity-20 flex items-center justify-center`}
                  >
                    <currentStepData.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">
                      {currentStepData.title}
                    </h3>
                    <p className="text-sm text-gray-400">
                      Step {currentStep + 1} of {tutorialSteps.length}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-gray-400 hover:text-white h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <p className="text-gray-300 leading-relaxed mb-4">
                {currentStepData.content}
              </p>

              {/* Action prompt */}
              {currentStepData.action !== "none" &&
                currentStepData.actionText && (
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.3 }}
                    className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl mb-4"
                  >
                    <motion.div
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 10, -10, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 2,
                        ease: "easeInOut",
                      }}
                    >
                      <MousePointer className="h-5 w-5 text-purple-400" />
                    </motion.div>
                    <span className="text-sm text-purple-300 font-medium">
                      {currentStepData.actionText}
                    </span>
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{
                        repeat: Infinity,
                        duration: 1.5,
                        ease: "easeInOut",
                      }}
                    >
                      <ArrowRight className="h-4 w-4 text-purple-400" />
                    </motion.div>
                  </motion.div>
                )}

              {/* Progress indicators */}
              <div className="flex items-center justify-between">
                <div className="flex gap-1">
                  {tutorialSteps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                        index === currentStep
                          ? "bg-purple-500 w-6"
                          : index < currentStep
                            ? "bg-purple-500/60"
                            : "bg-white/20"
                      }`}
                    />
                  ))}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    onClick={onClose}
                    size="sm"
                    className="text-gray-400 hover:text-white"
                  >
                    Skip Tutorial
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={handlePrevious}
                    disabled={isFirstStep}
                    size="sm"
                    className="text-gray-400 hover:text-white"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={isCompleting}
                    size="sm"
                    className={`bg-gradient-to-r ${currentStepData.gradient} hover:opacity-90 text-white`}
                  >
                    {isCompleting ? (
                      "Completing..."
                    ) : isLastStep ? (
                      "Complete Tutorial"
                    ) : (
                      <>
                        Next
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </>
  );
}