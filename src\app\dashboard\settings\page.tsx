import { getUser } from "src/server/session";
import SettingsPageClient from "./client";
import { getUserSettings } from "src/server/user-settings";
import { redirect } from "next/navigation";
import { getUserNotifications } from "@/server/notifications";
import { getUserById } from "@/lib/db/user";
import { hasPermission } from "@/server/admin";
import { getUserProfilePicture } from "@/server/profile";

export default async function SettingsPage() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }

  const settings = await getUserSettings(user.id);
  if (!settings) {
    redirect("/login");
  }

  const [notifications, dbUser, userProfileImage] = await Promise.all([
    getUserNotifications(user.id),
    getUserById(user.id),
    getUserProfilePicture(user.id),
  ]);

  const hasAccessToAdmin = dbUser
    ? await hasPermission(dbUser.roleId, ["ADMIN_PAGE"])
    : false;

  return (
    <SettingsPageClient
      userId={user.id}
      twoFaEnabled={settings.twoFactorEnabled}
      userName={user.name || user.email.split("@")[0]}
      userImage={userProfileImage}
      notifications={notifications}
      hasAccessToAdmin={hasAccessToAdmin}
    />
  );
}