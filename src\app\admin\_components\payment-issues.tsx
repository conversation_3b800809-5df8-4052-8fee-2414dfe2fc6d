"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DownloadIcon, FileTextIcon } from "lucide-react";
import {
  getPaymentIssues,
  type PaymentIssue,
} from "../_actions/get-payment-issues";
import { formatDistanceToNow, format } from "date-fns";

const statusColors = {
  requires_payment_method: "default",
  requires_action: "default",
  invoice_open: "destructive",
  subscription_past_due: "destructive",
} as const;

export function PaymentIssues() {
  const [issues, setIssues] = useState<PaymentIssue[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    const fetchIssues = async () => {
      try {
        const data = await getPaymentIssues();
        setIssues(data);
      } catch (error) {
        console.error("Error fetching payment issues:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchIssues();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatDate = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp * 1000), { addSuffix: true });
  };

  const formatDateForExport = (timestamp: number) => {
    return format(new Date(timestamp * 1000), "yyyy-MM-dd HH:mm:ss");
  };

  const exportToCSV = () => {
    setExporting(true);
    try {
      const headers = [
        "Customer Email",
        "Amount",
        "Status",
        "Failure Reason",
        "Created Date",
        "Next Payment Attempt",
        "Subscription Status",
      ];

      const csvData = issues.map((issue) => [
        issue.customerEmail,
        formatCurrency(issue.amount),
        issue.status.replace(/_/g, " "),
        issue.failureReason || "N/A",
        formatDateForExport(issue.created),
        issue.nextPaymentAttempt
          ? formatDateForExport(issue.nextPaymentAttempt)
          : "N/A",
        issue.subscriptionStatus || "N/A",
      ]);

      const csvContent = [
        headers.join(","),
        ...csvData.map((row) => row.join(",")),
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `payment-issues-${format(new Date(), "yyyy-MM-dd")}.csv`,
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error exporting data:", error);
    } finally {
      setExporting(false);
    }
  };

  const generateReport = () => {
    setExporting(true);
    try {
      const reportContent = `
Payment Issues Report
Generated on: ${format(new Date(), "yyyy-MM-dd HH:mm:ss")}

Total Issues: ${issues.length}

Summary by Status:
${Object.entries(
  issues.reduce(
    (acc, issue) => {
      acc[issue.status] = (acc[issue.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  ),
)
  .map(([status, count]) => `${status.replace(/_/g, " ")}: ${count}`)
  .join("\n")}

Total Amount in Issues: ${formatCurrency(
        issues.reduce((sum, issue) => sum + issue.amount, 0),
      )}

Detailed Issues:
${issues
  .map(
    (issue) => `
Customer: ${issue.customerEmail}
Amount: ${formatCurrency(issue.amount)}
Status: ${issue.status.replace(/_/g, " ")}
Failure Reason: ${issue.failureReason || "N/A"}
Created: ${formatDateForExport(issue.created)}
Next Attempt: ${
      issue.nextPaymentAttempt
        ? formatDateForExport(issue.nextPaymentAttempt)
        : "N/A"
    }
Subscription Status: ${issue.subscriptionStatus || "N/A"}
-------------------`,
  )
  .join("\n")}
      `;

      const blob = new Blob([reportContent], {
        type: "text/plain;charset=utf-8;",
      });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `payment-issues-report-${format(new Date(), "yyyy-MM-dd")}.txt`,
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error generating report:", error);
    } finally {
      setExporting(false);
    }
  };

  if (loading) {
    return (
      <Card className="bg-black border border-gray-800/60">
        <CardHeader className="px-4 pb-2">
          <CardTitle className="text-base text-white">Payment Issues</CardTitle>
          <CardDescription className="text-xs text-gray-400">
            Loading payment issues...
          </CardDescription>
        </CardHeader>
        <CardContent className="px-4 pb-4">
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-12 bg-gray-800 rounded-lg" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (issues.length === 0) {
    return (
      <Card className="bg-black border border-gray-800/60">
        <CardHeader className="px-4 pb-2">
          <CardTitle className="text-base text-white">Payment Issues</CardTitle>
          <CardDescription className="text-xs text-gray-400">
            No payment issues found in the last 30 days
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="bg-black border border-gray-800/60">
      <CardHeader className="px-4 pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base text-white">
              Payment Issues
            </CardTitle>
            <CardDescription className="text-xs text-gray-400">
              Failed payments and subscription issues from the last 30 days
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportToCSV}
              disabled={exporting}
              className="text-xs h-8 bg-black border-gray-700 text-gray-300 hover:bg-gray-900 hover:text-white"
            >
              <DownloadIcon className="h-3 w-3 mr-1" />
              Export CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={generateReport}
              disabled={exporting}
              className="text-xs h-8 bg-black border-gray-700 text-gray-300 hover:bg-gray-900 hover:text-white"
            >
              <FileTextIcon className="h-3 w-3 mr-1" />
              Generate Report
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        <div className="space-y-3">
          {issues.map((issue) => (
            <div
              key={issue.id}
              className="flex items-center justify-between p-3 border border-gray-800/60 rounded-lg bg-gray-900/30"
            >
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <p className="font-medium text-white text-sm">
                    {issue.customerEmail}
                  </p>
                  <Badge
                    variant={
                      statusColors[issue.status as keyof typeof statusColors] ||
                      "default"
                    }
                    className="text-xs"
                  >
                    {issue.status.replace(/_/g, " ")}
                  </Badge>
                </div>
                <p className="text-xs text-gray-400">
                  {issue.failureReason || "No reason provided"}
                </p>
                {issue.subscriptionStatus && (
                  <p className="text-xs text-gray-400">
                    Subscription: {issue.subscriptionStatus}
                  </p>
                )}
              </div>
              <div className="text-right">
                <p className="font-medium text-white text-sm">
                  {formatCurrency(issue.amount)}
                </p>
                <p className="text-xs text-gray-400">
                  {issue.nextPaymentAttempt
                    ? `Next attempt: ${formatDate(issue.nextPaymentAttempt)}`
                    : `Created: ${formatDate(issue.created)}`}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}