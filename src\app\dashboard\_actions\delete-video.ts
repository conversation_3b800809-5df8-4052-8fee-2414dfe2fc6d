"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { getVideoDataById } from "@/server/video";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
}

type Params = {
  videoId: string;
}

export default async function deleteVideoOnError({ videoId }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!videoId) {
    return { success: false, message: "Video ID is required" }
  }

  const videoData = await getVideoDataById(videoId);
  if (!videoData) {
    return { success: false, message: "Video not found" }
  }

  const userSession = await getUserSession();
  if (!userSession.userId) {
    return { success: false, message: "User ID not found" }
  }

  const user = await getUserById(userSession.userId);
  if (!user) {
    return { success: false, message: "User not found" }
  }

  if (videoData.userId !== user.id) {
    return { success: false, message: "Unauthorized" }
  }

  await prisma.video.delete({
    where: {
      id: videoId
    }
  });

  return { success: true, message: "Video deleted successfully" }

}
