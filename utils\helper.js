export const NAV_DATA_LIST = [
  { title: "Home", link: "/" },
  { title: "How it Works", link: "#how-it-works" },
  { title: "Features", link: "#features" },
  { title: "Pricing", link: "#pricing" },
];

export const MEMBERS_DATA_LIST = [
  {
    description: "<PERSON>b<PERSON>s is a real insider tip for all streaming fans! The platform impresses with a clear user interface, lightning-fast streaming and an impressive selection of content. - There is something here for every taste. Particularly positive: no annoying advertising and a stable connection even at high resolution. In short: <PERSON><PERSON><PERSON><PERSON> delivers what the name promises - pure streaming enjoyment!",
    profileImg: "/assets/images/svg/edi.png",
    name: "Edionix_HD",
    profession: "Offical Twitch Partner",
    platform: "twitch",
    profileUrl: "https://twitch.tv/edionix_hd",
  },
  {
    description: "Overall, the site is very user-friendly. The user panel is clearly laid out, the community page offers a lot of content and a good insight into clips from other users. What I find particularly positive is how simple the upload process is - quick and uncomplicated. The site is also very well optimized for mobile devices, which makes it easy to use on the go.",
    profileImg: "/assets/images/svg/fryzzer.jpg",
    name: "<PERSON><PERSON><PERSON>",
    profession: "Offical Youtube Partner",
    platform: "youtube",
    profileUrl: "https://youtube.com/@fryzzer",
  },
  {
    description: "Streambliss impresses with a user-friendly interface, fast streaming and a wide selection of content for every taste. The platform scores with a stable connection even at high resolutions and completely dispenses with annoying advertising. Particularly noteworthy are the clear design of the user panel and the community page, which offers exciting content and clips from other users. The upload process is also simple and fast, which makes the overall experience even more enjoyable.",
    profileImg: "/assets/images/svg/vprime.jpg",
    name: "vPriime",
    profession: "Offical Youtube Partner",
    platform: "youtube",
    profileUrl: "https://youtube.com/@vpriime",
  },
  {
    description: "I use StreamBliss because of its user-friendly interface and the wide range of upload options. I particularly like the high streaming quality and the ability to discover new content with ease thanks to the intuitive navigation and personalized recommendations. I also appreciate the reliable performance and the fair price.",
    profileImg: "/assets/images/svg/bobby.png",
    name: "Bobward28",
    profession: "Offical Twitch Partner",
    platform: "twitch",
    profileUrl: "https://twitch.tv/bobward28",
  },
]
export const FOOTER_DATA_LIST = [
  {
    title: "Company",
    links: [
      { label: "About", url: "/about" },
      { label: "Contact", url: "/contact" },
    ],
  },
  {
    title: "Legal",
    links: [
      { label: "Privacy Policy", url: "/privacy" },
      { label: "Terms of Services", url: "/terms" },
      { label: "Impressum", url: "/impressum" },
      { label: "Cookies", url: "/cookies" },
    ],
  },
  {
    title: "Support",
    links: [
      { label: "System Status", url: "/status" },
      { label: "Help Center", url: "/help" },
    ],
  },
];
export const PRICING_LIST = [
  {
    planType: "Free",
    planFee: "$00",
    planUse: "Perfect for personal use",
    list: [
      "250mb per upload limit",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
    ],
  },
  {
    planType: "Pro",
    planFee: "$10",
    planUse: "For individual creators",
    list: [
      "Up to 10GB per video",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
    ],
  },
  {
    planType: "Creator",
    planFee: "$25",
    planUse: "For content groups",
    list: [
      "Unlimited video size",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
      "Team collaboration",
    ],
  },
];
export const ACCORDION_DATA_LIST = [
  {
    question: "Can I switch plans later?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
  },
  {
    question: "How do I cancel my subscription?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
  },
]
export const COMPARE_FEATURE_LIST = [
  {
    title: "Features",
    list: [
      "Upload limit",
      "Video retention",
      "Basic analytics",
      "Content",
      "Priority support",
    ],
  },
  {
    title: "Free",
    list: ["250MB", "180 Days", "Basic", "Ad-supported content", "No"],
  },
  {
    title: "Pro",
    list: ["10GB", "Unlimited", "Advanced", "Ad-free content delivery", "Yes"],
  },
  {
    title: "Creator",
    list: [
      "Unlimited",
      "Unlimited",
      "Advanced",
      "Ad-free content delivery",
      "Yes",
    ],
  },
];

export const FEATURES_LIST = [
  {
    title: "HD Streaming",
    description: "Stream your content in high quality without buffering.",
    icon: "hdStreamingIcon"
  },
  {
    title: "Cloud Storage",
    description:
      "Store your videos securely in the cloud with unlimited storage space.",
    icon: "cloudStorageIcon"
  },
  {
    title: "Image Upload with ShareX",
    description:
      "Upload images instantly using our ShareX configuration for seamless sharing.",
    icon: "shareXIcon"
  },
  {
    title: "Private Sharing",
    description: "Share your videos privately with selected viewers.",
    icon: "sharingIcon"
  },
  {
    title: "Advanced Security",
    description: "End-to-end encryption ensures your content remains protected.",
    icon: "securityIcon"
  },
  {
    title: "Basic Editing",
    description: "Trim, crop, and add filters to your videos directly in the app.",
    icon: "editingIcon"
  },
  {
    title: "Cross-platform Sharing",
    description: "Easily share your content across all major social platforms.",
    icon: "platformIcon"
  },
  {
    title: "Community",
    description: "Connect with like-minded creators and build your audience through our community features.",
    icon: "communityIcon"
  },
  {
    title: "Analytics",
    description: "Track your video performance with detailed viewership statistics and audience insights.",
    icon: "analyticsIcon"
  },
];

export const UPLOAD_DATA_LIST = [
  {
    icon: "uploadIcon",
    heading: "Easy Upload",
    description: "Drag & drop your files in seconds",
  },
  {
    icon: "deliveryIcon",
    heading: "Fast Delivery",
    description: "Global CDN for instant access",
  },
  {
    icon: "alwaysOnlineIcon",
    heading: "Always Online",
    description: "99.9% uptime guarantee",
  }
]
export const SECURE_DATA_LIST = [
  {
    icon: "secureIcon",
    heading: "Secure Storage",
    description: "End-to-end encryption for your data",
  },
  {
    icon: "protectedIcon",
    heading: "SSl Protected",
    description: "256-bit SSL encryption",
  },
  {
    icon: "compliantIcon",
    heading: "GDPR Compliant",
    description: "Your data is protected by EU law",
  }
]
export const ACTION_DATA_LIST = [
  {
    icon: "effort",
    heading: "Effortless Uploads",
    description: "Drag, drop, and your video is ready to share in seconds.",
  },
  {
    icon: "fastUpload",
    heading: "Fast Upload",
    description: "Upload speeds up to 10x faster with our optimized infrastructure.",
  },
  {
    icon: "secureStorage",
    heading: "Secure Storage",
    description: "End-to-end encryption ensures your content remains protected.",
  },
  {
    icon: "easySharing",
    heading: "Easy Sharing",
    description: "Share your content with just a few clicks.",
  }

]