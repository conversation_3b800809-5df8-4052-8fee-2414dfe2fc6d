import { prisma } from '@/lib/prisma'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const path = searchParams.get('path')

  if (!path) {
    return NextResponse.json({ enabled: false })
  }

  const record = await prisma.maintenance.findUnique({
    where: { path }
  })

  return NextResponse.json({ enabled: record?.enabled ?? false })
}
