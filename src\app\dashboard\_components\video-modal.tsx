"use client";

import React from "react";
import { useEffect, useRef, useState } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { StreamBlissVideoPlayer } from "@/components/video";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: {
    id: string;
    title: string;
    url: string;
    thumbnailUrl: string | null;
    views: number;
    createdAt: string | Date;
    shortLink?: string | null;
    isPrivate: boolean;
    duration?: number;
    commentsDisabled?: boolean;
    musicDisabled?: boolean;
    showCommunity?: boolean;
    approvedForCommunity?: boolean;
  };
  userId: string;
  userSubscription: string;
}

export function VideoModal({
  isOpen,
  onClose,
  video,
  userId,
  userSubscription,
}: VideoModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);

  const API_URL = "https://dev-api.streambliss.cloud";
  const videoSrc = `${API_URL}/videos/${video.id}`;
  const posterSrc = video.thumbnailUrl || `${API_URL}/videos/thumbnail/${video.id}`;

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  const handleVideoMetadataLoaded = (aspectRatio: number) => {
    setVideoAspectRatio(aspectRatio);
  };

  // Get dynamic aspect ratio class based on video dimensions
  const getVideoContainerClass = () => {
    if (videoAspectRatio === null) {
      return "max-w-6xl max-h-[80vh] aspect-video";
    }

    if (isPortrait) {
      return "max-w-sm sm:max-w-md max-h-[85vh] aspect-[9/16]";
    } else if (videoAspectRatio > 1.5) {
      return "max-w-6xl max-h-[80vh]";
    } else {
      return "max-w-4xl max-h-[80vh]";
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        ref={modalRef}
        className="fixed inset-0 bg-black/80 z-50 flex flex-col"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        {/* Header with Title and Close Button */}
        <div className="flex items-center justify-between p-4 z-10 pointer-events-none">
          <h1 className="text-white text-xl font-semibold pointer-events-none">
            {video.title}
          </h1>
          <Button
            onClick={onClose}
            variant="ghost"
            size="icon"
            className="h-10 w-10 rounded-full bg-black/50 text-white/70 hover:text-white hover:bg-black/70 transition-colors pointer-events-auto"
          >
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        {/* Video Player Container - Adaptive layout */}
        <div
          className="flex-1 flex items-center justify-center px-6 pb-6 pt-2"
          onClick={onClose}
        >
          <div
            className={`w-full h-full bg-black rounded-lg overflow-hidden transition-all duration-300 ${getVideoContainerClass()}`}
            onClick={(e) => e.stopPropagation()}
          >
            <StreamBlissVideoPlayer
              src={videoSrc}
              poster={posterSrc}
              title={video.title}
              autoplay={true}
              controls={true}
              className="w-full h-full"
              onMetadataLoaded={handleVideoMetadataLoaded}
            />
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}