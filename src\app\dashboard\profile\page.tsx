import { getUser } from "src/server/session";
import ProfilePageClient from "./client";
import { getUserProfilePicture } from "src/server/profile";
import { redirect } from "next/navigation";

export default async function ProfilePage() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }

  const userPackage = user.package || "FREE";
  const userImage = await getUserProfilePicture(user.id);

  return (
    <ProfilePageClient
      userId={user.id}
      name={user.name || ""}
      userPackage={userPackage}
      image={userImage}
      email={user.email}
      instagram={user.instagram || ""}
      twitch={user.twitch || ""}
      twitter={user.twitter || ""}
      website={user.website || ""}
      youtube={user.youtube || ""}
    />
  );
}