"use client";

import { formatDistanceToNow, format } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar, MessageSquare, Plus, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useMemo } from "react";

interface User {
  id: string;
  name: string | null;
  email: string;
  image?: string | null;
}

interface Activity {
  id: string;
  action: string;
  createdAt: string;
  user: User;
  content?: string;
}

interface Response {
  id: string;
  content: string;
  createdAt: string;
  user: User;
}

interface TicketActivityTimelineProps {
  activities: Activity[];
  responses?: Response[];
  limit?: number;
}

export function TicketActivityTimeline({
  activities = [],
  limit = 0,
}: TicketActivityTimelineProps) {
  const [showAll, setShowAll] = useState(false);

  const allItems = useMemo(() => {
    return activities
      .filter(
        (activity) =>
          activity.action === "CREATED" ||
          activity.action === "ASSIGNED" ||
          activity.action === "SELF_ASSIGNED" ||
          activity.action === "UNASSIGNED" ||
          activity.action.startsWith("STATUS_CHANGED_TO"),
      )
      .map((activity) => ({
        ...activity,
        displayAction: getDisplayAction(activity.action),
      }))
      .sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      );
  }, [activities]);

  const displayItems =
    limit && !showAll && allItems.length > limit
      ? allItems.slice(-limit)
      : allItems;

  function getDisplayAction(action: string): string {
    switch (action) {
      case "CREATED":
        return "created the ticket";
      case "ASSIGNED":
        return "assigned the ticket";
      case "SELF_ASSIGNED":
        return "self-assigned the ticket";
      case "UNASSIGNED":
        return "unassigned the ticket";
      case "STATUS_CHANGED_TO_OPEN":
        return "changed status to open";
      case "STATUS_CHANGED_TO_IN_PROGRESS":
        return "changed status to in progress";
      case "STATUS_CHANGED_TO_RESOLVED":
        return "changed status to resolved";
      case "STATUS_CHANGED_TO_CLOSED":
        return "changed status to closed";
      default:
        return action.toLowerCase().replace(/_/g, " ");
    }
  }

  function getActionIcon(action: string) {
    switch (action) {
      case "CREATED":
        return <Plus className="h-4 w-4 text-blue-500" />;
      case "ASSIGNED":
      case "SELF_ASSIGNED":
      case "UNASSIGNED":
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      case "STATUS_CHANGED_TO_OPEN":
      case "STATUS_CHANGED_TO_IN_PROGRESS":
      case "STATUS_CHANGED_TO_RESOLVED":
      case "STATUS_CHANGED_TO_CLOSED":
        return <Clock className="h-4 w-4 text-purple-500" />;
      default:
        return <Calendar className="h-4 w-4 text-purple-500" />;
    }
  }

  if (allItems.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-4">
        No activity yet
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      {limit > 0 && allItems.length > limit && (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className="text-xs text-purple-400 hover:text-purple-300"
          >
            {showAll ? "Show less" : "Show all activity"}
          </Button>
        </div>
      )}

      <div className="space-y-3">
        {displayItems.map((item) => (
          <div
            key={item.id}
            className="bg-gray-900/40 border border-gray-800/40 rounded-xl p-4"
          >
            <div className="flex items-start gap-3">
              {/* Icon */}
              <div className="flex-shrink-0 mt-0.5">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full
                    ${
                      item.action === "CREATED"
                        ? "bg-blue-500"
                        : item.action === "ASSIGNED" ||
                            item.action === "SELF_ASSIGNED" ||
                            item.action === "UNASSIGNED"
                          ? "bg-green-500"
                          : "bg-purple-500"
                    }
                  `}
                >
                  {getActionIcon(item.action)}
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-white text-sm">
                    {item.user.name || item.user.email}
                  </span>
                  <span className="text-gray-300 text-sm">
                    {item.displayAction}
                  </span>
                  <span className="text-gray-500 text-xs ml-auto">
                    {formatDistanceToNow(new Date(item.createdAt), {
                      addSuffix: true,
                    })}
                  </span>
                </div>

                <div className="flex items-center gap-3 text-xs text-gray-400">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {format(new Date(item.createdAt), "MMM dd, yyyy")}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{format(new Date(item.createdAt), "HH:mm")}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}