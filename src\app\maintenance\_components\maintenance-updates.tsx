import { $Enums } from "@prisma/client"
import { Info } from "lucide-react"

type Update = {
  timestamp: string
  message: string
  type: $Enums.MaintenanceTypes
}

export function MaintenanceUpdates({ updates }: { updates: Update[] }) {
  return (
    <div className="space-y-4 animate-fadeIn" style={{ animationDuration: "1.6s", animationDelay: "0.6s" }}>
      <h3 className="text-xl font-semibold text-white">Latest Updates</h3>
      <div className="space-y-3">
        {updates.map((update, index) => (
          <div
            key={index}
            className="border border-[#2196F3]/20 bg-[#121118] rounded-lg p-4 text-left flex items-start space-x-3"
          >
            <div className="mt-0.5">
              <Info className="w-5 h-5 text-[#2196F3]" />
            </div>
            <div className="flex-1">
              <p className="text-white/90">{update.message}</p>
              <p className="text-sm text-white/50 mt-1">{update.timestamp}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}