import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { hasPermission } from "@/server/admin";
import { getCommentsByVideoId } from "@/server/comment";
import { getUserProfilePicture } from "@/server/profile";
import { getAdminUser, getUserSession } from "@/server/session";
import { requestVideo, requestVideoThumbnail } from "@/server/video";
import { redirect } from "next/navigation";
import NotFound from "src/app/not-found";
import ShortLinkPageClient from "src/app/v/[id]/client";

export default async function UserVideoPage(props) {
  const params = await props.params;
  const searchParams = await props.searchParams || {};
  const admin = await getAdminUser(["ADMIN_SEE_USER_VIDEOS"]);
  if (!admin) {
    redirect("/admin")
  }

  const user = await getUserById(params.userId)
  if (!user) {
    NotFound()
  }

  const video = await prisma.video.findUnique({
    where: {
      shortLink: params.id,
    },
    select: {
      id: true,
      title: true,
      userId: true,
      views: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      createdAt: true,
      musicDisabled: true,
      commentsDisabled: true,
      isPrivate: true,
    },
  })

  if (!video) {
    redirect("/404")
  }

  const videoUrl = await requestVideo(video.userId, video.id)
  const thumbnailUrl = await requestVideoThumbnail(video.userId, video.id)
  const comments = await getCommentsByVideoId(video.id)
  const uploaderAvatarUrl = await getUserProfilePicture(video.user.id)
  const currentUserAvatarUrl = user ? await getUserProfilePicture(user.id) : null

  return (
    <ShortLinkPageClient
      videoUrl={videoUrl}
      title={video.title}
      views={video.views + 1}
      videoId={video.id}
      thumbnailUrl={thumbnailUrl}
      uploader={{
        name: video.user.name || video.user.email.split("@")[0],
        email: video.user.email,
        avatarUrl: uploaderAvatarUrl,
        id: video.user.id,
      }}
      createdAt={video.createdAt}
      musicDisabled={video.musicDisabled}
      commentsDisabled={video.commentsDisabled}
      isPrivate={false}
      currentUser={{
        name: user!.name || user!.email.split("@")[0],
        email: user!.email,
        id: user!.id,
        image: currentUserAvatarUrl!,
      }}
      isAdmin={true}
      comments={comments}
      adminView={true}
    />
  )
}