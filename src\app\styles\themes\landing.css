/* Landing Page Theme Styles */

.landing-page {
  background-color: #000000;
  font-family: "Montserrat", sans-serif;
}

.landing-page .border-gradient {
  border-image: linear-gradient(90deg, #000000 0%, #ffffff 54.03%, #000000 100%)
    1;
}

.landing-page .pricing-gradient-border {
  position: relative;
  border-radius: 20px;
  z-index: 1;
}

.landing-page .pricing-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  height: 608px;
  padding: 1px;
  background: linear-gradient(180deg, #b851e0, #eb489b);
  border-radius: 20px;
  z-index: -1;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.landing-page .get-started::before {
  content: "";
  position: absolute;
  inset: 0;
  border: 1px solid transparent;
  background: linear-gradient(
      180deg,
      rgba(184, 81, 224, 0.2) 0%,
      rgba(235, 72, 155, 0.2) 100%
    )
    border-box;
  border-radius: 16px;
  z-index: -1;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.landing-page .membersSlider .swiper-slide {
  opacity: 0.5 !important;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.landing-page .membersSlider .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}

.membersSlider .swiper-slide {
  opacity: 0.5 !important;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.membersSlider .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}

.membersSlider .swiper-slide:hover {
  transform: translateY(-2px);
}

.membersSlider .swiper-slide p {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.6;
}

.landing-page .upload-box::before {
  content: "";
  position: absolute;
  inset: 0;
  border: 1px solid transparent;
  background: linear-gradient(
      204.52deg,
      rgba(184, 81, 224, 0.4) 11.72%,
      rgba(184, 81, 224, 0) 95.7%
    )
    border-box;
  border-radius: 8px;
  z-index: -1;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.landing-page .ellipse-gradient {
  background: var(--background-ellipse-gradient);
}

.landing-page .hero-gradient-border {
  position: relative;
  border-radius: 20px;
  z-index: 1;
}

.landing-page .hero-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  height: 608px;
  padding: 1px;
  background: linear-gradient(
    180deg,
    rgba(184, 81, 224, 0.24) 0%,
    rgba(235, 72, 155, 0.24) 100%
  );
  border-radius: 20px;
  z-index: -1;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

@layer utilities {
  .landing-page .bg-hero {
    background: var(--background-hero-img);
  }

  .landing-page .bg-badge {
    background: var(--background-badge-gradient);
  }

  .landing-page .bg-light-badge {
    background: var(--background-light-badge-gradient);
  }

  .landing-page .bg-back-to-top {
    background: var(--background-back-to-top);
  }

  .landing-page .shadow-custom-purple {
    box-shadow: 0px 0px 17.23px 0px #b851e066;
  }
}

@media (max-height: 601px) {
  .landing-page .mt-0-h-sm {
    margin-top: 0px !important;
  }
}

/* Landing page theme integration */
.landing-page .admin-panel {
  background: inherit;
}

/* Ensure proper font family inheritance */
.admin-panel * {
  font-family:
    "Montserrat",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Roboto",
    "Oxygen",
    "Ubuntu",
    "Cantarell",
    "Fira Sans",
    "Droid Sans",
    "Helvetica Neue",
    sans-serif;
}
