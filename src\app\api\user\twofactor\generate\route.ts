import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function POST(request: NextRequest) {
    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken ||!userSession.userId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No active user session."});
    }

    const generateResponse = await fetch(process.env.VIDEO_API_URL + "/two-factor/generate", {
        method: "POST",
        headers: {
            "Accept": "application/json",
            "Authorization": `Bearer ${userSession.accessToken}`,
            "x-api-key": process.env.API_SERVER_KEY!
        }
    });
    if (!generateResponse.ok) {
        const data = await generateResponse.json();
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    const data = await generateResponse.text();
    return NextResponse.json({status: HttpStatusCode.Ok, data: data});
}