"use client";

import { But<PERSON> } from "@/components/ui/buttons";
import { useState } from "react";
import submitBanUser from "../../reports/_actions/ban-user";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

type BanUserFormProps = {
  userId: string | undefined;
  onComplete: (close: boolean) => void;
  banReasons: { reason: string }[];
};

export default function BanUserForm({ userId, onComplete, banReasons }: BanUserFormProps) {
  const { success, error } = useEnhancedToast();
  const [reason, setReason] = useState<string>("");

  const submit = async () => {
    if (!userId) return;

    const response = await submitBanUser({ userId, reason });
    if (response.success) {
      success("User Banned", response.message);
    } else {
      error("Ban Failed", response.message);
    }

    onComplete(response.success);
  };

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <Select onValueChange={(value) => setReason(value)}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose Ban Reason" />
          </SelectTrigger>
          <SelectContent>
            {banReasons.map((value) => (
              <SelectItem key={value.reason} value={value.reason}>
                {value.reason}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-sm text-white/70 text-center">
          Here you can choose the reason for banning the user.
        </p>
      </div>

      <Button
        type="submit"
        onClick={submit}
        className="w-full bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 text-black font-medium h-10 text-base shadow-lg shadow-teal-500/20"
      >
        Submit
      </Button>
    </div>
  )
}