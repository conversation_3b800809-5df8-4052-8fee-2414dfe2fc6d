"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDistanceToNow } from "date-fns"
import Link from "next/link"

interface TicketCardProps {
  ticket: {
    id: string
    title: string
    status: string
    priority: string
    createdAt: string | Date
    updatedAt: string | Date
    user: {
      name: string | null
      email: string
      image?: string | null
    }
    responses: {
      id: string
      createdAt: string | Date
      user: {
        name: string | null
        email: string
        image?: string | null
      }
    }[]
    _count: {
      responses: number
    }
  }
  isAdmin?: boolean
}

export function TicketCard({ ticket, isAdmin = false }: TicketCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-green-500"
      case "IN PROGRESS":
        return "bg-blue-500"
      case "CLOSED":
        return "bg-red-500"
      case "RESOLVED":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "LOW":
        return "bg-gray-500"
      case "NORMAL":
        return "bg-blue-500"
      case "HIGH":
        return "bg-yellow-500"
      case "URGENT":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const hasResponses = ticket._count.responses > 0
  const latestResponse = hasResponses ? ticket.responses[0] : null
  const path = isAdmin ? `/admin/tickets/${ticket.id}` : `/dashboard/tickets/${ticket.id}`

  return (
    <Link href={path}>
      <Card className="hover:bg-accent transition-colors cursor-pointer">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <Avatar className="h-10 w-10">
              {ticket.user.image ? (
                <AvatarImage src={ticket.user.image} alt={ticket.user.name || "User"} className="object-cover" />
              ) : (
                <AvatarFallback>{ticket.user.name?.[0] || ticket.user.email[0]}</AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">{ticket.title}</h3>
                <div className="flex items-center gap-2">
                  <Badge className={getPriorityColor(ticket.priority)}>{ticket.priority}</Badge>
                  <Badge className={getStatusColor(ticket.status)}>{ticket.status.replace("_", " ")}</Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Updated {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true })}
              </p>

              {hasResponses && (
                <div className="mt-4 pt-4 border-t border-border">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      {latestResponse?.user.image ? (
                        <AvatarImage
                          src={latestResponse.user.image}
                          alt={latestResponse.user.name || "User"}
                          className="object-cover"
                        />
                      ) : (
                        <AvatarFallback>
                          {latestResponse?.user.name?.[0] || latestResponse?.user.email[0]}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className="text-sm font-medium">
                      {latestResponse?.user.name || latestResponse?.user.email}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {latestResponse?.createdAt
                        ? formatDistanceToNow(new Date(latestResponse.createdAt), { addSuffix: true })
                        : ""}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}