import type { Metada<PERSON> } from "next"
import RegisterPageClient from "./client"
import { GoogleOAuthProvider } from '@react-oauth/google';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: "Create an account - StreamBliss",
  description: "Create an account to get started",
}

const clientId = process.env.DISCORD_CLIENT_ID || "";
const redirectUri = process.env.DISCORD_REDIRECT_URI || "";
const googleClientId = process.env.GOOGLE_CLIENT_ID || "";

const RegisterPage = () => {
  return (
    <GoogleOAuthProvider clientId={googleClientId}>
      <Suspense>
        <RegisterPageClient clientId={clientId} redirectUri={redirectUri} />
      </Suspense>
    </GoogleOAuthProvider>
  );
};

export default RegisterPage;