"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  Menu,
  X,
  Home,
  Users,
  Settings,
  HelpCircle,
  CreditCard,
  User as UserIcon,
  LogOut,
} from "lucide-react";

interface MobileNavProps {
  userName: string;
  userImage?: string | null;
}

const navigationItems: Array<{
  name: string;
  href: string;
  icon: React.ComponentType<any>;
}> = [
  {
    name: "Home",
    href: "/dashboard",
    icon: Home,
  },
  {
    name: "Community",
    href: "/community",
    icon: Users,
  },
  {
    name: "Account",
    href: "/dashboard/settings",
    icon: Settings,
  },
  {
    name: "Support",
    href: "/dashboard/tickets",
    icon: HelpCircle,
  },
  {
    name: "Billing",
    href: "/dashboard/billing",
    icon: CreditCard,
  },
  {
    name: "Profile",
    href: "/dashboard/profile",
    icon: UserIcon,
  },
];

export function MobileNav({ userName, userImage }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = () => {
    router.push("/signout");
    setIsOpen(false);
  };

  return (
    <div className="sm:hidden">
      {/* Menu Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-center w-8 h-8 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
      >
        <Menu className="w-5 h-5 text-white/70" />
      </button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/90 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="fixed left-0 top-0 h-full w-80 bg-black border-r border-white/10 z-50 flex flex-col"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <div className="flex items-center gap-3">
                  <Image
                    src="/assets/images/svg/logo.svg"
                    alt="StreamBliss"
                    width={32}
                    height={29}
                    className="w-8 h-auto"
                  />
                  <div className="overflow-hidden">
                    <h3 className="text-white font-semibold whitespace-nowrap overflow-hidden text-ellipsis">
                      StreamBliss
                    </h3>
                    <p className="text-white/60 text-sm whitespace-nowrap overflow-hidden text-ellipsis">
                      {userName}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="flex items-center justify-center w-8 h-8 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
                >
                  <X className="w-5 h-5 text-white/70" />
                </button>
              </div>

              {/* Navigation Items */}
              <div className="flex-1 py-6 px-4 space-y-2">
                {navigationItems.map((item) => {
                  const isActive =
                    pathname === item.href ||
                    (item.href === "/dashboard" &&
                      pathname.startsWith("/dashboard") &&
                      !pathname.includes("/settings") &&
                      !pathname.includes("/tickets") &&
                      !pathname.includes("/billing") &&
                      !pathname.includes("/profile"));

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                        isActive
                          ? "bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-white"
                          : "text-white/70 hover:text-white hover:bg-white/5"
                      }`}
                    >
                      <item.icon className="w-5 h-5 flex-shrink-0" />
                      <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">
                        {item.name}
                      </span>
                    </Link>
                  );
                })}
              </div>

              {/* Footer */}
              <div className="border-t border-white/10 p-4">
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-3 px-4 py-3 rounded-xl text-white/70 hover:text-white hover:bg-red-500/10 transition-all duration-200 w-full"
                >
                  <LogOut className="w-5 h-5 flex-shrink-0" />
                  <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">
                    Logout
                  </span>
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}