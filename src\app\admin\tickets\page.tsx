import { getAdminUser } from "@/server/session";
import { prisma } from "@/lib/prisma";
import { redirect } from "next/navigation";
import AdminTicketsClient from "./client";
import { Ticket } from "lucide-react";

export default async function AdminTicketsPage() {
  const admin = await getAdminUser(["TICKET_MANAGEMENT"]);
  if (!admin) {
    redirect("/admin");
  }

  const openTickets = await prisma.ticket.findMany({
    where: {
      status: "OPEN",
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      responses: {
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
      _count: {
        select: {
          responses: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  const inProgressTickets = await prisma.ticket.findMany({
    where: {
      status: "IN_PROGRESS",
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      responses: {
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
      _count: {
        select: {
          responses: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  const resolvedTickets = await prisma.ticket.findMany({
    where: {
      status: "RESOLVED",
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      responses: {
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
      _count: {
        select: {
          responses: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 10,
  });

  const closedTickets = await prisma.ticket.findMany({
    where: {
      status: "CLOSED",
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      responses: {
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
      _count: {
        select: {
          responses: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 10,
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-800/60">
            <Ticket className="h-4 w-4 text-gray-300" />
          </div>
          <h1 className="text-xl font-bold text-white">Ticket Management</h1>
        </div>
        <p className="text-sm text-gray-400">
          Manage and respond to user support tickets
        </p>
      </div>

      {/* Tickets Content */}
      <AdminTicketsClient
        openTickets={openTickets}
        inProgressTickets={inProgressTickets}
        resolvedTickets={resolvedTickets}
        closedTickets={closedTickets}
      />
    </div>
  );
}