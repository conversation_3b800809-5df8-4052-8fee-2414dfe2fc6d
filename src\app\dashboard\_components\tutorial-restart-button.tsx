"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { HelpCircle, RotateCcw } from "lucide-react";
import { restartTutorial } from "../_actions/restart-tutorial";
import { useRouter } from "next/navigation";

export function TutorialRestartButton() {
  const [isRestarting, setIsRestarting] = useState(false);
  const { success, error } = useEnhancedToast();
  const router = useRouter();

  const handleRestart = async () => {
    setIsRestarting(true);
    try {
      await restartTutorial();
      success("Tutorial Reset!", "The tutorial will show again when you visit your dashboard.");

      // Navigate to dashboard to show tutorial
      router.push("/dashboard");
    } catch (err) {
      error("Tutorial Error", "Failed to restart tutorial. Please try again.");
    } finally {
      setIsRestarting(false);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <HelpCircle className="h-5 w-5 text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">
            Interactive Dashboard Tour
          </h3>
          <p className="text-sm text-gray-400">
            Take the interactive spotlight tour of your dashboard again
          </p>
        </div>
      </div>

      <Button
        onClick={handleRestart}
        disabled={isRestarting}
        variant="outline"
        className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10 hover:border-blue-400 transition-all duration-300"
      >
        <RotateCcw className="h-4 w-4 mr-2" />
        {isRestarting ? "Restarting..." : "Restart Tutorial"}
      </Button>
    </div>
  );
}