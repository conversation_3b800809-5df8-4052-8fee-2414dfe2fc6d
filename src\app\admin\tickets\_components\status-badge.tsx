import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-emerald-500 text-white border-0";
      case "IN_PROGRESS":
      case "IN PROGRESS":
        return "bg-blue-500 text-white border-0";
      case "CLOSED":
        return "bg-red-500 text-white border-0";
      case "RESOLVED":
        return "bg-purple-500 text-white border-0";
      default:
        return "bg-gray-500 text-white border-0";
    }
  };

  return (
    <Badge
      className={cn(
        "rounded-md px-2 py-1 text-xs font-medium",
        getStatusColor(status),
        className,
      )}
    >
      {status}
    </Badge>
  );
}