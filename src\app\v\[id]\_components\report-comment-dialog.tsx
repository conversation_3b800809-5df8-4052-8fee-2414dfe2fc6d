"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { useEnhancedToast } from "@/components/ui/enhanced-toast"
import { useRouter } from "next/navigation"
import { submitCommentReport } from "../_actions/report-comment"

interface ReportCommentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  commentId: string
}

const reportReasons = [
  { id: "spam", label: "Spam or misleading" },
  { id: "harassment", label: "Harassment or bullying" },
  { id: "hate_speech", label: "Hate speech" },
  { id: "inappropriate", label: "Inappropriate content" },
  { id: "misinformation", label: "Misinformation" },
  { id: "other", label: "Other" },
]

export function ReportCommentDialog({ open, onOpenChange, commentId }: ReportCommentDialogProps) {
  const [reason, setReason] = useState("")
  const [details, setDetails] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { success, error } = useEnhancedToast()
  const router = useRouter()

  const handleSubmit = async () => {
    if (!reason) {
      error("Reason Required", "Please select a reason for reporting this comment");
      return
    }

    setIsSubmitting(true)

    try {
      // Backend Placeholder - For Panda
      const response = await submitCommentReport({
        commentId,
        reason,
        details
      });

      if (response.success) {
        success("Report Submitted", response.message);
        onOpenChange(false)
        setReason("")
        setDetails("")
      } else {
        error("Report Failed", response.message);
      }
    } catch (err) {
      error("Submit Error", "There was an error submitting your report. Please try again.");
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <DialogTitle className="text-center pt-4">Report Comment</DialogTitle>
          <DialogDescription className="text-center">
            Let us know why you think this comment should be reviewed
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <RadioGroup value={reason} onValueChange={setReason}>
            <div className="grid gap-2">
              <Label htmlFor="report-reason" className="text-sm font-medium">
                Reason for reporting
              </Label>
              <div className="grid gap-2">
                {reportReasons.map((reportReason) => (
                  <div key={reportReason.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={reportReason.id} id={`reason-${reportReason.id}`} />
                    <Label htmlFor={`reason-${reportReason.id}`} className="text-sm font-normal">
                      {reportReason.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </RadioGroup>
          <div className="grid gap-2">
            <Label htmlFor="details" className="text-sm font-medium">
              Additional details (optional)
            </Label>
            <Textarea
              id="details"
              placeholder="Please provide any additional information that might help our review"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              className="resize-none"
              rows={4}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              onOpenChange(false)
              setReason("")
              setDetails("")
            }}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button className="w-full sm:w-auto" onClick={handleSubmit} disabled={isSubmitting || !reason}>
            {isSubmitting ? "Submitting..." : "Submit Report"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}