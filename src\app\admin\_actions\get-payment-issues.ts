"use server"

import { stripe } from "@/lib/stripe"
import Stripe from "stripe"

export type PaymentIssue = {
  id: string
  customerId: string
  customerEmail: string
  amount: number
  status: string
  created: number
  failureReason?: string
  lastPaymentAttempt?: number
  nextPaymentAttempt?: number
  subscriptionId?: string
  subscriptionStatus?: string
}

export async function getPaymentIssues() {
  try {
    const now = Math.floor(Date.now() / 1000)
    const thirtyDaysAgo = now - 30 * 24 * 60 * 60
    const failedPayments = await stripe.paymentIntents.list({
      created: {
        gte: thirtyDaysAgo,
        lte: now,
      },
      limit: 100,
    })

    const failedInvoices = await stripe.invoices.list({
      created: {
        gte: thirtyDaysAgo,
        lte: now,
      },
      status: 'open',
      limit: 100,
    })

    const failedSubscriptions = await stripe.subscriptions.list({
      created: {
        gte: thirtyDaysAgo,
        lte: now,
      },
      status: 'past_due',
      limit: 100,
      expand: ['data.default_payment_method'],
    })

    const issues: PaymentIssue[] = []

    for (const payment of failedPayments.data) {
      if (payment.status === 'requires_payment_method' || payment.status === 'requires_action') {
        const customer = await stripe.customers.retrieve(payment.customer as string) as Stripe.Customer
        issues.push({
          id: payment.id,
          customerId: payment.customer as string,
          customerEmail: customer.email || 'No email',
          amount: payment.amount / 100,
          status: payment.status,
          created: payment.created,
          failureReason: payment.last_payment_error?.message,
          lastPaymentAttempt: payment.created,
          nextPaymentAttempt: undefined,
        })
      }
    }

    for (const invoice of failedInvoices.data) {
      const customer = await stripe.customers.retrieve(invoice.customer as string) as Stripe.Customer
      issues.push({
        id: invoice.id,
        customerId: invoice.customer as string,
        customerEmail: customer.email || 'No email',
        amount: invoice.amount_due / 100,
        status: 'invoice_open',
        created: invoice.created,
        failureReason: invoice.billing_reason || undefined,
        lastPaymentAttempt: undefined,
        nextPaymentAttempt: invoice.next_payment_attempt || undefined,
      })
    }

    for (const subscription of failedSubscriptions.data) {
      const customer = await stripe.customers.retrieve(subscription.customer as string) as Stripe.Customer
      issues.push({
        id: subscription.id,
        customerId: subscription.customer as string,
        customerEmail: customer.email || 'No email',
        amount: subscription.items.data[0]?.price?.unit_amount ? subscription.items.data[0].price.unit_amount / 100 : 0,
        status: 'subscription_past_due',
        created: subscription.created,
        failureReason: 'Subscription payment failed',
        lastPaymentAttempt: subscription.current_period_start,
        nextPaymentAttempt: subscription.current_period_end,
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status,
      })
    }

    return issues.sort((a, b) => b.created - a.created)
  } catch (error) {
    console.error("Error fetching payment issues:", error)
    return []
  }
} 