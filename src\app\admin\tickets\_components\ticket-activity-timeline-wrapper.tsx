"use client"

import { TicketActivityTimeline } from "./ticket-activity-timeline"

interface User {
  id: string
  name: string | null
  email: string
  image?: string | null
}

interface TicketActivityTimelineWrapperProps {
  activities: {
    id: string
    action: string
    createdAt: string
    user: User
    content?: string
  }[]
  responses: {
    id: string
    content: string
    createdAt: string
    user: User
  }[]
  limit?: number
}

export function TicketActivityTimelineWrapper({
  activities,
  responses = [],
  limit = 0,
}: TicketActivityTimelineWrapperProps) {
  return <TicketActivityTimeline activities={activities} responses={responses} limit={limit} />
}