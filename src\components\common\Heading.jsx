import React from "react";

const Heading = ({ children, className = "", variant = "default" }) => {
  const variants = {
    default: "text-custom-5xl leading-130 max-lg:text-4xl max-md:text-3xl",
    "3xl": "text-custom-3xl leading-130 max-lg:text-2xl max-md:text-xl",
    "4xl": "text-custom-4xl leading-120 max-lg:text-custom-3xl max-md:text-2xl",
    "6xl": "text-custom-6xl leading-130 max-lg:text-custom-4xl max-md:text-3xl",
  };

  return (
    <h2
      className={`text-white font-semibold text-center mb-4 max-md:mb-2.5 ${className} ${variants[variant]}`}
    >
      {children}
    </h2>
  );
};

export default Heading;
