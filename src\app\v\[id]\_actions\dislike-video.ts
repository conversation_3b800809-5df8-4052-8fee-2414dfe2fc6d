"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { dislikeVideo, hasLikedVideo, unlikeVideo } from "@/server/video";

type Callback = {
  success: boolean;
  message: string;
  data?: any
}

type Props = {
  videoId: string;
}

export default async function submitDislikeVideo({ videoId }: Props): Promise<Callback> {
  try {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
      return {
        success: false,
        message: "You have exceeded the rate limit. Please try again later."
      }
    }

    const userSession = await getUserSession();
    const userId = userSession.userId;
    if (!userId || !userSession) {
      return {
        success: false,
        message: "User not authenticated."
      }
    }

    const isLiked = await hasLikedVideo(videoId, userId);
    if (isLiked) {
      await unlike<PERSON>ide<PERSON>(videoId, userId);
    }

    const dislikeData = await dislikeVideo(videoId, userId);
    if (!dislikeData) {
      return {
        success: false,
          message: "Failed to dislike the video."
      }
    }

    if (typeof dislikeData === "boolean" && dislikeData === true) {
      return {
        success: true,
        message: "You have removed your dislike.",
        data: {
          liked: false,
          dislike: false
        }
      }
    }

    return {
      success: true,
      message: "Disliked video!",
      data: {
        liked: false,
        dislike: true
      }
    }
  } catch (error) {
    console.error("Error disliking video:", error);

    return {
      success: false,
      message: "An error occurred while disliking the video."
    }
  }
}
