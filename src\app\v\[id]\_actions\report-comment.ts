"use server"

import { getUserById } from "@/lib/db/user"
import { rateLimiter } from "@/lib/rate-limit"
import { createReportComment } from "@/server/comment-reports"
import { getClientIp } from "@/server/geolocation"
import { getUserSession } from "@/server/session"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type ReportCommentParams = {
  commentId: string
  reason: string
  details?: string
}

type Callback = {
  success: boolean;
  message: string;
}

export async function submitCommentReport({ commentId, reason, details }: ReportCommentParams): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You are being rate limited. Please try again later.",
    }
  }

  try {
    if (!commentId || !reason) {
      return {
        success: false,
        message: "Comment ID and reason are required.",
      }
    }

    const session = await getUserSession();
    if (!session) {
      return {
        success: false,
        message: "You must be logged in to report a comment.",
      }
    }

    const userId = session.userId;
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        message: "User not found.",
      }
    }

    const reportedCommentData = await createReportComment(userId, commentId, reason, details);
    if (!reportedCommentData) {
      return {
        success: false,
        message: "Failed to report the comment. Please try again.",
      }
    }

    await createLog(user.id, LogConstants.REPORTED_COMMENT, LogActions.VIDEO);

    return {
      success: true,
      message: "Thank you for your report. We'll review it shortly.",
    }
  } catch (error) {
    console.error("Error reporting comment:", error)
    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    }
  }
}