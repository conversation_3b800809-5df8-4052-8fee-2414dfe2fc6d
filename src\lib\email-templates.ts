export function generateVerificationEmailTemplate(token: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email - StreamBliss</title>
        <!--[if mso]>
        <noscript>
        <xml>
        <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
        </xml>
        </noscript>
        <![endif]-->
      </head>
      <body style="margin: 0; padding: 0; background-color: #000000; font-family: Arial, Helvetica, sans-serif;">
        <div style="background-color: #000000; padding: 20px;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #000000;">
            <tr>
              <td align="center" style="padding: 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #111111; border-radius: 16px; border: 1px solid #333333; max-width: 600px; overflow: hidden;">

                  <!-- Header with Logo -->
                  <tr>
                    <td align="center" style="background: linear-gradient(135deg, #1a0a1f 0%, #000000 100%); background-color: #1a0a1f; padding: 60px 32px; border-bottom: 2px solid #b851e0;">
                      <img src="${baseUrl}/Streambliss-logo.png" alt="StreamBliss" width="400" height="152" style="display: block; margin: 0 auto; max-width: 400px; height: auto;" />
                    </td>
                  </tr>

                  <!-- Body Content -->
                  <tr>
                    <td style="background-color: #0a0a0a; padding: 40px 32px;">
                      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">

                        <!-- Icon -->
                        <tr>
                          <td align="center" style="padding-bottom: 24px;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                              <tr>
                                <td style="width: 80px; height: 80px; background: linear-gradient(135deg, #b851e0 0%, #eb489b 100%); background-color: #b851e0; border-radius: 16px; text-align: center; vertical-align: middle; font-size: 32px; line-height: 80px; color: #ffffff;">
                                  ✉️
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>

                        <!-- Title -->
                        <tr>
                          <td align="center" style="padding-bottom: 24px;">
                            <h2 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold; text-align: center; font-family: Arial, Helvetica, sans-serif;">
                              Verify Your Email Address
                            </h2>
                          </td>
                        </tr>

                        <!-- Welcome Text -->
                        <tr>
                          <td style="padding-bottom: 20px;">
                            <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                              Welcome to StreamBliss! 🎉
                            </p>
                          </td>
                        </tr>

                        <!-- Instructions -->
                        <tr>
                          <td style="padding-bottom: 32px;">
                            <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                              To complete your registration and unlock all the amazing features of our platform, please verify your email address using the verification code below:
                            </p>
                          </td>
                        </tr>

                        <!-- Verification Code -->
                        <tr>
                          <td align="center" style="padding: 32px 0;">
                            <div style="background: linear-gradient(135deg, rgba(184, 81, 224, 0.2) 0%, rgba(235, 72, 155, 0.2) 100%); background-color: rgba(184, 81, 224, 0.2); border: 2px solid #b851e0; border-radius: 12px; padding: 32px 20px; text-align: center; font-size: 48px; font-weight: bold; letter-spacing: 8px; color: #b851e0; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                              ${token}
                            </div>
                          </td>
                        </tr>

                        <!-- More Instructions -->
                        <tr>
                          <td style="padding-bottom: 24px;">
                            <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                              Simply enter this code on the verification page to confirm your email address and start your creative journey with us.
                            </p>
                          </td>
                        </tr>

                        <!-- Expiry Note -->
                        <tr>
                          <td align="center" style="padding-bottom: 16px;">
                            <div style="background-color: rgba(251, 191, 36, 0.1); border: 1px solid rgba(251, 191, 36, 0.3); border-radius: 8px; padding: 12px; text-align: center; font-size: 14px; color: #fbbf24; font-weight: bold; font-family: Arial, Helvetica, sans-serif;">
                              ⏰ This verification code will expire in 3 hours for security reasons.
                            </div>
                          </td>
                        </tr>

                        <!-- Security Badge -->
                        <tr>
                          <td align="center" style="padding-bottom: 40px;">
                            <div style="background-color: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 8px; padding: 8px 12px; font-size: 12px; color: #10b981; text-align: center; display: inline-block; font-family: Arial, Helvetica, sans-serif;">
                              🔒 Your security is our priority
                            </div>
                          </td>
                        </tr>

                        <!-- Help Text -->
                        <tr>
                          <td style="border-top: 1px solid #333333; padding-top: 24px;">
                            <div style="font-size: 14px; color: #b0b0b0; font-family: Arial, Helvetica, sans-serif;">
                              <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                                <strong style="color: #ffffff;">Didn't create an account?</strong><br>
                                <span style="color: #b0b0b0;">If you didn't sign up for StreamBliss, you can safely ignore this email. No further action is required.</span>
                              </p>

                              <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                                <strong style="color: #ffffff;">Need assistance?</strong><br>
                                <span style="color: #b0b0b0;">If you're having trouble with the verification process, our support team is here to help. Contact us anytime and we'll get you sorted!</span>
                              </p>

                              <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                                <strong style="color: #ffffff;">What's next?</strong><br>
                                <span style="color: #b0b0b0;">Once verified, you'll have access to unlimited uploads, advanced analytics, and our growing community of creators.</span>
                              </p>
                            </div>
                          </td>
                        </tr>

                      </table>
                    </td>
                  </tr>

                  <!-- Footer -->
                  <tr>
                    <td style="background-color: #000000; padding: 24px 32px; text-align: center; border-top: 1px solid #333333;">
                      <p style="color: #999999; font-size: 14px; margin: 0 0 8px 0; font-family: Arial, Helvetica, sans-serif;">
                        &copy; ${new Date().getFullYear()} StreamBliss. All rights reserved.
                      </p>
                      <p style="color: #666666; font-size: 12px; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                        This is an automated message, please do not reply to this email.<br>
                        StreamBliss - The Creative Cloud for Everyone
                      </p>
                    </td>
                  </tr>

                </table>
              </td>
            </tr>
          </table>
        </div>
      </body>
      </html>
    `;
}

export function generatePasswordResetTemplate(resetLink: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - StreamBliss</title>
      <!--[if mso]>
      <noscript>
      <xml>
      <o:OfficeDocumentSettings>
      <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
      </xml>
      </noscript>
      <![endif]-->
    </head>
    <body style="margin: 0; padding: 0; background-color: #000000; font-family: Arial, Helvetica, sans-serif;">
      <div style="background-color: #000000; padding: 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #000000;">
          <tr>
            <td align="center" style="padding: 0;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #111111; border-radius: 16px; border: 1px solid #333333; max-width: 600px; overflow: hidden;">

                <!-- Header with Logo -->
                <tr>
                  <td align="center" style="background: linear-gradient(135deg, #1a0a1f 0%, #000000 100%); background-color: #1a0a1f; padding: 60px 32px; border-bottom: 2px solid #b851e0;">
                    <img src="${baseUrl}/Streambliss-logo.png" alt="StreamBliss" width="400" height="152" style="display: block; margin: 0 auto; max-width: 400px; height: auto;" />
                  </td>
                </tr>

                <!-- Body Content -->
                <tr>
                  <td style="background-color: #0a0a0a; padding: 40px 32px;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">

                      <!-- Icon -->
                      <tr>
                        <td align="center" style="padding-bottom: 24px;">
                          <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                            <tr>
                              <td style="width: 80px; height: 80px; background: linear-gradient(135deg, #b851e0 0%, #eb489b 100%); background-color: #b851e0; border-radius: 16px; text-align: center; vertical-align: middle; font-size: 32px; line-height: 80px; color: #ffffff;">
                                🔒
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>

                      <!-- Title -->
                      <tr>
                        <td align="center" style="padding-bottom: 24px;">
                          <h2 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold; text-align: center; font-family: Arial, Helvetica, sans-serif;">
                            Reset Your Password
                          </h2>
                        </td>
                      </tr>

                      <!-- Greeting -->
                      <tr>
                        <td style="padding-bottom: 20px;">
                          <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                            Hello there! 👋
                          </p>
                        </td>
                      </tr>

                      <!-- Explanation -->
                      <tr>
                        <td style="padding-bottom: 20px;">
                          <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                            We received a request to reset your password for your StreamBliss account. No worries – it happens to the best of us!
                          </p>
                        </td>
                      </tr>

                      <!-- Instructions -->
                      <tr>
                        <td style="padding-bottom: 32px;">
                          <p style="color: #e0e0e0; font-size: 16px; line-height: 1.7; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                            Click the button below to create a new, secure password:
                          </p>
                        </td>
                      </tr>

                      <!-- Reset Button -->
                      <tr>
                        <td align="center" style="padding: 0 0 32px 0;">
                          <a href="${resetLink}" style="background: linear-gradient(135deg, #b851e0 0%, #eb489b 100%); background-color: #b851e0; color: #ffffff; text-decoration: none; padding: 18px 36px; border-radius: 12px; font-weight: bold; font-size: 18px; text-align: center; display: inline-block; font-family: Arial, Helvetica, sans-serif;">
                            Reset My Password
                          </a>
                        </td>
                      </tr>

                      <!-- Expiry Note -->
                      <tr>
                        <td align="center" style="padding-bottom: 24px;">
                          <div style="background-color: rgba(251, 191, 36, 0.1); border: 1px solid rgba(251, 191, 36, 0.3); border-radius: 8px; padding: 12px; text-align: center; font-size: 14px; color: #fbbf24; font-weight: bold; font-family: Arial, Helvetica, sans-serif;">
                            ⏰ This password reset link will expire in 3 hours for security reasons.
                          </div>
                        </td>
                      </tr>

                      <!-- Security Tips -->
                      <tr>
                        <td style="padding-bottom: 40px;">
                          <div style="background-color: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 12px; padding: 20px; font-family: Arial, Helvetica, sans-serif;">
                            <h4 style="color: #3b82f6; font-size: 16px; font-weight: bold; margin: 0 0 12px 0;">
                              🔒 Password Security Tips
                            </h4>
                            <ul style="list-style: none; padding: 0; margin: 0;">
                              <li style="color: #e0e0e0; font-size: 14px; margin: 8px 0; padding-left: 20px; position: relative;">✓ Use at least 12 characters</li>
                              <li style="color: #e0e0e0; font-size: 14px; margin: 8px 0; padding-left: 20px; position: relative;">✓ Include uppercase, lowercase, numbers, and symbols</li>
                              <li style="color: #e0e0e0; font-size: 14px; margin: 8px 0; padding-left: 20px; position: relative;">✓ Avoid common words or personal information</li>
                              <li style="color: #e0e0e0; font-size: 14px; margin: 8px 0; padding-left: 20px; position: relative;">✓ Consider using a password manager</li>
                            </ul>
                          </div>
                        </td>
                      </tr>

                      <!-- Help Text -->
                      <tr>
                        <td style="border-top: 1px solid #333333; padding-top: 24px;">
                          <div style="font-size: 14px; color: #b0b0b0; font-family: Arial, Helvetica, sans-serif;">
                            <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                              <strong style="color: #ffffff;">Didn't request a password reset?</strong><br>
                              <span style="color: #b0b0b0;">If you didn't request this password reset, you can safely ignore this email. Your account remains secure and no changes have been made.</span>
                            </p>

                            <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                              <strong style="color: #ffffff;">Having trouble?</strong><br>
                              <span style="color: #b0b0b0;">If the button above doesn't work, you can copy and paste this link into your browser:</span><br>
                              <a href="${resetLink}" style="color: #3b82f6; word-break: break-all; text-decoration: underline;">${resetLink}</a>
                            </p>

                            <p style="margin: 12px 0; line-height: 1.6; color: #b0b0b0;">
                              <strong style="color: #ffffff;">Need additional help?</strong><br>
                              <span style="color: #b0b0b0;">Our support team is standing by to assist you. Contact us anytime and we'll help you regain access to your account quickly and securely.</span>
                            </p>
                          </div>
                        </td>
                      </tr>

                    </table>
                  </td>
                </tr>

                <!-- Footer -->
                <tr>
                  <td style="background-color: #000000; padding: 24px 32px; text-align: center; border-top: 1px solid #333333;">
                    <p style="color: #999999; font-size: 14px; margin: 0 0 8px 0; font-family: Arial, Helvetica, sans-serif;">
                      &copy; ${new Date().getFullYear()} StreamBliss. All rights reserved.
                    </p>
                    <p style="color: #666666; font-size: 12px; margin: 0; font-family: Arial, Helvetica, sans-serif;">
                      This is an automated message, please do not reply to this email.<br>
                      StreamBliss - The Creative Cloud for Everyone
                    </p>
                  </td>
                </tr>

              </table>
            </td>
          </tr>
        </table>
      </div>
    </body>
    </html>
  `;
}