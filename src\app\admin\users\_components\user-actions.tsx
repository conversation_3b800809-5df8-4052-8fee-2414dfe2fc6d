"use client"

import { useState } from "react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { useEnhancedToast } from "@/components/ui/enhanced-toast"
import { useRouter } from "next/navigation"
import { MoreVertical, Trash, Shield, Ban, Check, MailWarning, LockKeyholeOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import submitDeleteUser from "../_forms/delete-user"
import sendPasswordReset from "../_forms/send-password-reset"
import submitDisableTwoFa from "../_forms/disable-twofa"
import ChangeRoleDialog from "../_components/change-role-dialog"
import BanUserDialog from "../_components/ban-user-dialog"

export function UserActions({ user, roles, banReasons }) {
  const { success, error, info } = useEnhancedToast()
  const router = useRouter()
  const [changingRole, setChangingRole] = useState(false)
  const [banningUser, setBanningUser] = useState(false)

  const onChangeRoleComplete = () => {
    setChangingRole(false)
    router.refresh()
  }

  const onBanUserComplete = () => {
    setBanningUser(false)
    router.refresh()
  }

  const sendResetPasswordForm = async () => {
    try {
      const response = await sendPasswordReset({ userId: user.id })
      if (response.success) {
        success("Admin Action", response.message);
        router.refresh()
      } else {
        error("Admin Action", response.message);
      }
    } catch (err) {
      error("Admin Action", "Failed to send password reset email");
    }
  }

  const disableTwoFactor = async () => {
    try {
      const response = await submitDisableTwoFa({ userId: user.id })
      if (response.success) {
        success("Admin Action", response.message);
      } else {
        error("Admin Action", response.message);
      }
      router.refresh()
    } catch (err) {
      error("Admin Action", "Failed to disable 2FA");
    }
  }

  const deleteUser = async () => {
    try {
      const response = await submitDeleteUser({ userId: user.id })
      if (response.success) {
        success("Admin Action", response.message);
        router.push("/admin/users")
      } else {
        error("Admin Action", response.message);
      }
    } catch (err) {
      console.error("Delete user error:", err)
      error("Admin Action", "Failed to delete user");
    }
  }

  const updateUser = async (updatedData) => {
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updatedData),
      })

      if (response.ok) {
        success("Admin Action", "User updated successfully");
        router.refresh()
      } else {
        throw new Error("Failed to update user")
      }
    } catch (err) {
      console.error("Update user error:", err)
      error("Admin Action", "Failed to update user");
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreVertical className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem className={cn("text-green-500")} onClick={() => setChangingRole(true)}>
            <Shield className="w-4 h-4 mr-2" />
            Change Role
          </DropdownMenuItem>
          {!user.verified && (
            <DropdownMenuItem onClick={() => updateUser({ verified: true })}>
              <Check className="w-4 h-4 mr-2" />
              Verify User
            </DropdownMenuItem>
          )}
          <DropdownMenuItem className={cn("text-orange-500")} onClick={sendResetPasswordForm}>
            <MailWarning className="w-4 h-4 mr-2" />
            Reset Password
          </DropdownMenuItem>
          {user.UserSettings?.[0]?.twoFactorEnabled && (
            <DropdownMenuItem className={cn("text-orange-700")} onClick={disableTwoFactor}>
              <LockKeyholeOpen className="w-4 h-4 mr-2" />
              Disable 2FA
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            className={cn(user.banReason?.length > 0 ? "text-green-500" : "text-destructive")}
            onClick={() => setBanningUser(true)}
          >
            <Ban className="w-4 h-4 mr-2" />
            {user.banReason?.length > 0 ? "Unban User" : "Ban User"}
          </DropdownMenuItem>
          <DropdownMenuItem className="text-destructive" onClick={deleteUser}>
            <Trash className="w-4 h-4 mr-2" />
            Delete User
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={changingRole} onOpenChange={setChangingRole}>
        <DialogContent>
          <ChangeRoleDialog onComplete={onChangeRoleComplete} roles={roles} userId={user.id} />
        </DialogContent>
      </Dialog>

      <Dialog open={banningUser} onOpenChange={setBanningUser}>
        <DialogContent>
          <BanUserDialog banReasons={banReasons} userId={user.id} onComplete={onBanUserComplete} />
        </DialogContent>
      </Dialog>
    </>
  )
}