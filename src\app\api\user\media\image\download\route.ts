import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {imageId} = await request.json();
    if (!imageId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No Image ID provided."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Conflict, message: "No user session found."});
    }

    const imageDownloadRequest = await fetch(process.env.VIDEO_API_URL + "/images/download/" + imageId, {
        method: "GET",
        headers: {
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        }
    });

    if (!imageDownloadRequest.ok || !imageDownloadRequest.body) {
        const errorText = await imageDownloadRequest.text();
        return new NextResponse(errorText, { status: 500 });
    }

    const headers = new Headers();
    headers.set('Content-Type', 'image/png');
    headers.set('Content-Disposition', `attachment; filename="${imageId}.png"`);

    return new NextResponse(imageDownloadRequest.body, {
        status: 200,
        headers,
    });
}