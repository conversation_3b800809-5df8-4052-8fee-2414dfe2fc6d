"use client";

import { useEffect } from "react";
import Image from "next/image";

async function handleDiscordLogin() {
  const params = new URLSearchParams(window.location.search);
  const code = params.get("code");

  if (code) {
    const response = await fetch(`/api/auth/discord?code=${code}`);
    const data = await response.json();

    if (data.redirect) {
      setTimeout(() => {
        window.location.href = data.redirect;
      }, 5000);
    }
  }
}

export default function DiscordLoginPageClient() {
  useEffect(() => {
    handleDiscordLogin();
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background text-foreground">
      <Image
        src="/Streambliss-logo.png"
        alt="Streambliss Logo"
        width={384}
        height={384}
        className="mb-6"
      />
      <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-8"></div>
      <h1 className="text-2xl font-semibold mb-2">Authenticating with Discord...</h1>
      <p className="text-muted-foreground">Please wait while we securely log you in.</p>
    </div>
  );
}
