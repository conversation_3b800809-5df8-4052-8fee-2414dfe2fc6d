"use client";

import { useState } from "react";
import type { Package, $Enums } from "@prisma/client";
import { Star } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitBilling from "./_actions/submit-billing";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import cancelSubscription from "./_actions/cancel-subscription";
import { DashboardWrapper } from "../_components/dashboard-wrapper";
import { DashboardHeader } from "../_components/dashboard-header";

type Notification = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type: $Enums.NotificationType;
  data: string;
  read: boolean;
};

const plans = [
  {
    name: "Free",
    monthlyPrice: "$00",
    yearlyPrice: "$00",
    period: "/month",
    description: "Perfect for personal use",
    features: [
      "250mb per upload limit",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
    ],
    package: "FREE" as Package,
    bgClass: "bg-[rgba(17,0,24,0.12)]",
  },
  {
    name: "Pro",
    monthlyPrice: "$10",
    yearlyPrice: "$8",
    period: "/month",
    description: "For individual creators",
    features: [
      "Up to 10GB per video",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
    ],
    package: "PRO" as Package,
    popular: true,
    bgClass: "bg-[#110018]",
  },
  {
    name: "Creator",
    monthlyPrice: "$25",
    yearlyPrice: "$20",
    period: "/month",
    description: "For content groups",
    features: [
      "Unlimited video size",
      "Unlimited video retention",
      "Advanced analytics",
      "Ad-free content delivery",
      "Priority support",
      "Team collaboration",
    ],
    package: "CREATOR" as Package,
    bgClass: "bg-[rgba(17,0,24,0.12)]",
  },
];

const getSubscriptionFeatures = (packageType: Package) => {
  switch (packageType) {
    case "FREE":
      return [
        "250mb per upload limit",
        "180 days video retention",
        "Basic analytics",
        "Ad-Supported Content",
      ];
    case "PRO":
      return [
        "Up to 10GB per video",
        "Unlimited video retention",
        "Advanced analytics",
        "Ad-free content delivery",
        "Priority support",
      ];
    case "CREATOR":
      return [
        "Unlimited video size",
        "Unlimited video retention",
        "Advanced analytics",
        "Ad-free content delivery",
        "Priority support",
        "Team collaboration",
      ];
    default:
      return [
        "250mb per upload limit",
        "180 days video retention",
        "Basic analytics",
        "Ad-Supported Content",
      ];
  }
};

interface BillingPageClientProps {
  currentPackage: Package;
  userName: string;
  userId: string;
  userImage: string | null;
  notifications: Notification[];
  hasAccessToAdmin: boolean;
}

export default function BillingPageClient({
  currentPackage,
  userName,
  userId,
  userImage,
  notifications,
  hasAccessToAdmin,
}: BillingPageClientProps) {
  const [isLoading, setIsLoading] = useState<Package | null>(null);
  const [isYearly, setIsYearly] = useState(false);
  const { success, error, info } = useEnhancedToast();
  const [showDowngradeDialog, setShowDowngradeDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);

  const handleUpgrade = async (packageToUpgrade: Package) => {
    if (packageToUpgrade === currentPackage) {
      info("Already Subscribed", "You are already subscribed to this package");
      return;
    }

    // Show confirmation dialog when downgrading to Free plan
    if (packageToUpgrade === "FREE" && currentPackage !== "FREE") {
      setSelectedPackage(packageToUpgrade);
      setShowDowngradeDialog(true);
      return;
    }

    setIsLoading(packageToUpgrade);
    try {
      const response = await fetch("/api/billing/create", {
        method: "POST",
        body: JSON.stringify({
          plan: packageToUpgrade
        })
      });

      const data = await response.json();
      if (!(data.status >= 200 &&  data.status < 300)) {
        error("Upgrade Failed", data.message);
        return;
      }

      if (data.checkoutUrl) {
        window.location.href = data.checkoutUrl;
      }
    } catch (err) {
      error("Upgrade Error", "Failed to process upgrade. Please try again later.");
    } finally {
      setIsLoading(null);
    }
  };

  const handleCancelSubscription = async () => {
    setIsCancelling(true);
    try {
      const response = await cancelSubscription();
      if (response.success) {
        success("Subscription Cancelled", response.message);
        setShowCancelDialog(false);
        window.location.reload();
      } else {
        error("Cancellation Failed", response.message || "Failed to cancel subscription. Please try again later.");
      }
    } catch (err) {
      error("Cancellation Error", "Failed to cancel subscription. Please try again later.");
    } finally {
      setIsCancelling(false);
    }
  };

  const confirmDowngrade = async () => {
    if (!selectedPackage) return;

    setShowDowngradeDialog(false);
    setIsLoading(selectedPackage);
    try {
      const response = await submitBilling({ plan: selectedPackage });
      if (response.success && response.sessionId != null) {
        window.location.href = response.sessionId;
      }
    } catch (err) {
      error("Downgrade Error", "Failed to process downgrade. Please try again later.");
    } finally {
      setIsLoading(null);
      setSelectedPackage(null);
    }
  };

  return (
    <DashboardWrapper
      userName={userName}
      userImage={userImage}
      notifications={notifications}
      className="font-['Montserrat']"
    >
      <DashboardHeader
        userName={userName}
        userImage={userImage}
        notifications={notifications}
        hasAccessToAdmin={hasAccessToAdmin}
        title="Billing & Subscription"
        description="Manage your subscription plan and billing information."
      />

      {/* Monthly/Yearly Toggle */}
      <div className="flex justify-center mb-8">
        <div className="w-full max-w-[370px] max-md:max-w-[300px] mx-auto bg-white/5 border border-white/24 rounded-full h-18.5 max-md:h-14.5 shadow-[0px_0px_6.5px_0px_#D74CB661_inset] relative flex justify-between items-center overflow-hidden">
          <div
            className={`absolute top-1/2 -translate-y-1/2 h-14.5 max-md:h-10.5 bg-gradient-to-b from-[#B851E0] to-[#EB489B] rounded-full duration-200 ease-linear ${
              isYearly
                ? "translate-x-[92%] max-md:translate-x-[83%] w-47 max-md:w-40 max-sm:w-40"
                : "translate-x-[4%] w-36.5 max-md:w-30 max-sm:w-28"
            }`}
          ></div>
          <button
            onClick={() => setIsYearly(false)}
            className="py-[14.5px] text-white font-bold leading-[160%] text-lg z-[1] cursor-pointer max-md:text-base absolute top-1/2 -translate-y-1/2 left-9 max-md:left-7 font-['Montserrat']"
          >
            Monthly
          </button>
          <button
            onClick={() => setIsYearly(true)}
            className="py-[14.5px] text-white font-bold leading-[160%] text-lg absolute z-[1] cursor-pointer flex items-center gap-2 max-md:text-base top-1/2 -translate-y-1/2 right-6 max-md:right-4.5 font-['Montserrat']"
          >
            Yearly
            <span className="px-2 py-[1px] text-white text-sm leading-[160%] font-medium border border-white rounded-full bg-[#D04DBF1F] max-md:px-1.5 max-md:text-xs">
              Save 20%
            </span>
          </button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="flex items-stretch gap-6 mb-8">
        {plans.map((plan, index) => (
          <div
            key={plan.name}
            className={`flex-1 ${plan.bgClass} border border-white/12 rounded-xl p-8 relative min-h-[420px]`}
          >
            <div className="flex flex-col h-full">
              <div className="flex-1 space-y-4">
                {/* Header */}
                <div className="space-y-2">
                  <div className="flex items-center gap-3 min-h-[32px]">
                    <h3 className="text-white text-xl font-semibold font-['Montserrat']">
                      {plan.name}
                    </h3>
                    {plan.popular && (
                      <div className="flex items-center justify-center px-3 py-1 border border-white rounded-full bg-gradient-to-b from-[rgba(184,81,224,0.12)] to-[rgba(235,72,155,0.12)]">
                        <span className="text-white text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="text-white leading-[120%]">
                    <span className="text-2xl font-bold font-['Montserrat']">
                      {isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                    </span>
                    <span className="text-lg font-normal font-['Montserrat']">
                      {plan.period}
                    </span>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-4">
                  <div>
                    <p className="text-white text-sm font-semibold font-['Montserrat']">
                      {plan.description}
                    </p>
                  </div>

                  {/* Features */}
                  <div className="space-y-3">
                    {plan.features.map((feature) => (
                      <div key={feature} className="flex items-start gap-2">
                        <div className="relative w-5 h-5 flex-shrink-0 mt-0.5">
                          <svg
                            className="w-5 h-5"
                            viewBox="0 0 27 27"
                            fill="none"
                          >
                            <circle
                              cx="13.5"
                              cy="13.5"
                              r="13.5"
                              fill="#EB489B"
                            />
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M12.3723 18.3364L18.7053 10.4194L17.5613 9.50415L12.1611 16.2523L8.336 13.0652L7.39733 14.1916L12.3723 18.3364Z"
                              fill="white"
                            />
                          </svg>
                        </div>
                        <span className="text-white/70 text-sm font-normal leading-[160%] font-['Montserrat']">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Button */}
              <div className="mt-6">
                <Button
                  onClick={() => {
                    const currentPlanIndex = plans.findIndex(p => p.package === currentPackage);
                    const targetPlanIndex = plans.findIndex(p => p.package === plan.package);

                    if (targetPlanIndex < currentPlanIndex) {
                      setSelectedPackage(plan.package);
                      setShowDowngradeDialog(true);
                    } else {
                      handleUpgrade(plan.package);
                    }
                  }}
                  disabled={
                    isLoading === plan.package ||
                    currentPackage === plan.package
                  }
                  className={`w-full h-auto py-3 px-4 rounded-full text-sm font-semibold leading-[160%] transition-all duration-300 font-['Montserrat'] ${
                    currentPackage === plan.package
                      ? "bg-gradient-to-b from-[#B851E0] to-[#EB489B] text-white opacity-50 cursor-not-allowed"
                      : plans.findIndex(p => p.package === plan.package) < plans.findIndex(p => p.package === currentPackage)
                        ? "bg-red-500/10 border border-red-400/30 text-red-200 hover:bg-red-500/20 hover:border-red-400/50 hover:text-red-100"
                        : plan.popular
                          ? "bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white"
                          : "bg-transparent border border-white text-white opacity-70 hover:opacity-100 hover:bg-white/5"
                  }`}
                >
                  {currentPackage === plan.package
                    ? "Current Plan"
                    : plans.findIndex(p => p.package === plan.package) < plans.findIndex(p => p.package === currentPackage)
                      ? "Downgrade"
                      : "Upgrade"}
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Bottom Section */}
      <div className="flex items-stretch gap-4">
        {/* Need something else? */}
        <div className="flex-1 bg-[rgba(17,0,24,0.12)] border border-white/12 rounded-xl p-4 min-h-[200px]">
          <div className="flex flex-col h-full">
            <div className="flex-1 space-y-4">
              <div className="space-y-2">
                <h3 className="text-white text-lg font-semibold leading-[160%]">
                  Need something else?
                </h3>
                <p className="text-white/70 text-sm font-normal leading-[160%]">
                  Contact Our sales team for custom enterprise solution tailored
                  to your needs.
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Button
                className="bg-transparent border border-white text-white text-sm font-semibold py-2 px-4 rounded-full opacity-70 hover:opacity-100 hover:bg-white/5"
                asChild
              >
                <a href="mailto:<EMAIL>">Contact Sales</a>
              </Button>
            </div>
          </div>
        </div>

        {/* Subscription */}
        <div className="flex-1 bg-[rgba(17,0,24,0.12)] border border-white/12 rounded-xl p-4 min-h-[200px]">
          <div className="flex flex-col h-full">
            <div className="flex-1 space-y-4">
              <h3 className="text-white text-lg font-semibold leading-[160%] font-['Montserrat']">
                Subscription
              </h3>

              <div className="flex items-start gap-4">
                <div className="space-y-3">
                  {getSubscriptionFeatures(currentPackage).slice(0, Math.ceil(getSubscriptionFeatures(currentPackage).length / 2)).map((feature) => (
                    <div key={feature} className="flex items-start gap-2">
                      <div className="w-5 h-5 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Star className="w-4 h-4 text-[#FFDE09] fill-current" />
                      </div>
                      <span className="text-white/70 text-sm font-normal leading-[160%] font-['Montserrat']">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="space-y-3">
                  {getSubscriptionFeatures(currentPackage).slice(Math.ceil(getSubscriptionFeatures(currentPackage).length / 2)).map((feature) => (
                    <div key={feature} className="flex items-start gap-2">
                      <div className="w-5 h-5 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Star className="w-4 h-4 text-[#FFDE09] fill-current" />
                      </div>
                      <span className="text-white/70 text-sm font-normal leading-[160%] font-['Montserrat']">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              {currentPackage === "FREE" ? (
                <Button
                  onClick={() => handleUpgrade("PRO")}
                  className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white text-sm font-semibold py-2 px-4 rounded-full font-['Montserrat']"
                >
                  Upgrade Plan
                </Button>
              ) : (
                <div className="space-y-2">
                  <div className="text-white/70 text-sm font-medium">
                    Current Plan: {currentPackage}
                  </div>
                  <Button
                    onClick={() => setShowCancelDialog(true)}
                    variant="outline"
                    className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-500 text-sm font-semibold py-2 px-4 rounded-full font-['Montserrat'] transition-all duration-200"
                  >
                    Cancel Subscription
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Downgrade to Free confirmation dialog */}
      <Dialog open={showDowngradeDialog} onOpenChange={setShowDowngradeDialog}>
        <DialogContent className="bg-[#110018] border border-white/24">
          <DialogHeader>
            <DialogTitle className="text-white">
              Downgrade to Free Plan
            </DialogTitle>
            <DialogDescription className="text-white/70">
              Are you sure you want to downgrade to the Free plan? You will lose
              access to premium features immediately.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDowngradeDialog(false)}
              className="border-white/24 text-white hover:bg-white/5"
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDowngrade}
              className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white"
            >
              Confirm Downgrade
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Subscription confirmation dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent className="bg-[#110018] border border-white/24">
          <DialogHeader>
            <DialogTitle className="text-white">
              Cancel Subscription
            </DialogTitle>
            <DialogDescription className="text-white/70">
              Are you sure you want to cancel your subscription? You will still
              have access to your current plan until the end of your billing
              period.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
              className="border-white/24 text-white hover:bg-white/5"
            >
              No, Keep My Plan
            </Button>
            <Button
              onClick={handleCancelSubscription}
              disabled={isCancelling}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isCancelling ? "Cancelling..." : "Yes, Cancel Subscription"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardWrapper>
  );
}