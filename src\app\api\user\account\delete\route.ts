import {NextRequest, NextResponse} from "next/server"
import { deleteUserAccount } from "@/lib/db/user"
import { getUserSession } from "src/server/session"
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function DELETE(request: NextRequest) {
  const userSession = await getUserSession();
  if (!userSession || !userSession.userId || !userSession.accessToken) {
    return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No active user session."});
  }

  const deleteResponse = await fetch(process.env.VIDEO_API_URL + "/users/delete/" + userSession.userId, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${userSession.accessToken}`,
      "x-api-key": process.env.API_SERVER_KEY!
    }
  });

  const data = await deleteResponse.json();
  if (!deleteResponse.ok) {
    return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
  }

  userSession.userId = "";
  userSession.accessToken = "";
  userSession.destroy();

  return NextResponse.json({status: HttpStatusCode.Ok, success: true});
}