"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Home, Search, HelpCircle } from "lucide-react";
import { motion } from "framer-motion";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-black text-white overflow-hidden relative">
      {/* Background Effects */}
      <div className="fixed inset-0 pointer-events-none">
        <div
          className="absolute w-[800px] h-[800px] rounded-full opacity-20 blur-[120px]"
          style={{
            background:
              "radial-gradient(circle, rgba(184, 81, 224, 0.3) 0%, rgba(235, 72, 155, 0.2) 40%, transparent 70%)",
            top: "30%",
            left: "20%",
            transform: "translate(-50%, -50%)",
          }}
        />
        <div
          className="absolute w-[600px] h-[600px] rounded-full opacity-15 blur-[100px]"
          style={{
            background:
              "radial-gradient(circle, rgba(235, 72, 155, 0.2) 0%, rgba(184, 81, 224, 0.1) 50%, transparent 80%)",
            top: "70%",
            right: "20%",
            transform: "translate(50%, -50%)",
          }}
        />
        <div
          className="absolute w-[400px] h-[400px] rounded-full opacity-10 blur-[80px]"
          style={{
            background:
              "radial-gradient(circle, rgba(184, 81, 224, 0.15) 0%, rgba(235, 72, 155, 0.05) 50%, transparent 80%)",
            top: "10%",
            right: "80%",
            transform: "translate(-50%, -50%)",
          }}
        />
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
            backgroundSize: "128px 128px",
          }}
        />
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(25)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-white/5"
              style={{
                width: `${Math.random() * 6 + 2}px`,
                height: `${Math.random() * 6 + 2}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 15 + 20}s linear infinite`,
                animationDelay: `${Math.random() * 10}s`,
                opacity: Math.random() * 0.4 + 0.1,
              }}
            />
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        <motion.div
          className="text-center max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Logo */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Link href="/" className="inline-block mb-4">
              <Image
                src="/assets/images/svg/footer-logo.svg"
                alt="StreamBliss"
                width={240}
                height={80}
                className="h-16 md:h-20 w-auto mx-auto"
              />
            </Link>
            <div className="w-16 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full"></div>
          </motion.div>

          {/* Error Number */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="relative">
              <h1
                className="text-8xl md:text-9xl lg:text-[12rem] font-bold mb-4 bg-gradient-to-r from-purple-400 via-pink-500 to-purple-600 bg-clip-text text-transparent"
                style={{
                  textShadow: "0 0 40px rgba(184, 81, 224, 0.3)",
                }}
              >
                404
              </h1>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-3xl rounded-full scale-75"></div>
            </div>
          </motion.div>

          {/* Error Message */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Oops! Page Not Found
            </h2>
            <p className="text-lg md:text-xl text-gray-400 leading-relaxed max-w-lg mx-auto mb-6">
              The page you&apos;re looking for seems to have vanished into the
              digital void. Don&apos;t worry, even the best explorers sometimes
              take a wrong turn.
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-white/5 border border-white/10 rounded-full">
              <span className="text-gray-500 text-sm">
                Error Code: 404 - Not Found
              </span>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <Button
              asChild
              className="w-full sm:w-auto bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
            >
              <Link href="/" className="inline-flex items-center">
                <Home className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="w-full sm:w-auto border-white/20 text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300"
            >
              <Link href="/community" className="inline-flex items-center">
                <Search className="w-5 h-5 mr-2" />
                Explore Content
              </Link>
            </Button>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-300 mb-4">
                Looking for something specific?
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <Link
                  href="/dashboard"
                  className="group p-4 bg-white/5 border border-white/10 rounded-xl hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="text-purple-400 font-medium group-hover:text-purple-300 transition-colors">
                    Dashboard
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    Manage your content
                  </div>
                </Link>
                <Link
                  href="/community"
                  className="group p-4 bg-white/5 border border-white/10 rounded-xl hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="text-purple-400 font-medium group-hover:text-purple-300 transition-colors">
                    Community
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    Discover videos
                  </div>
                </Link>
                <Link
                  href="/help"
                  className="group p-4 bg-white/5 border border-white/10 rounded-xl hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="text-purple-400 font-medium group-hover:text-purple-300 transition-colors">
                    Help Center
                  </div>
                  <div className="text-sm text-gray-500 mt-1">Get support</div>
                </Link>
              </div>
            </div>

            {/* Help Section */}
            <div className="pt-8 border-t border-white/10">
              <div className="flex items-center justify-center gap-2 text-gray-500 text-sm">
                <HelpCircle className="w-4 h-4" />
                <span>Still need help?</span>
                <Link
                  href="/contact"
                  className="text-purple-400 hover:text-purple-300 font-medium transition-colors duration-300"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative Elements */}
      <motion.div
        className="absolute top-20 left-10 w-2 h-2 bg-purple-500 rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />
      <motion.div
        className="absolute bottom-32 right-16 w-3 h-3 bg-pink-500 rounded-full"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1,
        }}
      />
      <motion.div
        className="absolute top-1/3 right-24 w-1 h-1 bg-purple-400 rounded-full"
        animate={{
          scale: [1, 2, 1],
          opacity: [0.4, 0.9, 0.4],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2,
        }}
      />
    </div>
  );
}