"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  User,
  Eye,
  ExternalLink,
  MessageSquare,
  Video,
  Clock,
  Check,
} from "lucide-react";
import Link from "next/link";
import { CommentReportDialog } from "./comment-report-dialog";

type Report = {
  id: string;
  status: string;
  reason: string;
  details?: string;
  createdAt?: string;
  reportedComment: {
    id: string;
    message: string;
  };
  reportedUser: {
    id: string;
    name: string;
    email?: string;
  };
  reportedBy?: {
    id: string;
    name: string;
  };
  reportedVideo: {
    shortLink: string;
    title: string;
  };
};

interface CommentReportsTableProps {
  currentUser: any;
  initCommentReports: any[];
}

export function CommentReportsTable({
  currentUser,
  initCommentReports,
}: CommentReportsTableProps) {
  const [filter] = useState("pending");
  const [commentReports] = useState(initCommentReports);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [isViewingDetails, setIsViewingDetails] = useState(false);

  const filteredReports =
    filter === "all"
      ? commentReports
      : commentReports.filter(
          (report) =>
            report.status === (filter === "pending" ? "PENDING" : "CLOSED"),
        );

  const getReasonLabel = (reason: string) => {
    switch (reason) {
      case "inappropriate":
        return "Inappropriate content";
      case "spam":
        return "Spam or misleading";
      case "harassment":
        return "Harassment";
      case "other":
        return "Other";
      default:
        return reason;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <div className="flex items-center gap-2 bg-yellow-500/20 text-yellow-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-yellow-500/30">
            <Clock className="w-3 h-3" />
            Pending
          </div>
        );
      case "OPEN":
        return (
          <div className="flex items-center gap-2 bg-red-500/20 text-red-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-red-500/30">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
            Open
          </div>
        );
      case "CLOSED":
      case "RESOLVED":
        return (
          <div className="flex items-center gap-2 bg-green-500/20 text-green-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-green-500/30">
            <Check className="w-3 h-3" />
            Resolved
          </div>
        );
      default:
        return (
          <div className="px-3 py-1.5 rounded-full bg-gray-500/20 text-gray-400 text-sm font-medium w-fit border border-gray-500/30">
            {status}
          </div>
        );
    }
  };

  if (filteredReports.length === 0) {
    return (
      <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl p-12">
        <div className="text-center">
          <div className="flex h-16 w-16 items-center justify-center mx-auto mb-4 rounded-xl bg-gray-800/40">
            <MessageSquare className="h-8 w-8 text-gray-500" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            No comment reports found
          </h3>
          <p className="text-sm text-gray-400 max-w-sm mx-auto">
            There are currently no comment reports to review. All comment
            reports will appear here when they are submitted.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/40 hover:bg-gray-800/40 border-gray-700/50">
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  User
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Video
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Comment
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Reason
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Date
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Status
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4 text-right">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => (
                <TableRow
                  key={report.id}
                  className="bg-gray-950/30 hover:bg-gray-900/50 border-gray-700/30 transition-colors"
                >
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-purple-500/30">
                        <User className="h-5 w-5 text-purple-400" />
                      </div>
                      <div>
                        <Link
                          href={`/admin/users/${report.reportedUser.id}`}
                          className="font-semibold text-white hover:text-purple-400 transition-colors"
                        >
                          {report.reportedUser.name}
                        </Link>
                        {report.reportedUser.email && (
                          <p className="text-xs text-gray-500">
                            {report.reportedUser.email}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500/20 border border-blue-500/30">
                        <Video className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <Link
                          href={`/v/${report.reportedVideo.shortLink}`}
                          className="font-medium text-white hover:text-blue-400 transition-colors flex items-center gap-1 group"
                          target="_blank"
                        >
                          {report.reportedVideo.title ||
                            report.reportedVideo.shortLink}
                          <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </Link>
                        <p className="text-xs text-gray-500">
                          {report.reportedVideo.shortLink}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="max-w-xs">
                      <div className="bg-gray-800/40 border border-gray-700/40 rounded-lg p-3">
                        <div className="flex items-start gap-2">
                          <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                          <p className="text-sm text-gray-300 line-clamp-2">
                            {report.reportedComment.message}
                          </p>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white">
                        {getReasonLabel(report.reason)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-300">
                      {format(new Date(report.createdAt), "MMM dd, yyyy")}
                    </div>
                    <div className="text-xs text-gray-500">
                      {format(new Date(report.createdAt), "HH:mm")}
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    {getStatusBadge(report.status)}
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center gap-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-gray-800/50 text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-white h-9 px-3"
                        onClick={() => {
                          setSelectedReport(report);
                          setIsViewingDetails(true);
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {selectedReport && (
        <CommentReportDialog
          isOpen={isViewingDetails}
          onClose={() => {
            setIsViewingDetails(false);
            setSelectedReport(null);
          }}
          report={selectedReport}
        />
      )}
    </>
  );
}