"use client";

import React, { useState } from "react";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Loader2,
  AlertCircle
} from "lucide-react";
import { VideoProgressBar } from "./VideoProgressBar";
import { VideoVolumeControl } from "./VideoVolumeControl";
import { VideoQualitySelector } from "./VideoQualitySelector";

// Default quality options for the video player
const defaultQualities = [
  { label: "4K60", value: "4k60", resolution: "3840x2160", fps: 60, bitrate: 25000 },
  { label: "1440p60", value: "1440p60", resolution: "2560x1440", fps: 60, bitrate: 16000 },
  { label: "1080p60", value: "1080p60", resolution: "1920x1080", fps: 60, bitrate: 8000 },
  { label: "1080p", value: "1080p", resolution: "1920x1080", fps: 30, bitrate: 5000 },
  { label: "720p60", value: "720p60", resolution: "1280x720", fps: 60, bitrate: 4500 },
  { label: "720p", value: "720p", resolution: "1280x720", fps: 30, bitrate: 2500 },
  { label: "480p", value: "480p", resolution: "854x480", fps: 30, bitrate: 1000 },
  { label: "360p", value: "360p", resolution: "640x360", fps: 30, bitrate: 600 },
  { label: "Auto", value: "auto", resolution: "Adaptive", fps: 60 },
];
import Image from "next/image";

export interface VideoControlsProps {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  showControls: boolean;
  isLoading: boolean;
  error: string | null;
  currentQuality?: string;
  onPlayPause: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number) => void;
  onMuteToggle: () => void;
  onFullscreenToggle: () => void;
  onQualityChange?: (quality: string) => void;
}

export const VideoControls: React.FC<VideoControlsProps> = ({
  isPlaying,
  currentTime,
  duration,
  volume,
  isMuted,
  isFullscreen,
  showControls,
  isLoading,
  error,
  currentQuality = "1080p60",
  onPlayPause,
  onSeek,
  onVolumeChange,
  onMuteToggle,
  onFullscreenToggle,
  onQualityChange,
}) => {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const showCenterButton = !isPlaying || isLoading || error;

  return (
    <>
      {/* Center Play/Pause Button */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          showCenterButton ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
      >
        <button
          onClick={onPlayPause}
          disabled={isLoading || !!error}
          className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <Loader2 className="w-8 h-8 md:w-10 md:h-10 text-white animate-spin" />
          ) : error ? (
            <AlertCircle className="w-8 h-8 md:w-10 md:h-10 text-white" />
          ) : isPlaying ? (
            <Pause className="w-8 h-8 md:w-10 md:h-10 text-white" fill="currentColor" />
          ) : (
            <Play className="w-8 h-8 md:w-10 md:h-10 text-white ml-1" fill="currentColor" />
          )}
        </button>
      </div>

      {/* Bottom Controls Bar */}
      <div
        className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent transition-all duration-300 ${
          showControls ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Progress Bar */}
        <div className="px-4 pt-4">
          <VideoProgressBar
            currentTime={currentTime}
            duration={duration}
            onSeek={onSeek}
          />
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between px-4 py-3" onMouseEnter={(e) => e.stopPropagation()}>
          {/* Left Controls */}
          <div className="flex items-center space-x-3">
            {/* Play/Pause */}
            <button
              onClick={onPlayPause}
              disabled={isLoading || !!error}
              className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
            >
              {isPlaying ? (
                <Pause className="w-5 h-5 text-white" fill="currentColor" />
              ) : (
                <Play className="w-5 h-5 text-white ml-0.5" fill="currentColor" />
              )}
            </button>

            {/* Volume Control */}
            <div className="flex items-center relative">
              <div className="relative">
                <button
                  onClick={onMuteToggle}
                  onMouseEnter={() => setShowVolumeSlider(true)}
                  onMouseLeave={() => setShowVolumeSlider(false)}
                  className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-200 hover:scale-105 backdrop-blur-sm"
                >
                  {isMuted || volume === 0 ? (
                    <VolumeX className="w-5 h-5 text-white" />
                  ) : (
                    <Volume2 className="w-5 h-5 text-white" />
                  )}
                </button>

                <div
                  className={`absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 transition-all duration-200 ${
                    showVolumeSlider ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
                  }`}
                  onMouseEnter={() => setShowVolumeSlider(true)}
                  onMouseLeave={() => setShowVolumeSlider(false)}
                >
                  <div className="bg-black/90 backdrop-blur-md rounded-lg px-3 py-2 border border-white/10 shadow-xl">
                    <div className="flex flex-col items-center space-y-2">
                      <span className="text-white text-xs font-medium">
                        {Math.round((isMuted ? 0 : volume) * 100)}%
                      </span>
                      <div className="w-1 h-16 bg-white/20 rounded-full relative cursor-pointer">
                        <div
                          className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-purple-500 to-pink-500 rounded-full transition-all duration-150"
                          style={{ height: `${(isMuted ? 0 : volume) * 100}%` }}
                        />
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={isMuted ? 0 : volume}
                          onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          style={{ writingMode: 'vertical-lr', WebkitAppearance: 'slider-vertical' }}
                        />
                      </div>
                    </div>
                    {/* Arrow pointing down */}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90" />
                  </div>
                </div>
              </div>
            </div>

            {/* Time Display */}
            <div className="text-white text-sm font-medium bg-black/30 px-2 py-1 rounded backdrop-blur-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          {/* Right Controls */}
          <div className="flex items-center space-x-3">
            {/* StreamBliss Logo */}
            <div className="flex items-center">
              <Image
                src="/assets/images/webp/logo.webp"
                alt="StreamBliss"
                width={80}
                height={16}
                className="h-4 w-auto opacity-90"
                priority={false}
                unoptimized
              />
            </div>

            {/* Quality Selector */}
            {onQualityChange && (
              <VideoQualitySelector
                currentQuality={currentQuality}
                qualities={defaultQualities}
                onQualityChange={onQualityChange}
              />
            )}

            {/* Fullscreen */}
            <button
              onClick={onFullscreenToggle}
              className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-200 hover:scale-105 backdrop-blur-sm"
            >
              {isFullscreen ? (
                <Minimize className="w-5 h-5 text-white" />
              ) : (
                <Maximize className="w-5 h-5 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="bg-red-600/90 text-white px-6 py-4 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">{error}</span>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default VideoControls;