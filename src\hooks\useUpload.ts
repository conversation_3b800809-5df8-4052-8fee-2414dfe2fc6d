import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import submitUploadVideo from "../app/dashboard/_actions/upload-video";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useSocket } from "@/components/socket-context";
import deleteVideoOnError from "../app/dashboard/_actions/delete-video";
import { createLog } from "@/server/logs";
import { LogConstants } from "@/server/log-constants";
import { LogActions } from "@prisma/client";

const API_URL = "https://dev-api.streambliss.cloud";

type UploadOptions = {
  file: File;
  title: string;
  userId: string;
  authToken: string;
  onSuccess?: (videoId: string) => void;
};

export function useVideoUpload() {
  const { error } = useEnhancedToast();
  const [status, setStatus] = useState<
    "idle" | "uploading" | "cancelled" | "error" | "success"
  >("idle");
  const [uploading, setUploading] = useState(false);
  const xhrRef = useRef<XMLHttpRequest | null>(null);
  const router = useRouter();
  const { sendEvent, isConnected } = useSocket();

  async function uploadVideo({
    file,
    title,
    userId,
    authToken,
    onSuccess,
  }: UploadOptions) {
    if (!file) return;

    try {
      setStatus("uploading");
      setUploading(true);

      const uploadData = await submitUploadVideo({
        title,
        userId,
        fileName: file.name,
      });

      if (!uploadData.success || !uploadData.videoId) {
        error("Upload Failed", uploadData.message);
        return;
      }

      const data = new FormData();
      data.append("file", file);
      data.append("videoId", uploadData.videoId);
      data.append("userId", userId);

      const xhr = new XMLHttpRequest();
      xhrRef.current = xhr;

      xhr.open("POST", API_URL + "/video/upload", true);
      xhr.setRequestHeader("Auth-Token", authToken);

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable && isConnected) {
          const percent = Math.round((event.loaded / event.total) * 100);

          sendEvent(
            JSON.stringify({
              type: "rely",
              userId,
              progress: percent,
              status: "processing",
            }),
          );
        }
      };

      xhr.onload = async () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          setStatus("success");

          await createLog(
            userId,
            LogConstants.UPLOADED_VIDEO,
            LogActions.VIDEO,
          );

          if (onSuccess && uploadData.videoId) {
            onSuccess(uploadData.videoId);
          }

          router.refresh();
        } else {
          setStatus("error");
          error("Upload Failed", "Server responded with error");
        }
      };

      xhr.onerror = async () => {
        if (uploadData.videoId == null) return;
        const deleteResponse = await deleteVideoOnError({
          videoId: uploadData.videoId,
        });
        if (deleteResponse.success) {
          error("Upload Error", "An error occurred while uploading your video. It has been deleted.");
        }
      };

      xhr.send(data);

      return {
        success: true,
      };
    } catch (err) {
      error("Upload Error", "Upload has failed.");
    } finally {
      setUploading(false);
    }
  }

  function cancelUpload() {
    if (xhrRef.current) {
      xhrRef.current.abort();

      setStatus("cancelled");
    }
  }

  function refreshPage() {
    router.refresh();
  }

  return {
    uploadVideo,
    uploading: status === "uploading",
    cancelUpload,
    status,
    refreshPage,
  };
}