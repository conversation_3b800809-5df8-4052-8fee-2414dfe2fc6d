import { getUser, getUserSession } from "@/server/session"
import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getUserSettings } from "@/server/user-settings";

const API_URL = process.env.VIDEO_API_URL || "http://localhost:9999";

export async function GET(req: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getUser()

    const video = await prisma.video.findUnique({
      where: { id: params.id },
    })

    if (!video || !user || video.userId !== user.id) {
      return new NextResponse("Not found", { status: 404 })
    }

    return NextResponse.json(video)
  } catch (error) {
    console.error("Video fetch error:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(req: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getUserSession()
    const userSettings = await getUserSettings(user.userId);
    if (!userSettings) {
      return new NextResponse("Internal error", { status: 500 })
    }

    const video = await prisma.video.findUnique({
      where: { id: params.id },
      select: { userId: true },
    })

    if (!video || video.userId !== user.userId) {
      return new NextResponse("Not found", { status: 404 })
    }

    const deletion = await fetch(API_URL + `/video/` + user.userId + "/" + params.id, {
      method: "DELETE",
      headers: {
        "Auth-Token": userSettings.secretToken,
      }
    });

    if (!deletion.ok) {
      return new NextResponse("Internal error", { status: 500 })
    }

    await prisma.video.delete({
      where: { id: params.id },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("Delete error:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}