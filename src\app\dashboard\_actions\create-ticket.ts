"use server"

import { prisma } from "@/lib/prisma"
import { rateLimiter } from "@/lib/rate-limit"
import { getClientIp } from "@/server/geolocation"
import { createNotification } from "@/server/notifications"
import { getUserSession } from "@/server/session"
import { revalidatePath } from "next/cache"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

interface CreateTicketResponse {
  success: boolean
  message: string
  ticket?: {
    id: string
    [key: string]: any
  }
}

export default async function submitCreateTicket({
  title,
  content,
  priority,
  attachments = [],
}: {
  title: string
  content: string
  priority: string
  attachments?: string[]
}): Promise<CreateTicketResponse> {
  try {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
      return {
        success: false,
        message: "You have exceeded the rate limit. Please try again later."
      }
    }
    
    const session = await getUserSession()
    if (!session?.userId) {
      return {
        success: false,
        message: "Not authenticated",
      }
    }

    const ticket = await prisma.ticket.create({
      data: {
        title: title.trim(),
        content: content,
        priority: priority as any,
        userId: session.userId,
      },
    })

    await prisma.ticketActivity.create({
      data: {
        ticketId: ticket.id,
        userId: session.userId,
        action: "CREATED",
      },
    })

    await createNotification(session.userId, "You have created a support ticket", "INFO");
    await createLog(session.userId, LogConstants.CREATED_SUPPORT_TICKET, LogActions.TICKET);

    revalidatePath("/dashboard/tickets")

    return {
      success: true,
      message: "Ticket created successfully",
      ticket,
    }
  } catch (error) {
    console.error("[CREATE_TICKET]", error)
    return {
      success: false,
      message: "Something went wrong",
    }
  }
}