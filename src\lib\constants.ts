export const MAX_FILE_SIZE = 512 * 1024 * 1024 // 512MB
export const ALLOWED_VIDEO_TYPES = [
  "video/mp4",
  "video/webm",
  "video/ogg",
  "video/quicktime",
]

export const PACKAGES = {
  FREE: {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    maxVideos: 10,
    features: ["Basic video hosting", "720p quality", "Public videos"],
  },
  PRO: {
    maxFileSize: 250 * 1024 * 1024, // 250MB
    maxVideos: 50,
    features: ["HD video hosting", "1080p quality", "Private videos", "Analytics"],
  },
  CREATOR: {
    maxFileSize: 512 * 1024 * 1024, // 512MB
    maxVideos: 100,
    features: [
      "4K video hosting",
      "Unlimited storage",
      "Team management",
      "Advanced analytics",
    ],
  },
} as const