import {NextResponse} from "next/server";
import {getUserSession} from "@/server/session";

export async function POST(request: Request): Promise<Response> {
    const {email, token} = await request.json();
    if (!email || !token) {
        return new Response('Invalid email or token', {status: 401});
    }

    const emailVerifyRequest = await fetch(process.env.VIDEO_API_URL + '/email-verification/verify', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            email,
            token
        }),
    });

    const data = await emailVerifyRequest.json();
    if (!data) {
        return NextResponse.json({status: 401, message: "Wrong verification token"});
    }

    if (data.user && data.accessToken) {
        const session = await getUserSession();
        session.userId = data.user.id;
        session.accessToken = data.accessToken;
        await session.save();
        return NextResponse.json({status: 200});
    }

    return NextResponse.json({status: 500, message: "Error while verifying token"});
}