"use server"

import { getUserById } from "@/lib/db/user"
import { rateLimiter } from "@/lib/rate-limit"
import { hasPermission } from "@/server/admin"
import { updateCommentReportStatus } from "@/server/comment-reports"
import { getClientIp } from "@/server/geolocation"
import { getUserSession } from "@/server/session"
import {LogActions, Prisma} from "@prisma/client"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

type ReportStatus = "OPEN" | "PENDING" | "CLOSED"

type ResolveCommentReportParams = {
  reportId: string
  status: ReportStatus
}

export async function resolveCommentReport({ reportId, status }: ResolveCommentReportParams) {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later.",
    }
  }

  try {
    const userSession = await getUserSession();
    if (!userSession) {
      return {
        success: false,
        message: "User session not found. Please log in again.",
      }
    }

    const userId = userSession.userId;
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        message: "User not found. Please log in again.",
      }
    }

    if (!await hasPermission(user.roleId, ["REPORTS_EDIT"])) {
      return {
        success: false,
        message: "You do not have permission to resolve reports.",
      }
    }

    const report = await updateCommentReportStatus(reportId, status);
    if (!report) {
      return {
        success: false,
        message: "Error updating comment.",
      }
    }

    await createLog(user.id, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_REPORTS_CLOSED, LogActions.REPORT);

    return {
      success: true,
      message: "Report resolved successfully",
    }
  } catch (error) {
    console.error("Error resolving comment report:", error)
    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    }
  }
}