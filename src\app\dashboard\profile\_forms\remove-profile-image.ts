"use server";

import { decryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { getUserSettings } from "@/server/user-settings";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callack = {
  success: boolean;
  message: string;
}

export default async function submitRemoveProfileImage(): Promise<Callack> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later."
    }
  }

  const session = await getUserSession();
  if (!session) {
    return {
      success: false,
      message: "No session found."
    }
  }

  const user = await getUserById(session.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found."
    }
  }

  const userSettings = await getUserSettings(user.id);
  if (!userSettings) {
    return {
      success: false,
      message: "User settings not found."
    }
  }

  const decrypted = decryptData(userSettings.secretToken, userSettings.secretTokenIv);
  if (!decrypted) {
    return {
      success: false,
      message: "Failed to decrypt token."
    }
  }

  const response = await fetch(process.env.VIDEO_API_URL + "/profile/delete/" + user.id, {
    method: 'DELETE',
    headers: {
      "Auth-Token": decrypted
    }
  });

  if (!response.ok) {
    const error = await response.json();
    return {
      success: false,
      message: error.message || "Failed to remove profile image."
    }
  }
  await createLog(user.id, LogConstants.PROFILE_PICTURE_UPDATED, LogActions.ACCOUNT);

  return {
    success: true,
    message: "Profile image removed."
  }
}
