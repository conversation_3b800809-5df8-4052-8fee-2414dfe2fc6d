import {NextRequest, NextResponse} from "next/server"
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";

interface FetchRequestInit extends RequestInit {
    duplex?: 'half';
}

export const config = {
    api: {
        bodyParser: false,
    },
};

export async function POST(request: NextRequest) {
    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No valid user-session."});
    }

    const response = await fetch(process.env.VIDEO_API_URL + "/images/upload", {
        method: "POST",
        headers: {
            'content-type': request.headers.get('content-type') || '',
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        },
        body: request.body,
        duplex: 'half'
    } as FetchRequestInit);

    if (!response.ok) {
        const text = await response.text();
        console.log(text)
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: text});
    }

    const data = await response.json();
    return NextResponse.json({status: HttpStatusCode.Ok, success: true, image: data});
}
