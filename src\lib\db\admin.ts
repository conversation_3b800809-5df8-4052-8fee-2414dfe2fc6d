import { prisma } from "@/lib/prisma";
import { getRoleById } from "@/server/role";
import type { Package } from "@prisma/client";

export type UserWithStats = {
  id: string;
  email: string;
  name: string | null;
  isAdmin: boolean;
  package: Package;
  verified: boolean;
  createdAt: Date;
  updatedAt: Date;
  videosCount: number;
  totalViews: number;
  twoFactorEnabled?: boolean;
  banned: boolean;
  imagesCount: number;
  roleId: number;
};

export async function getUsers(): Promise<UserWithStats[]> {
  const users = await prisma.user.findMany({
    include: {
      _count: {
        select: { videos: true, Image: true },
      },
      videos: {
        select: { views: true },
      },
      UserSettings: {
        select: { twoFactorEnabled: true },
      },
      banReason: {
        select: { reason: true },
      },
    },
    orderBy: { createdAt: "desc" },
  });

  return await Promise.all(
    users.map(async (user) => {
      const role = await getRoleById(user.roleId);
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        package: user.package,
        verified: user.verified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        videosCount: user._count.videos,
        imagesCount: user._count.Image,
        totalViews: user.videos.reduce((sum, video) => sum + video.views, 0),
        twoFactorEnabled: user.UserSettings[0]?.twoFactorEnabled || false,
        isAdmin: role == null ? false : role.permissions.length > 0,
        banned: user.banReason.length > 0,
        roleId: user.roleId,
        hashedPassword: user.hashedPassword,
      };
    }),
  );
}

export async function updateUser(
  userId: string,
  data: {
    name?: string | null;
    isAdmin?: boolean;
    package?: Package;
    verified?: boolean;
  },
) {
  return await prisma.user.update({
    where: { id: userId },
    data,
  });
}

export async function deleteUser(userId: string) {
  // The cascade will handle related records deletion
  return await prisma.user.delete({
    where: { id: userId },
  });
}

export async function getAdminStats() {
  const [
    totalUsers,
    totalVideos,
    totalViews,
    recentUsers,
    recentVideos,
    packageStats,
    verificationStats,
    // Report statistics
    totalReports,
    openReports,
    pendingReports,
    closedReports,
    reportReasons,
    // Image statistics
    totalImages,
    recentImages,
    // Ticket statistics
    totalTickets,
    openTickets,
    inProgressTickets,
    closedTickets,
    resolvedTickets,
  ] = await Promise.all([
    prisma.user.count(),
    prisma.video.count(),
    prisma.video.aggregate({
      _sum: { views: true },
    }),
    prisma.user.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    }),
    prisma.video.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    }),
    prisma.user.groupBy({
      by: ["package"],
      _count: true,
    }),
    prisma.user.groupBy({
      by: ["verified"],
      _count: true,
    }),
    // Get total reports count
    prisma.reportCase.count(),
    // Get open reports count
    prisma.reportCase.count({
      where: { status: "OPEN" },
    }),
    // Get pending reports count
    prisma.reportCase.count({
      where: { status: "PENDING" },
    }),
    // Get closed reports count
    prisma.reportCase.count({
      where: { status: "CLOSED" },
    }),
    // Get most common report reason
    prisma.reports.findMany({
      select: {
        reportReason: true,
      },
    }),
    // Get total images count
    prisma.image.count(),
    // Get recent images count
    prisma.image.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    }),
    // Get total tickets count
    prisma.ticket.count(),
    // Get open tickets count
    prisma.ticket.count({
      where: { status: "OPEN" },
    }),
    // Get in progress tickets count
    prisma.ticket.count({
      where: { status: "IN_PROGRESS" },
    }),
    // Get closed tickets count
    prisma.ticket.count({
      where: { status: "CLOSED" },
    }),
    // Get resolved tickets count
    prisma.ticket.count({
      where: { status: "RESOLVED" },
    }),
  ]);

  // Calculate most common reason
  const reasonCounts: Record<string, number> = {};
  reportReasons.forEach((report) => {
    reasonCounts[report.reportReason] =
      (reasonCounts[report.reportReason] || 0) + 1;
  });

  let mostCommonReason = "No reports";
  let maxCount = 0;

  Object.entries(reasonCounts).forEach(([reason, count]) => {
    if (count > maxCount) {
      maxCount = count;
      // Format the reason
      switch (reason) {
        case "inappropriate":
          mostCommonReason = "Inappropriate content";
          break;
        case "copyright":
          mostCommonReason = "Copyright violation";
          break;
        case "spam":
          mostCommonReason = "Spam or misleading";
          break;
        case "other":
          mostCommonReason = "Other";
          break;
        default:
          mostCommonReason = reason;
      }
    }
  });

  return {
    totalUsers,
    totalVideos,
    totalViews: totalViews._sum.views || 0,
    recentUsers,
    recentVideos,
    totalImages,
    recentImages,
    packageStats: packageStats.reduce(
      (acc, curr) => {
        acc[curr.package] = curr._count;
        return acc;
      },
      {} as Record<Package, number>,
    ),
    verifiedUsers: verificationStats.find((s) => s.verified)?._count || 0,
    reports: {
      total: totalReports,
      open: openReports,
      pending: pendingReports,
      closed: closedReports,
      mostCommonReason,
    },
    tickets: {
      total: totalTickets,
      open: openTickets,
      inProgress: inProgressTickets,
      closed: closedTickets,
      resolved: resolvedTickets,
    },
  };
}