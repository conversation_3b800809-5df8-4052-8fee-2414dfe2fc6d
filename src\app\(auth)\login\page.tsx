import { GoogleOAuthProvider } from "@react-oauth/google";
import LoginPageClient from "./client"
import { Suspense } from "react";
import { getMaintenance } from "@/server/maintenance";

export default async function LoginPage() {
  const clientId = process.env.DISCORD_CLIENT_ID || "";
  const redirectUri = process.env.DISCORD_REDIRECT_URI || "";
  const googleClientId = process.env.GOOGLE_CLIENT_ID || "";

  const maintenance = await getMaintenance();

  return (
    <GoogleOAuthProvider clientId={googleClientId}>
      <Suspense>
        <LoginPageClient maintenance={maintenance} clientId={clientId} redirectUri={redirectUri} />
      </Suspense>
    </GoogleOAuthProvider >
  )
}