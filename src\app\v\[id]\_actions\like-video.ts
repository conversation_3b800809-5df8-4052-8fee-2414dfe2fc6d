"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { hasDislikedVideo, likeVideo, unDislikeVideo } from "@/server/video";

type Callback = {
  success: boolean;
  message: string;
  data?: any
}

type Props = {
  videoId: string;
}

export default async function submitLikeVideo({ videoId }: Props): Promise<Callback> {
  try {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
      return {
        success: false,
        message: "You have exceeded the rate limit. Please try again later."
      }
    }

    const userSession = await getUserSession();
    const userId = userSession.userId;
    if (!userId || !userSession) {
      return {
        success: false,
        message: "User not authenticated."
      }
    }

    const isDisliked = await hasDislikedVideo(videoId, userId);
    if (isDisliked) {
      await unDislikeVideo(videoId, userId);
    }

    const likeData = await likeVideo(videoId, userId);
    if (!likeData) {
      return {
        success: false,
        message: "Failed to like the video."
      }
    }

    if (typeof likeData === "boolean" && likeData === true) {
      return {
        success: true,
        message: "You have removed your like.",
        data: {
          liked: false,
          dislike: false
        }
      }
    }

    return {
      success: true,
      message: "Liked video!",
      data: {
        liked: true,
        dislike: false
      }
    }
  } catch (error) {
    console.error("Error liking video:", error);

    return {
      success: false,
      message: "An error occurred while liking the video."
    }
  }
}
