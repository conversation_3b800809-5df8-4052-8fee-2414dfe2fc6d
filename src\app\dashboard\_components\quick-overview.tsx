"use client";

import React from "react";
import { Eye, Calendar, ThumbsUp, ThumbsDown } from "lucide-react";
import { motion } from "framer-motion";
import { format } from "date-fns";

interface QuickOverviewProps {
  video: {
    views: number;
    createdAt: string | Date;
  };
  estimatedLikes: number;
  estimatedDislikes: number;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function QuickOverview({
  video,
  estimatedLikes,
  estimatedDislikes,
}: QuickOverviewProps) {
  const formattedDate = format(new Date(video.createdAt), "dd-M-yyyy");

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="mb-6"
    >
      <div className="bg-[#110018] rounded-xl border border-white/12">
        <div className="flex flex-col p-6 pb-0">
          <h3 className="text-white font-bold text-2xl font-['Montserrat'] mb-6">
            Quick Overview
          </h3>
        </div>

        <div className="overflow-x-auto pb-6 px-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 w-full">
            {/* Total Views Card */}
            <motion.div variants={itemVariants} className="col-span-1">
              <div className="flex w-full p-4 flex-col justify-center items-center gap-2.5 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                <div className="flex w-full justify-between items-center px-2">
                  <div className="flex flex-col items-start gap-1">
                    <div className="text-[#B851E0] font-bold text-[24px] md:text-[28px] leading-[160%] font-['Montserrat']">
                      {video.views.toLocaleString()}
                    </div>
                    <div className="text-white font-normal text-base md:text-xl leading-[160%] font-['Montserrat']">
                      Total Views
                    </div>
                  </div>
                  <div className="flex w-[60px] md:w-[75px] h-[50px] md:h-[60px] p-3 md:p-4 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                    <Eye className="w-[24px] md:w-[30px] h-[24px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Upload Date Card */}
            <motion.div variants={itemVariants} className="col-span-1">
              <div className="flex w-full p-4 flex-col justify-center items-center gap-2.5 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                <div className="flex w-full justify-between items-center px-2">
                  <div className="flex flex-col items-start gap-1">
                    <div className="text-[#B851E0] font-bold text-[24px] md:text-[28px] leading-[160%] font-['Montserrat']">
                      {formattedDate}
                    </div>
                    <div className="text-white font-normal text-base md:text-xl leading-[160%] font-['Montserrat']">
                      Upload Date
                    </div>
                  </div>
                  <div className="flex w-[60px] md:w-[75px] h-[50px] md:h-[60px] p-3 md:p-4 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                    <Calendar className="w-[24px] md:w-[30px] h-[24px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Likes Card */}
            <motion.div variants={itemVariants} className="col-span-1">
              <div className="flex w-full p-4 flex-col justify-center items-center gap-2.5 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                <div className="flex w-full justify-between items-center px-2">
                  <div className="flex flex-col items-start gap-1">
                    <div className="text-[#B851E0] font-bold text-[24px] md:text-[28px] leading-[160%] font-['Montserrat']">
                      {estimatedLikes.toLocaleString()}
                    </div>
                    <div className="text-white font-normal text-base md:text-xl leading-[160%] font-['Montserrat']">
                      Likes
                    </div>
                  </div>
                  <div className="flex w-[60px] md:w-[75px] h-[50px] md:h-[60px] p-3 md:p-4 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                    <ThumbsUp className="w-[24px] md:w-[30px] h-[24px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Dislikes Card */}
            <motion.div variants={itemVariants} className="col-span-1">
              <div className="flex w-full p-4 flex-col justify-center items-center gap-2.5 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                <div className="flex w-full justify-between items-center px-2">
                  <div className="flex flex-col items-start gap-1">
                    <div className="text-[#B851E0] font-bold text-[24px] md:text-[28px] leading-[160%] font-['Montserrat']">
                      {estimatedDislikes.toLocaleString()}
                    </div>
                    <div className="text-white font-normal text-base md:text-xl leading-[160%] font-['Montserrat']">
                      Dislikes
                    </div>
                  </div>
                  <div className="flex w-[60px] md:w-[75px] h-[50px] md:h-[60px] p-3 md:p-4 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                    <ThumbsDown className="w-[24px] md:w-[30px] h-[24px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}