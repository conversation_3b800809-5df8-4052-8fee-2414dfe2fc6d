"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Trash2, Copy, TicketPercent } from "lucide-react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitCreateCoupon from "../_actions/create-coupon";
import { PromotionCode } from "@/server/coupons";
import submitDeleteCoupon from "../_actions/delete-coupon";

interface Coupon {
  id: string;
  code: string;
  discount: number;
  type: "percentage" | "fixed";
  validUntil: Date;
  maxUses: number;
  usedCount: number;
  active: boolean;
}

type TableProps = {
  initialCoupons: PromotionCode[];
};

export function CouponsTable({ initialCoupons }: TableProps) {
  const { success, error } = useEnhancedToast();
  const [coupons, setCoupons] = useState<PromotionCode[]>(initialCoupons);
  const [isCreating, setIsCreating] = useState(false);
  const [newCoupon, setNewCoupon] = useState({
    code: "",
    discount: "",
    type: "percentage",
    validUntil: "",
    maxUses: "",
  });

  const handleCreateCoupon = async () => {
    setIsCreating(true);
    try {
      const response = await submitCreateCoupon({
        type: newCoupon.type === "percentage" ? "percentage" : "fixed",
        discount: newCoupon.discount,
        validUntil: newCoupon.validUntil,
        maxUses: newCoupon.maxUses,
        code: newCoupon.code,
      });

      if (!response || !response.success) {
        error("Creation Failed", "An error occurred while creating the coupon");
        return;
      }

      success("Coupon Created", "Coupon created successfully");

      if (response.coupon) {
        setCoupons([...coupons, response.coupon]);

        setNewCoupon({
          code: "",
          discount: "",
          type: "percentage",
          validUntil: "",
          maxUses: "",
        });
      }
    } catch (err) {
      error("Creation Failed", "An error occurred while creating the coupon");
    } finally {
      setIsCreating(false);
    }
  };

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    success("Code Copied", "Coupon code copied to clipboard");
  };

  const handleDeleteCoupon = async (id: string) => {
    try {
      const response = await submitDeleteCoupon({ couponId: id });
      if (!response || !response.success) {
        error("Delete Failed", response.message);
        return;
      }

      success("Coupon Disabled", "Coupon disabled successfully");

      if (response.coupon) {
        setCoupons((prev) =>
          prev.map((coupon) =>
            coupon.id === response.coupon!.id
              ? { ...coupon, active: false }
              : coupon,
          ),
        );
      }
    } catch (err) {
      error("Delete Error", "An error occurred while deleting the coupon");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Create Button */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold text-white">
              Discount Coupons
            </h2>
            <p className="text-sm text-gray-400 mt-1">
              {coupons.length} {coupons.length === 1 ? "coupon" : "coupons"}{" "}
              total
            </p>
          </div>
          <Dialog open={isCreating} onOpenChange={setIsCreating}>
            <DialogTrigger asChild>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white border-0">
                <Plus className="h-4 w-4 mr-2" />
                Create Coupon
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-gray-950 border-gray-800/40">
              <DialogHeader>
                <DialogTitle className="text-white">
                  Create New Coupon
                </DialogTitle>
                <DialogDescription className="text-gray-400">
                  Create a new discount coupon for your customers.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="code" className="text-gray-300">
                    Coupon Code
                  </Label>
                  <Input
                    id="code"
                    value={newCoupon.code}
                    onChange={(e) =>
                      setNewCoupon({ ...newCoupon, code: e.target.value })
                    }
                    placeholder="SUMMER2024"
                    className="bg-gray-900/30 border-gray-800/40 text-white placeholder:text-gray-500 focus:border-purple-400"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="discount" className="text-gray-300">
                    Discount Amount
                  </Label>
                  <Input
                    id="discount"
                    type="number"
                    value={newCoupon.discount}
                    onChange={(e) =>
                      setNewCoupon({ ...newCoupon, discount: e.target.value })
                    }
                    placeholder="20"
                    className="bg-gray-900/30 border-gray-800/40 text-white placeholder:text-gray-500 focus:border-purple-400"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="type" className="text-gray-300">
                    Discount Type
                  </Label>
                  <select
                    id="type"
                    className="flex h-10 w-full rounded-md border border-gray-800/40 bg-gray-900/30 px-3 py-2 text-sm text-white focus:border-purple-400 focus:outline-none"
                    value={newCoupon.type}
                    onChange={(e) =>
                      setNewCoupon({
                        ...newCoupon,
                        type: e.target.value as "percentage" | "fixed",
                      })
                    }
                  >
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="validUntil" className="text-gray-300">
                    Valid Until
                  </Label>
                  <Input
                    id="validUntil"
                    type="date"
                    value={newCoupon.validUntil}
                    onChange={(e) =>
                      setNewCoupon({ ...newCoupon, validUntil: e.target.value })
                    }
                    className="bg-gray-900/30 border-gray-800/40 text-white focus:border-purple-400 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert [&::-webkit-calendar-picker-indicator]:brightness-0 [&::-webkit-calendar-picker-indicator]:invert"
                    min={new Date().toISOString().split("T")[0]}
                    style={{
                      colorScheme: "dark",
                    }}
                  />
                  <p className="text-xs text-gray-500">
                    Select the expiration date for this coupon
                  </p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="maxUses" className="text-gray-300">
                    Maximum Uses
                  </Label>
                  <Input
                    id="maxUses"
                    type="number"
                    value={newCoupon.maxUses}
                    onChange={(e) =>
                      setNewCoupon({ ...newCoupon, maxUses: e.target.value })
                    }
                    placeholder="100"
                    className="bg-gray-900/30 border-gray-800/40 text-white placeholder:text-gray-500 focus:border-purple-400"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreating(false)}
                  className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateCoupon}
                  className="bg-purple-600 hover:bg-purple-700 text-white border-0"
                >
                  Create Coupon
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Coupons Table */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-800/40 hover:bg-gray-900/20">
              <TableHead className="text-gray-300 font-semibold">
                Code
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Discount
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Valid Until
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Uses
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Status
              </TableHead>
              <TableHead className="text-gray-300 font-semibold">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coupons.length === 0 ? (
              <TableRow className="border-gray-800/40">
                <TableCell
                  colSpan={6}
                  className="text-center text-gray-400 py-12"
                >
                  <div className="flex flex-col items-center gap-3">
                    <TicketPercent className="h-12 w-12 text-gray-600" />
                    <div>
                      <p className="font-medium">No coupons found</p>
                      <p className="text-sm">
                        Create your first coupon to get started!
                      </p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              coupons.map((coupon) => (
                <TableRow
                  key={coupon.id}
                  className="border-gray-800/40 hover:bg-gray-900/20"
                >
                  <TableCell className="font-medium text-white">
                    {coupon.code}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {coupon.coupon.amount_off === null &&
                    coupon.coupon.percent_off !== null
                      ? `${coupon.coupon.percent_off}%`
                      : `$${coupon.coupon.amount_off != null ? coupon.coupon.amount_off : 0 / 100}`}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {coupon.coupon.redeem_by
                      ? new Date(
                          coupon.coupon.redeem_by * 1000,
                        ).toLocaleDateString()
                      : "N/A"}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {coupon.times_redeemed} / {coupon.max_redemptions}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border-0 ${
                        coupon.active
                          ? "bg-green-500/20 text-green-400"
                          : "bg-red-500/20 text-red-400"
                      }`}
                    >
                      {coupon.active ? "Active" : "Inactive"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleCopyCode(coupon.code)}
                        className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800/40"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteCoupon(coupon.id)}
                        className="h-8 w-8 text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}