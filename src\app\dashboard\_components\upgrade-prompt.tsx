"use client";

import React from "react";
import { Lock, Zap, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Link from "next/link";

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

interface UpgradePromptProps {
  type?: "main" | "cta";
}

export function UpgradePrompt({ type = "main" }: UpgradePromptProps) {
  if (type === "cta") {
    return (
      <motion.div variants={itemVariants} className="mb-6">
        <div className="bg-[#110018] rounded-2xl border border-white/12 p-6 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-[#1A0B21] border border-white/16 flex items-center justify-center">
              <TrendingUp className="w-8 h-8 text-[#FFDE09]" />
            </div>
            <div>
              <h3 className="text-white font-bold text-xl md:text-2xl leading-[160%] font-['Montserrat'] mb-2">
                Get More Detailed Analytics
              </h3>
              <p className="text-white/70 font-normal text-sm md:text-base leading-[140%] font-['Montserrat'] mb-4">
                Unlock traffic sources, viewer retention curves, geographic
                insights, device breakdowns, and more with Pro or Creator plans.
              </p>
              <Link href="/dashboard/billing">
                <Button className="bg-gradient-to-r from-[#B851E0] to-[#EB489B] hover:from-[#A041D0] hover:to-[#DC3A8B] text-white font-bold py-3 px-6 rounded-full transition-all duration-300">
                  Upgrade to Pro
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div variants={itemVariants} className="flex-1">
      <div className="w-full max-w-[846px] h-[300px] md:h-[468px] rounded-xl border border-white/12 bg-[#110018] relative">
        <div className="flex flex-col items-center justify-center gap-3 md:gap-4 absolute inset-4 md:inset-6">
          {/* Lock Icon */}
          <div className="w-[50px] md:w-[67px] h-[50px] md:h-[67px] rounded-full bg-[#1A0B21] border border-white/16 flex items-center justify-center drop-shadow-[0px_5.36px_5.36px_rgba(0,0,0,0.25)]">
            <Lock className="w-6 md:w-8 h-6 md:h-8 text-[#FFDE09]" />
          </div>

          <div className="flex flex-col items-center gap-4 md:gap-7 max-w-[280px] md:max-w-[365px]">
            <div className="flex flex-col items-center gap-2 md:gap-3 text-center">
              <div className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat']">
                Unlock Advanced Analytics
              </div>
              <div className="text-white font-normal text-sm md:text-base leading-[140%] font-['Montserrat'] opacity-70">
                Upgrade to Pro Or Creator to Access Detailed Insights,
                Engagement metrics, and performance analytics.
              </div>
            </div>
            <Link href="/dashboard/billing">
              <Button className="flex py-2.5 md:py-3.5 px-4 md:px-6 justify-center items-center gap-2 md:gap-2.5 rounded-[65px] bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A041D0] hover:to-[#DC3A8B] transition-all duration-300">
                <Zap className="w-5 md:w-[29px] h-5 md:h-[29px] text-white" />
                <span className="text-white text-center font-bold text-sm md:text-lg leading-[160%] font-['Montserrat']">
                  Upgrade Required
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  );
}