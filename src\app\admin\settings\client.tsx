"use client";

import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import MaintenanceEditDialog from "./_components/edit-dialog";
import disableMaintenance from "./_actions/disable-maintenance";
import { useRouter } from "next/navigation";

export default function SettingsPageClient({
  maintenance,
}: {
  maintenance: boolean;
}) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isMaintenanceMode, setIsMaintenanceMode] =
    useState<boolean>(maintenance);
  const [editMaintenance, setEditMaintenance] = useState<boolean>(false);

  const toggleMaintenanceMode = async () => {
    setIsMaintenanceMode(!isMaintenanceMode);
    if (!isMaintenanceMode) {
      setEditMaintenance(true);
    }

    if (isMaintenanceMode) {
      const response = await disableMaintenance();
      if (response.success) {
        success("Maintenance Disabled", response.message);
        router.refresh();
      } else {
        error("Maintenance Error", response.message);
      }
    }
  };

  const handleClose = () => {
    setEditMaintenance(false);
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      {/* Settings Section Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <h2 className="text-lg font-semibold text-white">Settings</h2>
        <p className="text-sm text-gray-400 mt-1">
          Manage system-wide settings
        </p>
      </div>

      {/* Maintenance Mode Card */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-white">
              Maintenance Mode
            </h3>
            <p className="text-sm text-gray-400 mt-1">
              Enable maintenance mode to prevent non-admin users from accessing
              the site
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <Switch
              checked={isMaintenanceMode}
              onCheckedChange={toggleMaintenanceMode}
              className="data-[state=checked]:bg-purple-600"
            />
            <Label className="text-white text-sm font-medium">
              Enable maintenance mode
            </Label>
          </div>
        </div>
        <MaintenanceEditDialog isOpen={editMaintenance} onClose={handleClose} />
      </div>
    </div>
  );
}