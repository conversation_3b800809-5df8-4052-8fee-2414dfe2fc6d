import React from "react";
import { getUserSession } from "@/server/session";
import { prisma } from "@/lib/prisma";
import { hasPermission } from "@/server/admin";
import { SocketProvider } from "@/components/socket-context";
import MaintenancePage from "../maintenance/page";
import { getUserProfilePicture } from "src/server/profile";

export const dynamic = "force-dynamic";

interface UserForSidebar {
  id: string;
  name: string;
  role: {
    id: number;
    permissions: {
      permission: {
        name: string;
      };
    }[];
  };
  image?: string | null;
}

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getUserSession();

  let userForSidebar: UserForSidebar | null = null;
  let hasAccessToAdmin = false;

  if (session.userId) {
    const dbUser = await prisma.user.findUnique({
      where: { id: session.userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (dbUser) {
      const imageBinary = await getUserProfilePicture(dbUser.id);
      if (imageBinary == null) return;
      userForSidebar = {
        id: dbUser.id,
        name: dbUser.name || "",
        role: dbUser.role as UserForSidebar["role"],
        image: "",
      };
      hasAccessToAdmin = await hasPermission(userForSidebar.role.id, [
        "ADMIN_PAGE",
      ]);
    }
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_APP_URL}/api/maintenance?path=/dashboard`,
    {
      cache: "no-store",
    },
  );

  const data = await res.json();
  if (data.enabled) {
    return <MaintenancePage />;
  }

  return (
    <div className="min-h-screen bg-black">
      <SocketProvider userId={session.userId} userToken={session.accessToken}>{children}</SocketProvider>
    </div>
  );
}