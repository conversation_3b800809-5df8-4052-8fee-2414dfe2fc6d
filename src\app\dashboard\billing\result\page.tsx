import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import { createNotification } from "@/server/notifications";
import { getUser } from "@/server/session";
import {$Enums, LogActions} from "@prisma/client";
import { redirect } from "next/navigation";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

async function getSession(sessionId: string) {
  const session = await stripe.checkout.sessions.retrieve(sessionId!, {
    expand: ["line_items.data.price.product"]
  });
  return session;
}

async function updateBillingUser(userId: string, plan: $Enums.Package, customerId: string): Promise<boolean> {
  const user = await getUserById(userId);
  if (!user) {
    return false;
  }

  await prisma.user.update({
    where: {
      id: user.id
    },
    data: {
      package: plan,
      stripeAccountId: customerId
    }
  });

  await createNotification(user.id, "Your subscription has been updated", "INFO");
  await createLog(user.id, LogConstants.BOUGHT_SUBSCRIPTION, LogActions.ACCOUNT, "Subscription: " + plan);

  return true;
}

export default async function BillingPage(props) {
  const searchParams = await props.searchParams;
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }
  const sessionId = searchParams.session_id;
  if (sessionId == null) {
    redirect("/dashboard/billing")
  }

  const session = await getSession(sessionId);

  if (session.status === "open") {
    return <p>Payment did not work.</p>;
  }

  if (session.payment_status === "paid" && session.status === "complete") {
    let subscriptionPackage: $Enums.Package = "FREE";
    switch (session.line_items?.data[0].description) {
      case "Streambliss Creator":
        subscriptionPackage = "CREATOR";
        break;
      case "Streambliss Pro":
        subscriptionPackage = "PRO";
        break;
      default:
        break;
    }

    const complete = await updateBillingUser(user.id, subscriptionPackage, session.customer as string);
    if (complete) {
      redirect("/dashboard/profile")
    }
  }

  return (
    <p>hi</p>
  )
}