/*
  Warnings:

  - You are about to drop the column `reportedMessageId` on the `CommentReports` table. All the data in the column will be lost.
  - Added the required column `commentText` to the `CommentReports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reportedUserEmail` to the `CommentReports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reportedUserName` to the `CommentReports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reportedVideoTitle` to the `CommentReports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reportedVideoUrl` to the `CommentReports` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `CommentReports` DROP COLUMN `reportedMessageId`,
    ADD COLUMN `commentText` VARCHAR(191) NOT NULL,
    ADD COLUMN `reportedUserEmail` VARCHAR(191) NOT NULL,
    ADD COLUMN `reportedUserName` VARCHAR(191) NOT NULL,
    ADD COLUMN `reportedVideoTitle` VARCHAR(191) NOT NULL,
    ADD COLUMN `reportedVideoUrl` VARCHAR(191) NOT NULL;
