"use client";

import { useState, useRef, useEffect } from "react";
import type { Video } from "@/types/video";
import { Heart, Play } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

const API_URL =
  process.env.NEXT_PUBLIC_VIDEO_API_URL ||
  process.env.VIDEO_API_URL ||
  "https://api.streambliss.cloud";

interface CommunityVideoCardProps {
  video: Video;
  size?: "normal" | "large" | "wide" | "tall";
}

export function CommunityVideoCard({
  video,
  size = "normal",
}: CommunityVideoCardProps) {
  const [hovered, setHovered] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  const [imgError, setImgError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const router = useRouter();

  const thumbnail = `${API_URL}/video/thumbnail/${video.userId}/${video.id}`;
  const videoSrc = `${API_URL}/video/stream/${video.userId}/${video.id}`;
  const uploader = video.user?.name || "Unknown";
  const uploaderImage = video.user?.image;
  const displayTitle =
    video.title && video.title.length > 0 ? video.title : "Clip by " + uploader;

  useEffect(() => {
    if (hovered) {
      const timer = setTimeout(() => {
        setVideoVisible(true);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setVideoVisible(false);
    }
  }, [hovered]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (videoVisible) {
      video.muted = true;
      video.currentTime = 0;
      video.play().catch(() => {
        // Ignore play errors
      });
    } else {
      if (!video.paused) {
        video.pause();
      }
    }
  }, [videoVisible]);

  const handleCardClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest(".user-info-clickable")) {
      return;
    }
    router.push(`/v/${video.shortLink}`);
  };

  const formatDuration = (duration: number) => {
    if (!duration) return "0:00";
    const totalSeconds = Math.floor(duration);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const formatLikes = (likes: number = 0) => {
    if (likes >= 1000000) {
      return `${(likes / 1000000).toFixed(1)}M`;
    } else if (likes >= 1000) {
      return `${(likes / 1000).toFixed(1)}K`;
    }
    return likes.toString();
  };

  return (
    <div
      className="relative w-full h-full cursor-pointer overflow-hidden bg-black rounded-2xl group"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={handleCardClick}
    >
      {/* Video/Thumbnail */}
      <div className="relative w-full h-full">
        {imgError ? (
          <div className="absolute inset-0 w-full h-full bg-zinc-800 flex items-center justify-center">
            <span className="text-zinc-400 text-sm">Thumbnail unavailable</span>
          </div>
        ) : (
          <Image
            src={thumbnail}
            alt={displayTitle}
            fill
            className="object-cover"
            onError={() => setImgError(true)}
            unoptimized
          />
        )}

        {videoVisible && (
          <video
            ref={videoRef}
            src={videoSrc}
            className="absolute inset-0 w-full h-full object-cover z-[1]"
            muted
            loop
            playsInline
            preload="metadata"
          />
        )}

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent z-[2]" />
      </div>

      {/* Title */}
      <div className="absolute top-5 left-5 z-[3]">
        <h3
          className="text-white font-bold text-base font-montserrat leading-tight drop-shadow-lg max-w-[90%]"
          style={{ textShadow: "0px 1px 2px rgba(0, 0, 0, 0.9)" }}
        >
          {displayTitle.length > 40
            ? displayTitle.substring(0, 37) + "..."
            : displayTitle}
        </h3>
      </div>

      {/* Bottom info */}
      <div className="absolute bottom-5 left-5 right-5 z-[3] flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* User info */}
          <Link
            href={`/community/u/${uploader}`}
            className="user-info-clickable flex items-center gap-2 hover:opacity-80 transition-opacity"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative w-[30px] h-[30px]">
              {uploaderImage && !avatarError ? (
                <Image
                  src={uploaderImage}
                  alt={uploader}
                  fill
                  className="rounded-full object-cover"
                  onError={() => setAvatarError(true)}
                  unoptimized
                />
              ) : (
                <div className="w-[30px] h-[30px] rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <span className="text-white text-xs font-semibold">
                    {uploader.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <span className="text-white text-base font-semibold font-montserrat">
              {uploader}
            </span>
          </Link>

          {/* Likes */}
          <div className="flex items-center gap-[6px]">
            <Heart className="w-[27px] h-[27px] text-white fill-current" />
            <span className="text-white text-base font-semibold font-montserrat">
              {formatLikes(video.views || 0)}
            </span>
          </div>
        </div>

        {/* Duration */}
        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded px-[6px] py-1">
          <span className="text-white text-base font-semibold font-montserrat">
            {formatDuration(video.duration || 0)}
          </span>
        </div>
      </div>

      {/* Play button overlay */}
      <div className="absolute inset-0 flex items-center justify-center z-[2] opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className="w-16 h-16 rounded-full bg-gradient-to-b from-[#b851e0] to-[#eb489b] flex items-center justify-center shadow-lg transform scale-0 group-hover:scale-100 transition-transform duration-300">
          <Play className="w-7 h-7 text-white ml-1" fill="currentColor" />
        </div>
      </div>
    </div>
  );
}