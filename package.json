{"name": "streambliss-beta", "version": "1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "eslint": "next eslint", "clean": "rimraf .next out"}, "dependencies": {"@emailjs/browser": "^3.12.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^3.10.0", "@mui/material": "^6.1.9", "@mui/x-charts": "^7.22.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-oauth/google": "^0.12.1", "@react-three/drei": "^9.121.5", "@react-three/fiber": "^8.17.14", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/nodemailer": "^6.4.17", "@types/sharp": "^0.32.0", "aos": "^2.3.4", "bcrypt": "^5.1.1", "chart.js": "^3.9.1", "chartjs-adapter-date-fns": "^2.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "discord-oauth2": "^2.12.1", "diskusage": "^1.2.0", "dotenv": "^16.4.5", "dotenv-cli": "^8.0.0", "emoji-picker-react": "^4.12.2", "express": "^4.19.2", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "fluent-ffmpeg": "^2.1.3", "formidable": "^3.5.4", "framer-motion": "^11.15.0", "gsap": "^3.13.0", "ignore-loader": "^0.1.2", "input-otp": "^1.4.2", "iron-session": "^8.0.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.441.0", "masonic": "^4.1.0", "multer": "^2.0.1", "nanoid": "^5.0.7", "next": "15.3.1", "next-auth": "^4.24.11", "next-connect": "^1.0.0", "nodemailer": "^6.10.0", "npm": "^11.1.0", "otplib": "^12.0.1", "qrcode": "^1.5.4", "rate-limiter-flexible": "^5.0.5", "react": "^19.1.0", "react-apexcharts": "^1.4.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.3.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.28.0", "recharts": "^2.12.7", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "stripe": "^17.6.0", "swiper": "^11.2.8", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tailwindcss-animatecss": "^3.0.5", "tailwindcss-aspect-ratio": "^3.0.0", "undici": "^7.11.0", "uuid": "^9.0.1", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.5", "@types/node": "^22.13.5", "@types/ws": "^8.18.1", "assert": "^2.1.0", "autoprefixer": "10.0.1", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.0", "eslint": "8", "eslint-config-next": "15.3.1", "https-browserify": "^1.0.0", "node-polyfill-webpack-plugin": "^4.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "8", "prisma": "^6.11.1", "process": "^0.11.10", "rimraf": "^5.0.5", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^4", "typescript": "^5.4.5", "url": "^0.11.4", "webpack": "^5.94.0"}, "overrides": {"react": "^19.1.0", "react-dom": "^19.1.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0"}}