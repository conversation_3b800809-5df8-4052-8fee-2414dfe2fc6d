"use client";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Icons } from "@/components/icons";
import { TokenResponse, useGoogleLogin } from "@react-oauth/google";
import { useEffect, useState } from "react";
import CustomButton from "@/components/common/CustomButton";

export function RegisterForm({
  clientId,
  redirectUri,
  banned,
}: {
  clientId: string;
  redirectUri: string;
  banned: boolean;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { error } = useEnhancedToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isBanned, setIsBanned] = useState(banned);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (searchParams?.get("banned") === "true") {
      setIsBanned(true);
    }
  }, [searchParams]);

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData(event.currentTarget);
      const name = formData.get('name') as string;
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      const request = await fetch('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          name,
          email,
          password,
        }),
      });

      const data = await request.json();
      if (!data && request.status === 401) {
        const message = await request.text();
        error("Registration Failed", message);
      }

      router.push('/login');
    } catch (err) {
      error("Registration Error", "Something went wrong");
      console.error("Error during registration: ", err);
    } finally {
      setIsLoading(false);
    }
  }

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (response: any) => {
      const request = await fetch("/api/auth/google", {
        method: "POST",
        body: JSON.stringify({
          access_token: response.access_token,
        })
      });

      const data = await request.json();
      if (data.status === 200 && data.redirect) {
        router.push('/dashboard');
      }
    },
    onError: (_error) => {
      console.error('error trying to sign in with google: ', _error);
    },
  });

  const handleDiscordLogin = () => {
    const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(
      redirectUri,
    )}&response_type=code&scope=identify%20email&state=registration`;
    window.location.href = discordAuthUrl;
  };

  return (
    <div className="space-y-6">
      {/* Social Login Buttons */}
      <div className="space-y-3">
        <button
          type="button"
          onClick={() => handleGoogleLogin()}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-3 px-6 py-3.5 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 rounded-xl text-white font-medium transition-all duration-300 backdrop-blur-sm"
        >
          <Icons.google className="h-5 w-5" />
          Continue with Google
        </button>

        <button
          type="button"
          onClick={handleDiscordLogin}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-3 px-6 py-3.5 bg-[#5865F2]/20 hover:bg-[#5865F2]/30 border border-[#5865F2]/30 hover:border-[#5865F2]/50 rounded-xl text-white font-medium transition-all duration-300 backdrop-blur-sm"
        >
          <Icons.discord className="h-5 w-5" />
          Continue with Discord
        </button>
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-white/20" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-transparent px-4 text-white/60 font-medium tracking-wider">
            or create with email
          </span>
        </div>
      </div>

      {/* Registration Form */}
      <form onSubmit={onSubmit} className="space-y-5">
        <div className="space-y-2">
          <Label htmlFor="name" className="text-white/90 font-medium">
            Username
          </Label>
          <Input
            id="name"
            name="name"
            type="text"
            autoComplete="name"
            required
            disabled={isLoading}
            placeholder="Choose a username"
            className="h-12 bg-white/5 border-white/20 focus:border-custom-purple focus:ring-1 focus:ring-custom-purple text-white placeholder:text-white/50 rounded-xl backdrop-blur-sm transition-all duration-300"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-white/90 font-medium">
            Email Address
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            disabled={isLoading}
            placeholder="Enter your email"
            className="h-12 bg-white/5 border-white/20 focus:border-custom-purple focus:ring-1 focus:ring-custom-purple text-white placeholder:text-white/50 rounded-xl backdrop-blur-sm transition-all duration-300"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-white/90 font-medium">
            Password
          </Label>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="new-password"
              required
              disabled={isLoading}
              placeholder="Create a strong password"
              className="h-12 pr-12 bg-white/5 border-white/20 focus:border-custom-purple focus:ring-1 focus:ring-custom-purple text-white placeholder:text-white/50 rounded-xl backdrop-blur-sm transition-all duration-300"
            />
            <span
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 -translate-y-1/2 px-2 py-1 text-xs text-white bg-gradient-to-r from-custom-purple/80 to-custom-pink/80 hover:from-custom-purple hover:to-custom-pink transition-all cursor-pointer select-none font-medium rounded-md shadow-sm backdrop-blur-sm"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  setShowPassword(!showPassword);
                }
              }}
            >
              {showPassword ? "Hide" : "Show"}
            </span>
          </div>
          <p className="text-xs text-white/50 mt-1">
            Must be at least 8 characters long
          </p>
        </div>

        {/* Terms and conditions */}
        <div className="text-xs text-white/60 leading-relaxed">
          By creating an account, you agree to our{" "}
          <Link
            href="/terms"
            className="text-custom-purple hover:text-custom-pink transition-colors"
          >
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link
            href="/privacy"
            className="text-custom-purple hover:text-custom-pink transition-colors"
          >
            Privacy Policy
          </Link>
        </div>

        {/* Create Account Button */}
        <CustomButton
          variant="gradiant"
          className="w-full !py-[13px] !text-base !font-semibold !mt-6 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
          disabled={isLoading}
        >
          {isLoading ? "Creating Account..." : "Create Account"}
        </CustomButton>
      </form>

      {/* Ban Message */}
      {isBanned && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
          <p className="text-red-400 text-sm text-center font-medium">
            Your account has been banned. Please contact support for more
            information.
          </p>
        </div>
      )}
    </div>
  );
}