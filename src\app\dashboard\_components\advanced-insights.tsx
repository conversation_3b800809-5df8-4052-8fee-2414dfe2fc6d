"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Clock,
  BarChart3,
  ThumbsUp,
  ThumbsDown,
  Heart,
  MessageCircle,
  Loader2,
} from "lucide-react";
import { motion } from "framer-motion";
import { getCommentsByVideoId } from "@/server/comment";

interface AdvancedInsightsProps {
  video: {
    id: string;
    views: number;
  };
  isProOrCreator: boolean;
  calculatedMetrics: {
    totalWatchTimeMinutes: number;
    totalWatchTimeSeconds: number;
    avgWatchTimeMinutes: number;
    avgWatchTimeSecondsOnly: number;
    estimatedLikes: number;
    estimatedDislikes: number;
  };
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function AdvancedInsights({
  video,
  isProOrCreator,
  calculatedMetrics,
}: AdvancedInsightsProps) {
  const [commentsCount, setCommentsCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  const {
    totalWatchTimeMinutes,
    totalWatchTimeSeconds,
    avgWatchTimeMinutes,
    avgWatchTimeSecondsOnly,
    estimatedLikes,
    estimatedDislikes,
  } = calculatedMetrics;

  const fetchComments = useCallback(async () => {
    if (isProOrCreator && video?.id) {
      setIsLoading(true);
      try {
        const comments = await getCommentsByVideoId(video.id);
        setCommentsCount(comments.length);
      } catch (error) {
        console.error("Failed to fetch comments:", error);
        setCommentsCount(0);
      } finally {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
    }
  }, [isProOrCreator, video?.id]);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  return (
    <motion.div variants={containerVariants} className="mb-4">
      <div className="bg-[#110018] rounded-xl border border-white/12 p-4">
        <h3 className="text-white font-bold text-lg font-['Montserrat'] mb-4">
          Advanced Insights
        </h3>

        <div className="flex flex-col gap-4 md:gap-8">
          <div className="overflow-x-auto pb-4 scrollbar-hide">
            <div className="flex items-center gap-4 md:gap-8 min-w-max">

              {/* Total Watch Time Card */}
              <motion.div variants={itemVariants} className="flex-shrink-0">
                <div className="flex w-[320px] md:w-[380px] p-5 md:p-6 flex-col justify-center items-center gap-3 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                  <div className="flex w-[220px] md:w-[325px] justify-between items-center">
                    <div className="flex flex-col items-start gap-0.5">
                      <div className="text-[#B851E0] font-bold text-[20px] md:text-[28px] leading-[160%] font-['Montserrat'] flex items-center gap-1">
                        <span className="text-[#B851E0] transition-all duration-300">
                          {isProOrCreator ? totalWatchTimeMinutes : "0"}
                        </span>
                        <span className="text-white/70 text-base md:text-xl font-bold">
                          Min
                        </span>
                        <span className="text-[#B851E0] transition-all duration-300">
                          {isProOrCreator ? totalWatchTimeSeconds : "0"}
                        </span>
                        <span className="text-white/70 text-base md:text-xl font-bold">
                          Sec
                        </span>
                      </div>
                      <div className="text-white font-normal text-sm md:text-xl leading-[160%] font-['Montserrat']">
                        Total Watch Time
                      </div>
                    </div>
                    <div className="flex w-[50px] md:w-[75px] h-[35px] md:h-[50px] p-2 md:p-3 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                      <Clock className="w-[20px] md:w-[30px] h-[20px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Average Watch Time Card */}
              <motion.div variants={itemVariants} className="flex-shrink-0">
                <div className="flex w-[320px] md:w-[380px] p-5 md:p-6 flex-col justify-center items-center gap-3 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                  <div className="flex w-[220px] md:w-[325px] justify-between items-center">
                    <div className="flex flex-col items-start gap-0.5">
                      <div className="text-[#B851E0] font-bold text-[20px] md:text-[28px] leading-[160%] font-['Montserrat'] flex items-center gap-1">
                        <span className="text-[#B851E0] transition-all duration-300">
                          {isProOrCreator ? avgWatchTimeMinutes : "0"}
                        </span>
                        <span className="text-white/70 text-base md:text-xl font-bold">
                          Min
                        </span>
                        <span className="text-[#B851E0] transition-all duration-300">
                          {isProOrCreator ? avgWatchTimeSecondsOnly : "0"}
                        </span>
                        <span className="text-white/70 text-base md:text-xl font-bold">
                          Sec
                        </span>
                      </div>
                      <div className="text-white font-normal text-sm md:text-xl leading-[160%] font-['Montserrat']">
                        Avg. Watch Time
                      </div>
                    </div>
                    <div className="flex w-[50px] md:w-[75px] h-[35px] md:h-[50px] p-2 md:p-3 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                      <BarChart3 className="w-[20px] md:w-[30px] h-[20px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Reactions Card */}
              <motion.div variants={itemVariants} className="flex-shrink-0">
                <div className="flex w-[320px] md:w-[380px] p-5 md:p-6 flex-col justify-center items-center gap-3 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                  <div className="flex w-[220px] md:w-[325px] justify-between items-center">
                    <div className="flex flex-col items-start gap-0.5">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 hover:scale-110 transition-transform duration-200">
                          <ThumbsUp className="w-5 h-5 text-green-500" />
                          <span className="text-[#B851E0] font-bold text-[20px] md:text-[28px] leading-[160%] font-['Montserrat'] transition-all duration-300">
                            {isProOrCreator ? estimatedLikes : "0"}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 hover:scale-110 transition-transform duration-200">
                          <ThumbsDown className="w-5 h-5 text-red-500" />
                          <span className="text-[#B851E0] font-bold text-[20px] md:text-[28px] leading-[160%] font-['Montserrat'] transition-all duration-300">
                            {isProOrCreator ? estimatedDislikes : "0"}
                          </span>
                        </div>
                      </div>
                      <div className="text-white font-normal text-sm md:text-xl leading-[160%] font-['Montserrat']">
                        Reactions
                      </div>
                    </div>
                    <div className="flex w-[50px] md:w-[75px] h-[35px] md:h-[50px] p-2 md:p-3 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                      <Heart className="w-[20px] md:w-[30px] h-[20px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Comments Card */}
              <motion.div variants={itemVariants} className="flex-shrink-0">
                <div className="flex w-[320px] md:w-[380px] p-5 md:p-6 flex-col justify-center items-center gap-3 rounded-lg border border-white/8 bg-white/4 hover:bg-white/6 hover:border-white/12 transition-all duration-300 cursor-pointer group">
                  <div className="flex w-[220px] md:w-[325px] justify-between items-center">
                    <div className="flex flex-col items-start gap-0.5">
                      <div className="text-[#B851E0] font-bold text-[20px] md:text-[28px] leading-[160%] font-['Montserrat'] transition-all duration-300">
                        {isProOrCreator ? (
                          isLoading ? (
                            <div className="flex items-center gap-1">
                              <Loader2 className="w-5 h-5 animate-spin" />
                              <span className="animate-pulse">Loading...</span>
                            </div>
                          ) : (
                            commentsCount.toLocaleString()
                          )
                        ) : (
                          "00"
                        )}
                      </div>
                      <div className="text-white font-normal text-sm md:text-xl leading-[160%] font-['Montserrat']">
                        Comments
                      </div>
                    </div>
                    <div className="flex w-[50px] md:w-[75px] h-[35px] md:h-[50px] p-2 md:p-3 justify-center items-center rounded-[89px] border border-[rgba(184,81,224,0.50)] bg-[rgba(184,81,224,0.12)] group-hover:bg-[rgba(184,81,224,0.20)] group-hover:border-[rgba(184,81,224,0.70)] transition-all duration-300">
                      <MessageCircle className="w-[20px] md:w-[30px] h-[20px] md:h-[30px] text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}