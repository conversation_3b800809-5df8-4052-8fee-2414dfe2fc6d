"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  Check,
  Clock,
  Shield,
  User,
  Eye,
  ExternalLink,
  Flag,
  Image as ImageIcon,
  Video,
  FileText,
  X,
} from "lucide-react";
import { updateReportStatus } from "../_actions/update-status";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";
import { ReportDialog } from "../_components/report-dialog";
import { useState } from "react";

type Report = {
  id: string;
  reportedUser: string;
  reportedUserId: string;
  reportReason: string;
  createdAt: string;
  status: string;
  moderator: string;
  shortLink: string;
  videoId: string;
  details: string | null;
  contentType: "video" | "image";
};

type ReportsTableProps = {
  reports: Report[];
};

export default function ReportsTable({ reports }: ReportsTableProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [processingReportId, setProcessingReportId] = useState<string | null>(
    null,
  );

  const reportReason = (id: string) => {
    switch (id) {
      case "inappropriate":
        return "Inappropriate content";
      case "spam":
        return "Spam or misleading";
      case "other":
        return "Other";
      case "copyright":
        return "Copyright violation";
      default:
        return "Unknown";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "OPEN":
        return (
          <div className="flex items-center gap-2 bg-red-500/20 text-red-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-red-500/30">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
            Open
          </div>
        );
      case "PENDING":
        return (
          <div className="flex items-center gap-2 bg-yellow-500/20 text-yellow-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-yellow-500/30">
            <Clock className="w-3 h-3" />
            Pending
          </div>
        );
      case "CLOSED":
        return (
          <div className="flex items-center gap-2 bg-green-500/20 text-green-400 px-3 py-1.5 rounded-full text-sm font-medium w-fit border border-green-500/30">
            <Check className="w-3 h-3" />
            Closed
          </div>
        );
      default:
        return (
          <div className="px-3 py-1.5 rounded-full bg-gray-500/20 text-gray-400 text-sm font-medium w-fit border border-gray-500/30">
            {status}
          </div>
        );
    }
  };

  const updateStatus = async (status: string, reportId: string) => {
    setProcessingReportId(reportId);

    try {
      const response = await updateReportStatus({ reportId, status });
      if (response.success) {
        success("Status Updated", response.message);
        router.refresh();
      } else {
        error("Update Failed", response.message);
      }
    } catch (err) {
      error("Update Error", "Failed to update report status");
      console.error("Error updating report status:", err);
    } finally {
      setProcessingReportId(null);
    }
  };

  const handleViewReport = (report: Report) => {
    setSelectedReport(report);
    setIsReportDialogOpen(true);
  };

  if (reports.length === 0) {
    return (
      <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl p-12">
        <div className="text-center">
          <div className="flex h-16 w-16 items-center justify-center mx-auto mb-4 rounded-xl bg-gray-800/40">
            <Flag className="h-8 w-8 text-gray-500" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            No reports found
          </h3>
          <p className="text-sm text-gray-400 max-w-sm mx-auto">
            There are currently no content reports to review. All reports will
            appear here when they are submitted.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/40 hover:bg-gray-800/40 border-gray-700/50">
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  User
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Content
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Reason
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Date
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Status
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4">
                  Moderator
                </TableHead>
                <TableHead className="text-gray-300 font-semibold px-6 py-4 text-right">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reports.map((report) => {
                const isProcessing = processingReportId === report.id;

                return (
                  <TableRow
                    key={report.id}
                    className="bg-gray-950/30 hover:bg-gray-900/50 border-gray-700/30 transition-colors"
                  >
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-purple-500/30">
                          <User className="h-5 w-5 text-purple-400" />
                        </div>
                        <div>
                          <p className="font-semibold text-white">
                            {report.reportedUser}
                          </p>
                          <p className="text-xs text-gray-500">Reported user</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      {report.shortLink == "DELETED" ? (
                        <div className="flex items-center gap-3">
                          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-500/20 border border-red-500/30">
                            <FileText className="h-5 w-5 text-red-400" />
                          </div>
                          <div>
                            <p className="font-semibold text-red-400">
                              Content removed
                            </p>
                            <p className="text-xs text-gray-500 capitalize">
                              {report.contentType}
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center gap-3">
                          <div
                            className={cn(
                              "flex h-10 w-10 items-center justify-center rounded-lg border",
                              report.contentType === "video"
                                ? "bg-blue-500/20 border-blue-500/30"
                                : "bg-pink-500/20 border-pink-500/30",
                            )}
                          >
                            {report.contentType === "video" ? (
                              <Video className="h-5 w-5 text-blue-400" />
                            ) : (
                              <ImageIcon className="h-5 w-5 text-pink-400" />
                            )}
                          </div>
                          <div>
                            <a
                              className="font-semibold text-white hover:text-blue-400 transition-colors flex items-center gap-2 group"
                              href={`/${report.contentType === "video" ? "v" : "i"}/${report.shortLink}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {report.shortLink}
                              <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                            </a>
                            <p className="text-xs text-gray-500 capitalize">
                              {report.contentType}
                            </p>
                          </div>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-white">
                          {reportReason(report.reportReason)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-300">
                        {format(new Date(report.createdAt), "MMM dd, yyyy")}
                      </div>
                      <div className="text-xs text-gray-500">
                        {format(new Date(report.createdAt), "HH:mm")}
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      {getStatusBadge(report.status)}
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center gap-2 text-sm">
                        <div className="flex h-7 w-7 items-center justify-center rounded-lg bg-gray-700/50">
                          <Shield className="w-4 h-4 text-gray-400" />
                        </div>
                        <span className="text-gray-300 font-medium">
                          {report.moderator.length > 0
                            ? report.moderator
                            : "Unassigned"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center gap-2 justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-gray-800/50 text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-white h-9 px-3"
                          onClick={() => handleViewReport(report)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        {report.status === "OPEN" && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30 hover:text-green-300 h-9 px-3"
                            onClick={() => updateStatus("PENDING", report.id)}
                            disabled={isProcessing}
                          >
                            {isProcessing ? (
                              <>
                                <Clock className="w-4 h-4 mr-1 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Check className="w-4 h-4 mr-1" />
                                Accept
                              </>
                            )}
                          </Button>
                        )}

                        {report.status === "PENDING" && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30 hover:text-red-300 h-9 px-3"
                            onClick={() => updateStatus("CLOSED", report.id)}
                            disabled={isProcessing}
                          >
                            {isProcessing ? (
                              <>
                                <Clock className="w-4 h-4 mr-1 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <X className="w-4 h-4 mr-1" />
                                Close
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>

      {selectedReport && (
        <ReportDialog
          isOpen={isReportDialogOpen}
          onClose={() => setIsReportDialogOpen(false)}
          report={selectedReport}
        />
      )}
    </>
  );
}