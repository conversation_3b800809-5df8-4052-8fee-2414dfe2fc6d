"use server";

export async function getGeoLocation(ip): Promise<{ success: false, ip: string, hostname: string, city: string, region: string, country: string, loc: string, org: string, postal: string, timezone: string, readme: string, anycast: boolean }> {
  try {
    const response = await fetch(`https://ipinfo.io/${ip}/json`);
    return await response.json();
  } catch {
    return {
      success: false,
      ip: "unknown",
      hostname: "unknown",
      city: "unknown",
      region: "unknown",
      country: "unknown",
      loc: "unknown",
      org: "unknown",
      postal: "unknown",
      timezone: "unknown",
      readme: "unknown",
      anycast: false,
    }
  }
}

import { headers } from "next/headers";

export async function getClientIp() {
  const forwarded = (await headers()).get("x-forwarded-for");
  const realIp = (await headers()).get("x-real-ip");
  const remoteAddr = (await headers()).get("cf-connecting-ip");

  return forwarded?.split(",")[0].trim() || realIp || remoteAddr || "unknown";
}
