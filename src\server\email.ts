"use server";

import { getUserByEmail } from "@/lib/db/user";
import { sendEmail } from "@/lib/email-sender";
import { prisma } from "@/lib/prisma";
import { isBefore, subHours } from "date-fns"
import { generateVerificationEmailTemplate } from "@/lib/email-templates"

type OtpData = {
  id: number;
  userId: string;
  token: string;
  requestedAt: Date;
  expired: boolean;
};

export const getRandomOtpToken =  async(): Promise<string> => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

export const isEmailExpired = async (email: string) => {
  const data = await getVerificationOtp(email);
  if (!data) {
    return true;
  }

  const today = new Date();
  const threeHoursAgo = subHours(today, 3);

  return !isBefore(threeHoursAgo, data.requestedAt);
};

export const isEmailVerified = async (email: string): Promise<boolean> => {
  const user = await getUserByEmail(email);
  if (!user) {
    return false;
  }

  return user.verified;
}

export const getVerificationOtp = async (email: string): Promise<OtpData | null> => {
  const user = await getUserByEmail(email);
  if (!user) {
    return null;
  }

  const data = await prisma.emailVerificationRequest.findFirst({
    where: {
      userId: user.id,
    }
  });
  if (!data) {
    return null;
  }

  return data;
};

export const updateVerificationOtp = async (email: string, token: string) => {
  const otpData = await getVerificationOtp(email);
  if (!otpData) {
    return;
  }

  await prisma.emailVerificationRequest.update({
    where: { id: otpData.id },
    data: {
      token: token,
      requestedAt: new Date(),
      expired: false,
    }
  });
}

export const verifyEmail = async (email: string): Promise<boolean> => {
  const user = await getUserByEmail(email);
  if (!user) return false;

  const data = await getVerificationOtp(email);
  if (!data) return false;

  await prisma.user.update({
    where: { id: user.id },
    data: {
      verified: true
    }
  });

  await prisma.emailVerificationRequest.delete({
    where: { id: data.id }
  });

  return true;
}

export const sendVerificationEmail = async (email: string, token: string) => {
  const user = await getUserByEmail(email);
  if (!user) {
    return;
  }

  const data = await getVerificationOtp(email);
  if (data) {
    await updateVerificationOtp(email, token);
    return;
  }

  const verificationData = await prisma.emailVerificationRequest.create({
    data: {
      userId: user.id,
      token: token,
      expired: false,
      requestedAt: new Date(),
    }
  })

  const emailHtml = generateVerificationEmailTemplate(token)
  const success = await sendEmail(user.email, "Email Verification", emailHtml, true);  if (!success) {
    await prisma.emailVerificationRequest.delete({
      where: {
        id: verificationData.id
      }
    });
    return;
  }
};

export const resendVerificationToken = async (email: string): Promise<boolean> => {
  const otp = await getRandomOtpToken();
  const data = await getVerificationOtp(email);
  if (!data) {
    await sendVerificationEmail(email, otp);
    return true;
  }

  await updateVerificationOtp(email, otp);
  return true;
}