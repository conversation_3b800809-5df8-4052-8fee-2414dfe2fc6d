"use client";

import { cn } from "@/lib/utils";

interface VideoGridSkeletonProps {
  count?: number;
  className?: string;
}

export function VideoGridSkeleton({
  count = 8,
  className,
}: VideoGridSkeletonProps) {
  return (
    <div
      className={cn(
        "grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-4",
        className,
      )}
    >
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "rounded-xl bg-zinc-900/50 border border-zinc-800/50 animate-pulse",
            // Vary heights for visual interest
            index === 0
              ? "col-span-2 h-[320px]" // Hero
              : index % 4 === 1
                ? "h-[280px]" // Large
                : index % 3 === 2
                  ? "h-[200px]" // Medium
                  : "h-[160px]", // Small
          )}
        >
          <div className="w-full h-full p-4 flex flex-col justify-end">
            {/* Title skeleton */}
            <div className="space-y-2">
              <div className="h-4 bg-zinc-700/50 rounded w-3/4"></div>
              <div className="h-3 bg-zinc-700/30 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}