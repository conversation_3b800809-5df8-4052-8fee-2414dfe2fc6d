"use client"

import { Bell, <PERSON>, Check } from "lucide-react"

interface MaintenanceActionsProps {
  onNotify: () => void
  onContact: () => void
  notificationPermission: NotificationPermission | "default"
  showSuccess: boolean
}

export function MaintenanceActions({
  onNotify,
  onContact,
  notificationPermission,
  showSuccess,
}: MaintenanceActionsProps) {
  return (
    <div className="space-y-6 animate-fadeIn" style={{ animationDuration: "1.8s", animationDelay: "0.8s" }}>
      <div className="space-y-3">
        <h3 className="text-xl font-semibold text-white">Stay Updated</h3>
        <p className="text-white/70">Get notified when we&apos;re back online and ready to serve you.</p>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={onNotify}
          disabled={notificationPermission === "granted" && !showSuccess}
          className={`relative px-6 py-3 rounded-lg flex items-center justify-center transition-all duration-300 ${
            notificationPermission === "granted" && !showSuccess
              ? "bg-[#4CAF50] text-white cursor-default"
              : "bg-[#B066FF] hover:bg-[#9A4DFF] text-white"
          }`}
        >
          {notificationPermission === "granted" && !showSuccess ? (
            <>
              <Check className="w-5 h-5 mr-2" />
              <span>Notifications Enabled</span>
            </>
          ) : (
            <>
              <Bell className="w-5 h-5 mr-2" />
              <span>Get Notified</span>
            </>
          )}

          {/* Success message overlay */}
          {showSuccess && (
            <div className="absolute inset-0 bg-[#4CAF50] rounded-lg flex items-center justify-center animate-fadeIn">
              <Check className="w-5 h-5 mr-2" />
              <span>Notification Sent!</span>
            </div>
          )}
        </button>
        <button
          onClick={onContact}
          className="bg-transparent hover:bg-white/5 text-white border border-white/20 px-6 py-3 rounded-lg flex items-center justify-center transition-colors"
        >
          <Mail className="w-5 h-5 mr-2" />
          <span>Contact Support</span>
        </button>
      </div>
    </div>
  )
}