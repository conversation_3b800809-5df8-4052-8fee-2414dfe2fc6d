"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { hasSubscribedChannel, subscribeChannel, unSubscribeChannel } from "@/server/channel";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
  data?: any;
}

type Props = {
  channelId: string;
  hasSubcribed: boolean;
}

export const submitSubscribeChannel = async ({ channelId, hasSubcribed }: Props): Promise<Callback> => {
  try {
    const clientIp = await getClientIp();
    if (rateLimiter(clientIp)) {
      return {
        success: false,
        message: "Rate limit exceeded. Please try again later.",
      }
    }

    const userSession = await getUserSession();
    const userId = userSession.userId;
    if (!userSession || !userId) {
      return {
        success: false,
        message: "You must be logged in to subscribe to a channel.",
      }
    }

    if (await hasSubscribedChannel(userId, channelId)) {
      await unSubscribeChannel(userId, channelId);
      hasSubcribed = false;
    } else {
      await subscribeChannel(userId, channelId);
      hasSubcribed = true;
    }

    await createLog(userId, LogConstants.SUBSCRIBED_TO_USER, LogActions.ACCOUNT);

    return {
      success: true,
      message: !hasSubcribed ? "Unsubscribed from channel" : "Subscribed to channel",
      data: {
        hasSubcribed,
      }
    }
  } catch (error) {
    console.error("Error subscribing to channel:", error);

    return {
      success: false,
      message: "An error occurred while subscribing to the channel.",
    }
  }
}
