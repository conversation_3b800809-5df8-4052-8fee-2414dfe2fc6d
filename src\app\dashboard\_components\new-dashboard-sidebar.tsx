"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  Home,
  Users,
  Settings,
  HelpCircle,
  CreditCard,
  User,
  LogOut,
} from "lucide-react";
import { useEffect, useState } from "react";
import Image from "next/image";

const navigationItems = [
  {
    name: "Home",
    href: "/dashboard",
    icon: Home,
  },
  {
    name: "Community",
    href: "/community",
    icon: Users,
  },
  {
    name: "Account",
    href: "/dashboard/settings",
    icon: Settings,
  },
  {
    name: "Support",
    href: "/dashboard/tickets",
    icon: HelpCircle,
  },
  {
    name: "Billing",
    href: "/dashboard/billing",
    icon: CreditCard,
  },
  {
    name: "Profile",
    href: "/dashboard/profile",
    icon: User,
  },
  {
    name: "Logout",
    href: "#",
    icon: LogOut,
  },
];

export function NewDashboardSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const handleLogout = () => {
    router.push("/signout");
  };

  if (isMobile) {
    return null;
  }

  return (
    <div
      className={`fixed left-0 top-0 h-screen bg-black/95 border-r border-white/10 flex flex-col py-6 z-40 hidden md:flex transition-all duration-300 ease-in-out ${
        isHovered ? "w-[200px]" : "w-[70px]"
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Logo */}
      <div className="mb-8 flex items-center px-4">
        <div className="flex items-center gap-3">
          <Image
            src="/assets/images/svg/logo.svg"
            alt="StreamBliss"
            width={32}
            height={32}
            className="w-8 h-8 flex-shrink-0"
          />
          <span
            className={`text-white font-semibold text-lg transition-all duration-300 whitespace-nowrap overflow-hidden ${
              isHovered
                ? "opacity-100 translate-x-0 delay-150 w-auto"
                : "opacity-0 -translate-x-4 pointer-events-none delay-0 w-0"
            }`}
          >
            StreamBliss
          </span>
        </div>
      </div>

      {/* Navigation Items */}
      <div
        className="flex flex-col space-y-2 flex-1 px-3"
        data-tutorial="user-menu"
      >
        {navigationItems
          .filter((item) => item.name !== "Logout")
          .map((item) => {
            const isActive =
              pathname === item.href ||
              (item.href === "/dashboard" &&
                pathname.startsWith("/dashboard") &&
                !pathname.includes("/settings") &&
                !pathname.includes("/tickets") &&
                !pathname.includes("/billing") &&
                !pathname.includes("/profile"));

            const NavItem = ({ children }: { children: React.ReactNode }) => (
              <div
                className={`group relative flex items-center rounded-xl transition-all duration-200 ${
                  isActive
                    ? "bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30"
                    : "hover:bg-white/5 border border-transparent hover:border-white/10"
                }`}
              >
                {children}
              </div>
            );

            return (
              <NavItem key={item.name}>
                <Link
                  href={item.href}
                  className="flex items-center w-full p-3 rounded-xl"
                >
                  <item.icon
                    className={`w-5 h-5 flex-shrink-0 transition-colors duration-200 ${
                      isActive
                        ? "text-white"
                        : "text-white/70 group-hover:text-white"
                    }`}
                  />
                  <span
                    className={`ml-3 font-medium transition-all duration-300 whitespace-nowrap overflow-hidden ${
                      isHovered
                        ? "opacity-100 translate-x-0 delay-150 w-auto"
                        : "opacity-0 -translate-x-4 pointer-events-none delay-0 w-0"
                    } ${
                      isActive
                        ? "text-white"
                        : "text-white/70 group-hover:text-white"
                    }`}
                  >
                    {item.name}
                  </span>
                </Link>
              </NavItem>
            );
          })}
      </div>

      {/* Logout Button at Bottom */}
      <div className="px-3 pb-2">
        <div className="group relative flex items-center rounded-xl transition-all duration-200 hover:bg-red-500/10 border border-transparent hover:border-red-500/20">
          <button
            onClick={handleLogout}
            className="flex items-center w-full p-3 rounded-xl cursor-pointer"
          >
            <LogOut className="w-5 h-5 flex-shrink-0 transition-colors duration-200 text-white/70 group-hover:text-red-400" />
            <span
              className={`ml-3 font-medium transition-all duration-300 whitespace-nowrap overflow-hidden ${
                isHovered
                  ? "opacity-100 translate-x-0 delay-150 w-auto"
                  : "opacity-0 -translate-x-4 pointer-events-none delay-0 w-0"
              } text-white/70 group-hover:text-red-400`}
            >
              Logout
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}