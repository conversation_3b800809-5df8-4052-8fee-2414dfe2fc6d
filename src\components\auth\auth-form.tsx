"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/icons"
import { useEnhancedToast } from "../ui/enhanced-toast"

interface AuthFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type: "login" | "register"
}

export function AuthForm({ type, className, ...props }: AuthFormProps) {
  const router = useRouter()
  const { success, error: showError } = useEnhancedToast();
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string>("")

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setIsLoading(true)
    setError("")

    const formData = new FormData(event.currentTarget)
    const email = formData.get("email") as string
    const password = formData.get("password") as string
    const name = type === "register" ? (formData.get("name") as string) : null

    try {
      if (type === "register") {
        // Validate inputs
        if (!email || !password || !name) {
          throw new Error("Please fill in all fields")
        }
        if (password.length < 6) {
          throw new Error("Password must be at least 6 characters")
        }

        const request = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name,
            email,
            password,
          }),
        });

        if (request.ok) {
          success("Registration Successful", "Account created successfully");
          router.push("/login");
          router.refresh();
        } else {
          const errorMessage = await request.text();
          throw new Error(errorMessage || "Registration failed");
        }
      } else {
        if (!email || !password) {
          throw new Error("Please fill in all fields")
        }

        const request = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            password,
          }),
        });

        const data = await request.json();

        if (data.status === 200) {
          success('Login Successful', 'Successfully logged in');
          router.push('/dashboard');
          router.refresh();
        } else if (data.status === 201 && data.redirect) {
          router.push(data.redirect);
        } else {
          throw new Error(data.message || "Login failed");
        }
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message)
        showError("Authentication Error", err.message);
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid gap-6" {...props}>
      <form onSubmit={onSubmit}>
        <div className="grid gap-4">
          {type === "register" && (
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="John Doe"
                type="text"
                autoCapitalize="none"
                autoCorrect="off"
                disabled={isLoading}
              />
            </div>
          )}
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              placeholder="********"
              type="password"
              autoComplete={type === "register" ? "new-password" : "current-password"}
              disabled={isLoading}
            />
          </div>
          {error && <div className="text-sm text-red-500">{error}</div>}
          <Button className="w-full" type="submit" disabled={isLoading}>
            {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
            {type === "register" ? "Create account" : "Sign in"}
          </Button>
        </div>
      </form>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            {type === "register" ? "Already have an account?" : "Don't have an account?"}
          </span>
        </div>
      </div>
      <Button
        variant="outline"
        type="button"
        disabled={isLoading}
        onClick={() => router.push(type === "register" ? "/login" : "/register")}
      >
        {type === "register" ? "Sign in" : "Create account"}
      </Button>
    </div>
  )
}