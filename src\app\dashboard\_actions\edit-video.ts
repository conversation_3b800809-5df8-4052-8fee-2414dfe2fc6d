"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUser } from "@/server/session";
import { getVideoDataById, updateVideo } from "@/server/video";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type Params = {
  videoId: string;
  title: string;
  isPrivate: boolean;
  commentsDisabled: boolean;
  musicDisabled: boolean;
  showCommunity: boolean;
};

export default async function submitEditVideo({ videoId, title, isPrivate, commentsDisabled, musicDisabled, showCommunity }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!videoId) {
    return {
      success: false,
      message: "Video ID is required",
    };
  }

  const user = await getUser();
  if (!user) {
    return {
      success: false,
      message: "You must be logged in to edit video settings",
    };
  }

  const videoData = await getVideoDataById(videoId);
  if (!videoData) {
    return {
      success: false,
      message: "Video not found",
    };
  }

  if (videoData.userId !== user.id) {
    return {
      success: false,
      message: "You do not have permission to edit this video",
    };
  }

  const finalTitle = title || videoData.title;

  await updateVideo(videoId, finalTitle, isPrivate, commentsDisabled, musicDisabled, showCommunity);
  await createLog(user.id, LogConstants.UPDATED_VIDEO_SETTINGS, LogActions.VIDEO);

  return {
    success: true,
    message: "Video settings updated successfully",
  };
}
