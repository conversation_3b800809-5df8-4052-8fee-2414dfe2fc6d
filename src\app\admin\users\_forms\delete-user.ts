"use server";

import { deleteUser } from "@/lib/db/admin";
import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { getRoleById } from "@/server/role";
import { getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type DeleteUserData = {
  userId: string;
}

export default async function submitDeleteUser({ userId }: DeleteUserData) {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!userId) {
    return { success: false, message: "User ID is required" };
  }

  const user = await getUserById(userId);
  if (!user) {
    return { success: false, message: "User not found" };
  }

  const _user = await getUser();
  if (!_user) {
    return { success: false, message: "User not found" };
  }
  
  const userRole = await getUserById(_user.id);
  if (!userRole) {
    return { success: false, message: "User Role not found" };
  }

  const userRoleName = await getRoleById(userRole.roleId);
  if (!userRoleName) {
    return { success: false, message: "User Role Name not found" };
  }

  const canPerform = await hasPermission(userRole.roleId, ["USER_DELETE"]);
  if (!canPerform) {
    return { success: false, message: "You do not have permission to change the users roles" };
  }

  await deleteUser(userId);
  await createLog(_user.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_DELETE, LogActions.ACCOUNT);

  return {
    success: true,
    message: "User deleted successfully"
  }
}
