/* Component-Specific Styles */

/* Tab content scrolling */
.tabs-content-scroll {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(168, 85, 247, 0.2) transparent;
}

.tabs-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.tabs-content-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-content-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(168, 85, 247, 0.2);
  border-radius: 3px;
}

.tabs-content-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(168, 85, 247, 0.4);
}

.scrollbar-track-muted::-webkit-scrollbar-track {
  background-color: hsl(var(--muted));
}

/* Admin nav specific styles */
.admin-nav-item {
  position: relative;
  transition: all 0.3s ease;
}

.admin-nav-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(184, 81, 224, 0.1),
    rgba(235, 72, 155, 0.1)
  );
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.admin-nav-item:hover::before {
  opacity: 1;
}

/* Action Cards Enhancement */
.action-card {
  position: relative;
  overflow: hidden;
  transform-origin: left center;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:focus {
  outline: 2px solid rgba(139, 92, 246, 0.6);
  outline-offset: 2px;
}

.action-card.active {
  animation: stepPulse 2s infinite;
}

.action-progress-line {
  background: linear-gradient(180deg, #8b5cf6 0%, #3b82f6 100%);
  animation: progressGlow 2s ease-in-out infinite alternate;
}

/* Enhanced upload-box animation */
.upload-box {
  position: relative;
  overflow: hidden;
}

.upload-box::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(138, 43, 226, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.group:hover .upload-box::after {
  opacity: 1;
}

/* Interactive preview enhancements */
.preview-overlay {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  backdrop-filter: blur(2px);
}

/* Progress dots animation */
.progress-dot {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-dot.active {
  animation: dotPulse 1.5s ease-in-out infinite;
}

/* Step indicator slide-in animation */
.step-indicator {
  animation: slideInUp 0.5s ease-out;
}

/* Framer-style card components */
.framer-card {
  will-change: transform, opacity;
  transform-origin: center bottom;
  transition: all 0.7s cubic-bezier(0.16, 1, 0.3, 1);
}

.framer-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 0 60px rgba(138, 43, 226, 0.08);
}

.framer-image {
  will-change: transform, opacity;
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.framer-ease {
  transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
}

.framer-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.01);
  border-radius: inherit;
  backdrop-filter: blur(0.5px);
  transition: backdrop-filter 0.5s ease;
}

.framer-card:hover::before {
  backdrop-filter: blur(1px);
}

.framer-card p,
.framer-card h1,
.framer-card h2,
.framer-card h3 {
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.framer-card .upload-box {
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
}

/* Slide-in Card Toast Animations */
.toast-card {
  transform-origin: top right;
  will-change: transform, opacity;
}

/* Toast stacking effect */
.toast-card:nth-child(1) {
  z-index: 50;
}

.toast-card:nth-child(2) {
  z-index: 49;
  transform: translateY(-4px) scale(0.98);
  opacity: 0.9;
}

.toast-card:nth-child(3) {
  z-index: 48;
  transform: translateY(-8px) scale(0.96);
  opacity: 0.8;
}

.toast-card:nth-child(n+4) {
  z-index: 47;
  transform: translateY(-12px) scale(0.94);
  opacity: 0.7;
}

/* Hover effects for toast cards */
.toast-card:hover {
  transform: translateY(-2px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Progress bar for auto-dismiss */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(138, 43, 226, 0.8), rgba(59, 130, 246, 0.8));
  border-radius: 0 0 1rem 1rem;
  animation: toast-progress 5s linear forwards;
}

/* Toast icon animations */
.toast-icon {
  animation: toast-icon-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Success checkmark animation */
.toast-success-icon {
  animation: toast-success 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Error shake animation */
.toast-error-icon {
  animation: toast-error 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Hero cinematic glow effect */
.hero-cinematic-glow {
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.08),
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 60px rgba(138, 43, 226, 0.15);
  transition: all 0.3s ease;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

.hero-cinematic-glow:hover {
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.12),
    0 12px 48px rgba(0, 0, 0, 0.4),
    0 0 80px rgba(138, 43, 226, 0.25);
}
