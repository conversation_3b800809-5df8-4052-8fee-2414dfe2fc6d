import { getBillingUser, revokeSubscription } from "@/lib/db/user";
import { stripe } from "@/lib/stripe";
import { createNotification } from "@/server/notifications";
import { UserRoundIcon } from "lucide-react";
import { NextResponse } from "next/server";

const allowedTypes = ["invoice.payment_succeeded", "invoice.payment_failed", "customer.subscription.updated", "customer.subscription.deleted"];

export async function POST(req: Request) {
  const payload = await req.text();
  const sig = req.headers.get("stripe-signature")!;
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

  try {
    const event = stripe.webhooks.constructEvent(payload, sig, endpointSecret);

    if (!allowedTypes.includes(event.type)) {
      return new NextResponse("Event type not allowed", { status: 400 });
    }

    const session = event.data.object;
    console.log("Payment successful:", session);
    switch (event.type) {
      case "invoice.payment_succeeded":
        const _session = await stripe.checkout.sessions.retrieve(event.data.object.id);
        const customerId = _session.customer as string;
        const customer = await stripe.customers.retrieve(customerId);

        break;
      case "invoice.payment_failed":
        break;
      case "customer.subscription.updated":
        break;
      case "customer.subscription.deleted":
        const subscription = await stripe.subscriptions.retrieve(event.data.object.id);
        const user = await getBillingUser(subscription.customer as string);
        if (!user) {
          return new NextResponse("User not found", { status: 400 });
        }

        await revokeSubscription(user.id);
        await createNotification(user.id, "Your subscription has been cancelled", "WARNING");
        // TODO: cancel subscription notification
        break;
      default: break;
    }
    // TODO: Update your database (e.g., mark order as paid)

    return NextResponse.json({ received: true });
  } catch (err: any) {
    return new NextResponse(`Webhook error: ${err.message}`, { status: 400 });
  }
}
