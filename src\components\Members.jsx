"use client";

import Image from "next/image";
import { MEMBERS_DATA_LIST } from "../../utils/helper";
import Description from "./common/Description";
import Icons from "./common/Icons";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { Autoplay } from "swiper/modules";
import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect, useRef, useState } from "react";
import { ExternalLink } from "lucide-react";

const Members = () => {
  const swiperRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.stop();
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (swiperRef.current && swiperRef.current.autoplay) {
      swiperRef.current.autoplay.start();
    }
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case "twitch":
        return (
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="text-purple-500"
          >
            <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z" />
          </svg>
        );
      case "youtube":
        return (
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="text-red-500"
          >
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
          </svg>
        );
      default:
        return <ExternalLink size={24} className="text-gray-400" />;
    }
  };

  return (
    <div className="py-10 md:py-16 lg:py-[140px] max-[400px]:px-4 max-w-[1920px] mx-auto">
      <h2
        data-aos="fade-up"
        data-aos-duration={800}
        className="text-white text-center font-semibold text-custom-5xl leading-130 max-lg:text-4xl max-md:text-3xl px-4 max-w-4xl mx-auto"
      >
        What Members Are Saying
      </h2>
      <div data-aos="fade-up" data-aos-duration={1000}>
        <Description className="text-center mt-4 max-md:!text-sm px-4">
          We&apos;ve compiled some helpful reviews for you here.
        </Description>
      </div>
      <div
        data-aos="fade-up"
        data-aos-duration={1000}
        className="max-w-[694px] h-[1px] border-t border-solid border-gradient mt-3 md:mt-5 mx-auto"
      ></div>
      <div className="mt-12">
        <Swiper
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={94}
          slidesPerView={3}
          centeredSlides={true}
          loop={true}
          breakpoints={{
            0: {
              slidesPerView: 1,
              spaceBetween: 20,
            },
            400: {
              slidesPerView: 1.3,
              spaceBetween: 20,
            },
            540: {
              slidesPerView: 1.3,
              spaceBetween: 40,
            },
            768: {
              slidesPerView: 2.2,
              spaceBetween: 40,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 50,
            },
            1200: {
              slidesPerView: 3,
              spaceBetween: 65,
            },
            1530: {
              slidesPerView: 3,
              spaceBetween: 90,
            },
          }}
          autoplay={{
            delay: 2500,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          modules={[Autoplay]}
          className="membersSlider"
        >
          {MEMBERS_DATA_LIST.map((obj, i) => (
            <SwiperSlide
              className="min-h-[280px] !flex flex-col justify-between px-2"
              key={i}
            >
              <div className="flex-1">
                <Icons icon="memberCommon" />
                <p className="text-base leading-160 text-white mt-3 md:mt-4 max-lg:max-w-[437px] text-justify hyphens-auto break-words">
                  {obj.description}
                </p>
              </div>
              <div className="flex gap-2.5 items-center justify-between mt-4">
                {obj.profileUrl ? (
                  <a
                    href={obj.profileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex gap-2.5 items-center hover:opacity-80 transition-opacity duration-200 cursor-pointer"
                    aria-label={`Visit ${obj.name}&apos;s ${obj.platform} profile`}
                  >
                    <Image
                      src={obj.profileImg}
                      width={46}
                      height={46}
                      alt="profile-img"
                      className="rounded-full"
                    />
                    <div>
                      <p className="font-medium text-base leading-160 text-white hover:text-custom-purple transition-colors duration-200">
                        {obj.name}
                      </p>
                      <p className="font-normal text-sm leading-160 text-white opacity-70">
                        {obj.profession}
                      </p>
                    </div>
                  </a>
                ) : (
                  <div className="flex gap-2.5 items-center">
                    <Image
                      src={obj.profileImg}
                      width={46}
                      height={46}
                      alt="profile-img"
                      className="rounded-full"
                    />
                    <div>
                      <p className="font-medium text-base leading-160 text-white">
                        {obj.name}
                      </p>
                      <p className="font-normal text-sm leading-160 text-white opacity-70">
                        {obj.profession}
                      </p>
                    </div>
                  </div>
                )}
                {obj.profileUrl && (
                  <a
                    href={obj.profileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-auto flex items-center gap-1 text-white/70 hover:text-white transition-colors duration-200 hover:scale-110 transform"
                    aria-label={`Visit ${obj.name}&apos;s ${obj.platform} profile`}
                  >
                    {getPlatformIcon(obj.platform)}
                  </a>
                )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default Members;