"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { resetPassword } from "@/server/password";
import { getAdminUser, getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
}

type Props = {
  userId: string;
};

export default async function sendPasswordReset({ userId }: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!userId) {
    return { success: false, message: "User ID is required" };
  }

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_RESET_PASSWORD"]);
  if (!user) {
    return {
      success: false,
      message: "Not authorized to reset passwords."
    }
  }

  const target = await getUserById(userId);
  if (!target) {
    return { success: false, message: "User not found." };
  }

  const response = await resetPassword(target.id);
  if (!response) {
    return { success: false, message: "Failed to reset password." };
  }

  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_RESET_PASSWORD, LogActions.ACCOUNT);

  return { success: true, message: "Password reset email sent." };
}
