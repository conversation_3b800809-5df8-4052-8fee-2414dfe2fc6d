import {NextRequest, NextResponse} from "next/server"
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {readableWebToNode} from "@/lib/helper";
import { PassThrough, Readable } from "stream";
import WebSocket from "ws";

interface FetchRequestInit extends RequestInit {
    duplex?: 'half';
}

export const config = {
    api: {
        bodyParser: false,
    },
};

function readableWebToProgressStream(webStream: ReadableStream<Uint8Array>, totalBytes: number, onProgress: (percent: number) => void): Readable {
    const reader = webStream.getReader();
    let uploaded = 0;

    return new Readable({
        async read() {
            const { done, value } = await reader.read();
            if (done) {
                this.push(null);
            } else {
                uploaded += value.length;
                if (totalBytes > 0) {
                    const percent = Math.round((uploaded / totalBytes) * 100);
                    onProgress(percent);
                }
                this.push(Buffer.from(value));
            }
        }
    });
}

export async function POST(request: NextRequest) {
    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No valid user-session."});
    }

    const contentLength = request.headers.get("content-length");
    const totalBytes = contentLength ? parseInt(contentLength, 10) : 0;

    const ws = new WebSocket("wss://dev-socket.streambliss.cloud?token=" + userSession.accessToken);

    ws.on("open", () => {
        ws.send(JSON.stringify({
            type: "video-progress",
            action: "subscribe",
            userId: userSession.userId
        }));
    });

    ws.on("error", (err) => console.error("WebSocket error:", err));

    const progressStream = readableWebToProgressStream(request.body!, totalBytes, (percent) => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: "video-progress",
                action: "rely",
                userId: userSession.userId,
                progress: percent
            }));
        }
    });

    const response = await fetch(`${process.env.VIDEO_API_URL}/videos/upload`, {
        method: "POST",
        headers: {
            "Content-Type": request.headers.get("content-type") || "",
            "Authorization": `Bearer ${userSession.accessToken}`,
            "x-api-key": process.env.API_SERVER_KEY!,
            "Content-Length": contentLength || ""
        },
        body: progressStream as any,
        duplex: "half"
    } as any);

    if (ws.readyState === WebSocket.OPEN) ws.close();

    if (!response.ok) {
        const text = await response.text();
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: text});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, success: true});
}
