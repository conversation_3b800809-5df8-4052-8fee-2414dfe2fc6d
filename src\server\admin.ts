"use server";

import { getRoleById } from "./role";
import { getUserById } from "@/lib/db/user";
import { getUserSettings } from "./user-settings";

export async function hasPermission(roleId: number, requiredPermissions: string[]) {
  const role = await getRoleById(roleId);
  if (!role) return false;

  const userPermissions = role.permissions.map(p => p.permission.name);
  if (userPermissions.includes("ALL")) return true;

  const hasAccess = requiredPermissions.some(permission => userPermissions.includes(permission));

  return hasAccess;
}

export async function needsTwoFactorAuthAsAdmin(userId: string) {
  const user = await getUserById(userId);
  if (!user) return false;

  const settings = await getUserSettings(user.id);
  if (!settings) return false;

  const role = await getRoleById(user.roleId);
  if (!role) return false;

  return !settings.twoFactorEnabled && role.name != "User";
}
