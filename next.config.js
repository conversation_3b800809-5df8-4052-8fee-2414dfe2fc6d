/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
    ],
    formats: ["image/webp", "image/avif"],
  },
  // Turbopack configuration
  turbopack: {
    rules: {
      // Ignore .node files
      "*.node": {
        loaders: ["ignore-loader"],
      },
    },
    resolveAlias: {
      // Disable diskusage for client-side in Turbopack
      diskusage: "./lib/empty-module.js",
    },
  },
  // Webpack configuration (for production builds)
  webpack: (config, { isServer }) => {
    // Ignore .node files during build
    config.module.rules.push({
      test: /\.node$/,
      use: "ignore-loader",
    });

    // Only use diskusage on the server side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        diskusage: false,
      };
    }

    return config;
  },
};

export default nextConfig;
