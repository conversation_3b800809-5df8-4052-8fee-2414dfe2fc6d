"use server";

import { decryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { getUserSettings } from "@/server/user-settings";
import {LogConstants} from "@/server/log-constants";
import {createLog} from "@/server/logs";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type DeleteImageProps = {
  imageId: string;
}

export default async function submitDeleteImage({ imageId }: DeleteImageProps): Promise<Callback> {
  const clientIp = await getClientIp();
  if (rateLimiter(clientIp)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later."
    }
  }

  if (!imageId) {
    return {
      success: false,
      message: "Image ID is required"
    }
  }

  try {
    const userSession = await getUserSession();
    const userId = userSession.userId;
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        message: "User not found"
      }
    }

    const userSettings = await getUserSettings(userId);
    if (!userSettings) {
      return {
        success: false,
        message: "User settings not found"
      }
    }
  
    const decryptedAuth = decryptData(userSettings.secretToken, userSettings.secretTokenIv);
    if (!decryptedAuth) {
      return {
        success: false,
        message: "Invalid auth token"
      }
    }

    const response = await fetch(`${process.env.VIDEO_API_URL}/image/${userId}/${imageId}`, {
      method: "DELETE",
      headers: {
        "Auth-Token": decryptedAuth,
      }
    });

    if (!response.ok) {
      return {
        success: false,
        message: "Failed to delete image"
      }
    }

    await createLog(user.id, LogConstants.DELETED_IMAGE,LogActions.IMAGE);

    const data = await response.json();
    return {
      success: true,
      message: data.message || "Image deleted successfully"
    }
  } catch (error) {
    return {
      success: false,
      message: "Failed to delete image"
    }
  }
}
