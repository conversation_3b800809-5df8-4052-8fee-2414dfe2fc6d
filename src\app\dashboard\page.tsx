import { getUserVideos } from "@/lib/db/video";
import { getUser } from "src/server/session";
import Dashboard<PERSON>lient from "./client";
import { getUserSettings } from "@/server/user-settings";
import { redirect } from "next/navigation";
import { decryptData } from "@/lib/crypter";
import { getUserNotifications } from "@/server/notifications";
import { getImageByUserId } from "@/server/image";
import { getUserById } from "@/lib/db/user";
import { hasPermission } from "@/server/admin";
import { getUserProfilePicture } from "@/server/profile";

export default async function DashboardPage() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }

  const dbUser = await getUserById(user.id);
  const hasAccessToAdmin = dbUser
    ? await hasPermission(dbUser.roleId, ["ADMIN_PAGE"])
    : false;
  const videos = await getUserVideos(user.id);
  const images = (await getImageByUserId(user.id)) || [];
  const notifications = await getUserNotifications(user.id);
  const userSettings = await getUserSettings(user.id);
  const userImage = await getUserProfilePicture(user.id);
  if (!userSettings) {
    redirect("/login");
  }

  const decrypted = await decryptData(
    userSettings.secretToken,
    userSettings.secretTokenIv,
  );
  if (!decrypted) {
    redirect("/login");
  }

  return (
    <div className="min-h-screen bg-background">
      <DashboardClient
        userName={user.name || ""}
        userId={user.id}
        userImage={userImage}
        videos={videos}
        images={images}
        authToken={decrypted}
        notifications={notifications}
        hasAccessToAdmin={hasAccessToAdmin}
        userSubscription={user.package}
        hasSeenTutorial={dbUser?.hasSeenTutorial ?? false}
      />
    </div>
  );
}