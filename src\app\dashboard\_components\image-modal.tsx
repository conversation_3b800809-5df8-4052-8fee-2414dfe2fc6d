"use client";

import { useEffect, useRef, useState } from "react";
import NextImage from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  X,
  Link2,
  ExternalLink,
  Download,
  Share2,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Copy,
  CheckCircle2,
  Calendar,
  Image as ImageIcon,
  Eye,
  Maximize,
  FileImage,
} from "lucide-react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  image: {
    id: string;
    url: string;
    title?: string | null;
    name?: string | null;
    shortLink?: string | null;
    createdAt: Date;
  } | null;
  userId: string;
}

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "";
const API_URL = "https://dev-api.streambliss.cloud";

export function ImageModal({ isOpen, onClose, image, userId }: ImageModalProps) {
  const { success, error } = useEnhancedToast();
  const modalRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<"preview" | "details">("preview");
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageNaturalSize, setImageNaturalSize] = useState<{
    width: number;
    height: number;
  } | null>(null);

  const imageTitle = image?.title || image?.name || "Untitled Image";
  const imageUrl = image?.shortLink ? `${BASE_URL}/i/${image.shortLink}` : "";

  const formattedDate = image?.createdAt
    ? formatDistanceToNow(image.createdAt, { addSuffix: true })
    : "";

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
      setZoom(1);
      setRotation(0);
      setIsFullscreen(false);
      setImageLoaded(false);
      setImageError(false);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  const copyImageLink = () => {
    if (image?.shortLink) {
      navigator.clipboard.writeText(imageUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
      success("Link Copied", "Image link has been copied to clipboard");
    }
  };

  const shareImage = () => {
    if (navigator.share && image?.shortLink) {
      navigator
        .share({
          title: imageTitle,
          text: `Check out this image: ${imageTitle}`,
          url: imageUrl,
        })
        .catch(console.error);
    } else {
      copyImageLink();
    }
  };

  const downloadImage = async () => {
    if (!image?.url) return;

    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${imageTitle.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      success("Download Started", "Your image is downloading");
    } catch (err) {
      console.error("Download error:", err);
      error("Download Failed", "Unable to download image");
    }
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.25, 0.25));
  };

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const resetTransforms = () => {
    setZoom(1);
    setRotation(0);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    setImageLoaded(true);
    const img = e.target as HTMLImageElement;
    setImageNaturalSize({
      width: img.naturalWidth,
      height: img.naturalHeight,
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  if (!isOpen || !image) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/98 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <motion.div
          ref={modalRef}
          className={cn(
            "relative bg-[#110018] border border-purple-500/20 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300",
            isFullscreen
              ? "w-full h-full max-w-none max-h-none rounded-none"
              : "w-full max-w-6xl max-h-[90vh]",
          )}
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ duration: 0.3, type: "spring", damping: 20 }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-purple-500/20 bg-[#0a0010]">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                <ImageIcon className="w-4 h-4 text-purple-400" />
              </div>
              <div className="min-w-0 flex-1">
                <h2
                  className="font-semibold text-white truncate text-lg"
                  title={imageTitle}
                >
                  {imageTitle}
                </h2>
                <p className="text-sm text-white/70">
                  Uploaded {formattedDate}
                </p>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-2 ml-4">
              <Button
                onClick={toggleFullscreen}
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-white/70 hover:text-white hover:bg-white/10"
              >
                <Maximize className="h-4 w-4" />
              </Button>

              <Button
                onClick={onClose}
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-white/70 hover:text-white hover:bg-white/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div
            className={cn(
              "grid min-h-[500px]",
              isFullscreen ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-3",
            )}
          >
            {/* Image Preview Section */}
            <div
              className={cn(
                "relative bg-black/50 flex items-center justify-center",
                isFullscreen ? "col-span-1" : "lg:col-span-2",
              )}
            >
              {/* Image Controls Overlay */}
              {imageLoaded && !isFullscreen && (
                <div className="absolute top-4 left-4 z-10 flex gap-2">
                  <div className="bg-black/80 rounded-lg p-2 flex gap-1">
                    <Button
                      onClick={handleZoomOut}
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-white hover:bg-white/20"
                      disabled={zoom <= 0.25}
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>

                    <Button
                      onClick={resetTransforms}
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-white hover:bg-white/20 text-xs"
                    >
                      {Math.round(zoom * 100)}%
                    </Button>

                    <Button
                      onClick={handleZoomIn}
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-white hover:bg-white/20"
                      disabled={zoom >= 3}
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>

                    <Separator
                      orientation="vertical"
                      className="bg-white/20 mx-1"
                    />

                    <Button
                      onClick={handleRotate}
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-white hover:bg-white/20"
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Loading State */}
              {!imageLoaded && !imageError && (
                <div className="flex flex-col items-center justify-center text-white/70">
                  <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                  <p className="text-sm">Loading image...</p>
                </div>
              )}

              {/* Error State */}
              {imageError && (
                <div className="flex flex-col items-center justify-center text-white/70">
                  <FileImage className="w-16 h-16 mb-4 text-white/30" />
                  <p className="text-sm">Failed to load image</p>
                </div>
              )}

              {/* Image */}
              {image.url && (
                <div className="relative w-full h-full flex items-center justify-center p-4">
                  <NextImage
                    ref={imageRef}
                    src={API_URL + "/images/stream/" + userId + "/" + image.id}
                    alt={imageTitle}
                    width={800}
                    height={600}
                    className={cn(
                      "max-w-full max-h-full object-contain transition-all duration-300",
                      !imageLoaded && "opacity-0",
                    )}
                    style={{
                      transform: `scale(${zoom}) rotate(${rotation}deg)`,
                    }}
                    onLoad={handleImageLoad}
                    onError={() => setImageError(true)}
                    priority
                    unoptimized
                  />
                </div>
              )}
            </div>

            {/* Details Panel */}
            {!isFullscreen && (
              <div className="bg-[#0a0010] border-l border-purple-500/20 flex flex-col">
                {/* Tab Navigation */}
                <div className="p-4 border-b border-purple-500/20">
                  <div className="flex gap-1 p-1 bg-black/20 rounded-lg">
                    <button
                      onClick={() => setActiveTab("preview")}
                      className={cn(
                        "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        activeTab === "preview"
                          ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                          : "text-white/70 hover:text-white hover:bg-white/5",
                      )}
                    >
                      Preview
                    </button>
                    <button
                      onClick={() => setActiveTab("details")}
                      className={cn(
                        "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        activeTab === "details"
                          ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                          : "text-white/70 hover:text-white hover:bg-white/5",
                      )}
                    >
                      Details
                    </button>
                  </div>
                </div>

                {/* Tab Content */}
                <div className="flex-1 p-4 overflow-y-auto">
                  {activeTab === "preview" && (
                    <div className="space-y-4">
                      {/* Action Buttons */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-white/90 mb-3">
                          Actions
                        </h4>

                        <Button
                          onClick={copyImageLink}
                          variant="outline"
                          size="sm"
                          className="w-full justify-start border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                          disabled={!image.shortLink}
                        >
                          {copySuccess ? (
                            <>
                              <CheckCircle2 className="w-4 h-4 mr-2" />
                              Copied!
                            </>
                          ) : (
                            <>
                              <Copy className="w-4 h-4 mr-2" />
                              Copy Link
                            </>
                          )}
                        </Button>

                        <Button
                          onClick={shareImage}
                          variant="outline"
                          size="sm"
                          className="w-full justify-start border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                          disabled={!image.shortLink}
                        >
                          <Share2 className="w-4 h-4 mr-2" />
                          Share Image
                        </Button>

                        <Button
                          onClick={downloadImage}
                          variant="outline"
                          size="sm"
                          className="w-full justify-start border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>

                        {image.shortLink && (
                          <Button
                            asChild
                            variant="outline"
                            size="sm"
                            className="w-full justify-start border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                          >
                            <a
                              href={imageUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <ExternalLink className="w-4 h-4 mr-2" />
                              Open Original
                            </a>
                          </Button>
                        )}
                      </div>

                      <Separator className="bg-purple-500/20" />

                      {/* Quick Info */}
                      {imageNaturalSize && (
                        <div className="bg-black/20 rounded-lg p-3">
                          <h4 className="text-sm font-medium text-white/90 mb-2">
                            Image Info
                          </h4>
                          <div className="space-y-1 text-sm text-white/70">
                            <div>
                              Dimensions: {imageNaturalSize.width} ×{" "}
                              {imageNaturalSize.height}
                            </div>
                            <div>
                              Aspect Ratio:{" "}
                              {(
                                imageNaturalSize.width / imageNaturalSize.height
                              ).toFixed(2)}
                              :1
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === "details" && (
                    <div className="space-y-4">
                      {/* Image Details */}
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-white/90">
                          File Information
                        </h4>

                        <div className="space-y-3 text-sm">
                          <div className="flex items-center gap-3 text-white/70">
                            <Calendar className="w-4 h-4 flex-shrink-0" />
                            <span>Uploaded {formattedDate}</span>
                          </div>

                          <div className="flex items-center gap-3 text-white/70">
                            <FileImage className="w-4 h-4 flex-shrink-0" />
                            <span>ID: {image.id.slice(0, 8)}...</span>
                          </div>

                          {imageNaturalSize && (
                            <div className="flex items-center gap-3 text-white/70">
                              <Eye className="w-4 h-4 flex-shrink-0" />
                              <span>
                                Resolution: {imageNaturalSize.width} ×{" "}
                                {imageNaturalSize.height}px
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      <Separator className="bg-purple-500/20" />

                      {/* Status */}
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-white/90">
                          Status
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Badge
                            variant="outline"
                            className="border-green-500/30 text-green-400"
                          >
                            <Eye className="w-3 h-3 mr-1" />
                            Public
                          </Badge>
                          <Badge
                            variant="outline"
                            className="border-blue-500/30 text-blue-400"
                          >
                            <Link2 className="w-3 h-3 mr-1" />
                            {image.shortLink ? "Shareable" : "Private"}
                          </Badge>
                        </div>
                      </div>

                      {/* Share URL */}
                      {image.shortLink && (
                        <>
                          <Separator className="bg-purple-500/20" />
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-white/90">
                              Share URL
                            </h4>
                            <div className="bg-black/20 rounded-lg p-3">
                              <code className="text-xs text-purple-400 break-all">
                                {imageUrl}
                              </code>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}