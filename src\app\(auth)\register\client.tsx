"use client";

import Link from "next/link";
import { RegisterForm } from "./_components/register-form";
import { useEffect, useState, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import gsap from "gsap";
import Heading from "@/components/common/Heading";
import Description from "@/components/common/Description";
import Icons from "@/components/common/Icons";
import compareEllips from "../../../../public/assets/images/webp/compare-left-img.webp";

export default function RegisterPageClient({
  clientId,
  redirectUri,
}: {
  clientId: string;
  redirectUri: string;
}) {
  const [banned, setBanned] = useState(false);
  const searchParams = useSearchParams();
  const leftBadgeRef = useRef(null);
  const rightBadgeRef = useRef(null);
  const leftContainerRef = useRef(null);
  const rightContainerRef = useRef(null);

  useEffect(() => {
    const banned = searchParams?.get("banned") === "true";
    if (banned) {
      setBanned(true);
    }
  }, [searchParams]);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  const handleBadgeMouseMove = (e: any, badgeRef: any, containerRef: any) => {
    if (!badgeRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const badge = badgeRef.current;

    const containerRect = container.getBoundingClientRect();
    const centerX = containerRect.left + containerRect.width / 2;
    const centerY = containerRect.top + containerRect.height / 2;

    const x = (e.clientX - centerX) * 0.3;
    const y = (e.clientY - centerY) * 0.3;

    const maxMove = 40;
    const limitedX = Math.max(-maxMove, Math.min(maxMove, x));
    const limitedY = Math.max(-maxMove, Math.min(maxMove, y));

    gsap.to(badge, {
      x: limitedX,
      y: limitedY,
      duration: 0.3,
      ease: "power2.out",
    });
  };

  const handleBadgeMouseLeave = (badgeRef: any) => {
    if (!badgeRef.current) return;

    gsap.to(badgeRef.current, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
    });
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-dark-purple via-custom-black to-light-purple">
      {/* Background Ellipse */}
      <Image
        width={379}
        height={379}
        className="absolute right-0 pointer-events-none top-[-5%] max-lg:hidden z-[0] opacity-60 transform scale-x-[-1]"
        src={compareEllips}
        alt="background ellipse"
      />

      {/* Animated background particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-custom-purple to-custom-pink opacity-20"
            style={{
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 15}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-8">
        <div className="max-w-6xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            {/* Left Side - Branding & Features */}
            <div className="relative">
              {/* Floating Badges */}
              <div
                ref={leftContainerRef}
                className="absolute max-lg:hidden top-[-10%] right-[10%] z-10"
                data-aos="fade-right"
                data-aos-duration={1050}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, leftBadgeRef, leftContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(leftBadgeRef)}
              >
                <div
                  ref={leftBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[226px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-2.5 cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="fastUpload" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute top-[-40%] end-[-6%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Easy Uploads
                  </p>
                </div>
              </div>

              <div
                ref={rightContainerRef}
                className="absolute max-lg:hidden top-[-8%] left-[-5%] z-10"
                data-aos="fade-left"
                data-aos-duration={1300}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, rightBadgeRef, rightContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(rightBadgeRef)}
              >
                <div
                  ref={rightBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[264px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-[9px] cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="effort" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute rotate-[-90deg] top-[-38%] start-[-5%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Secure Storage
                  </p>
                </div>
              </div>

              {/* Main Content */}
              <div data-aos="zoom-in" data-aos-duration={500}>
                <Link href="/" className="flex items-center mb-8 lg:mb-12">
                  <Image
                    src="/assets/images/svg/footer-logo.svg"
                    alt="StreamBliss"
                    width={180}
                    height={40}
                    className="h-10 lg:h-12 w-auto"
                  />
                </Link>

                <Heading
                  variant="6xl"
                  className="!text-left !mb-6 max-w-[500px] !font-bold"
                >
                  Start Your Creative Journey Today
                </Heading>

                <Description className="!text-left max-w-[480px] mb-8 !opacity-80">
                  Join thousands of creators who trust StreamBliss for secure
                  video hosting, easy sharing, and powerful collaboration tools.
                </Description>

                {/* Feature highlights */}
                <div className="space-y-4 max-lg:hidden">
                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={200}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="fastUpload" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Instant video uploads
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={400}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="effort" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Unlimited cloud storage
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={600}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="easySharing" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Advanced privacy controls
                    </span>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Right Side - Register Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full max-w-md mx-auto lg:max-w-none"
            >
              <div className="relative">
                <div className="backdrop-blur-md bg-white/5 rounded-2xl border border-white/10 p-8 shadow-2xl relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  <div className="relative z-10">
                    <div className="mb-8 text-center lg:text-left">
                      <h1 className="text-2xl lg:text-3xl font-bold text-white mb-2">
                        Create Account
                      </h1>
                      <p className="text-white/70">
                        Join the creative community
                      </p>
                    </div>

                    {/* Register Form */}
                    <RegisterForm
                      clientId={clientId}
                      redirectUri={redirectUri}
                      banned={banned}
                    />

                    {/* Additional links */}
                    <div className="mt-8 text-center space-y-3">
                      <p className="text-sm text-white/60">
                        Already have an account?{" "}
                        <Link
                          href="/login"
                          className="text-custom-purple hover:text-custom-pink transition-colors font-medium"
                        >
                          Sign in here
                        </Link>
                      </p>

                      <div className="flex justify-center space-x-6 text-xs text-white/50">
                        <Link
                          href="/privacy"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Privacy
                        </Link>
                        <Link
                          href="/terms"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Terms
                        </Link>
                        <Link
                          href="/help"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Help
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-r from-custom-purple to-custom-pink rounded-full opacity-20 blur-xl" />
                <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-r from-custom-pink to-custom-purple rounded-full opacity-15 blur-xl" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating animations */}
      <style>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          25% {
            transform: translateY(-20px) rotate(90deg);
          }
          50% {
            transform: translateY(-10px) rotate(180deg);
          }
          75% {
            transform: translateY(-30px) rotate(270deg);
          }
        }
      `}</style>
    </div>
  );
}