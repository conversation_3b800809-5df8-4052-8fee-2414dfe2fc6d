"use client";

import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { Video } from "@prisma/client";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Play } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import ApprovalModal from "./approval-modal";
import { useRouter } from "next/navigation";

type Props = {
  videos: Video[];
};

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "";
const API_URL = "https://dev-api.streambliss.cloud";

interface VideoCardProps {
  video: Video;
  onVideoClick: (video: Video) => void;
}

function VideoCard({ video, onVideoClick }: VideoCardProps) {
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const videoSrc = `${API_URL}/videos/${video.id}`;
  const posterSrc = video.thumbnailUrl || `${API_URL}/videos/thumbnail/${video.id}`;

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleLoadedMetadata = () => {
      if (videoElement.videoWidth && videoElement.videoHeight) {
        const aspectRatio = videoElement.videoWidth / videoElement.videoHeight;
        setVideoAspectRatio(aspectRatio);
      }
    };

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);

    // Check immediately if metadata is already loaded
    if (videoElement.videoWidth && videoElement.videoHeight) {
      handleLoadedMetadata();
    }

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, [videoSrc]);

  // Get dynamic aspect ratio class based on video dimensions
  const getAspectRatioClass = () => {
    if (videoAspectRatio === null) {
      return "aspect-video"; // Default fallback
    }

    if (isPortrait) {
      return "aspect-[9/16]"; // Portrait/vertical videos (TikTok, Shorts)
    } else if (videoAspectRatio > 1.5) {
      return "aspect-video"; // Wide landscape videos
    } else {
      return "aspect-square"; // Square-ish videos
    }
  };

  return (
    <Card className="overflow-hidden bg-card border border-border hover:shadow-lg transition-shadow duration-200 rounded-lg flex flex-col h-full">
      <CardContent className="p-0">
        <div
          className={cn(
            "bg-muted cursor-pointer relative group transition-all duration-300",
            getAspectRatioClass()
          )}
          onClick={() => onVideoClick(video)}
        >
          {/* Hidden video element for metadata detection */}
          <video
            ref={videoRef}
            src={videoSrc}
            className="hidden"
            preload="metadata"
            muted
          />

          {video.id && (
            <Image
              src={posterSrc}
              alt={video.title}
              fill
              className="object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
                console.error("Thumbnail failed to load:", posterSrc);
              }}
            />
          )}

          {/* Fallback for failed thumbnail loads */}
          <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center hidden">
            <span className="text-white/40 text-3xl">🎬</span>
          </div>

          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <Play className="w-10 h-10 text-white" />
          </div>

          {/* Aspect ratio indicator for debugging */}
          {videoAspectRatio !== null && (
            <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
              {isPortrait ? "Portrait" : "Landscape"} ({videoAspectRatio.toFixed(2)})
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default function ApprovalGrid({ videos }: Props) {
  const router = useRouter();
  const [initialVideos, setInitialVideos] = useState<Video[]>(videos);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const handleVideoClick = (video: Video) => {
    setSelectedVideo(video);
    setIsVideoModalOpen(true);
  };

  return (
    <>
      <div
        className={cn(
          "grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6",
        )}
      >
        <AnimatePresence mode={"popLayout"}>
          {initialVideos
            .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
            .map((video) => (
              <motion.div
                key={video.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                layout
              >
                <VideoCard video={video} onVideoClick={handleVideoClick} />
              </motion.div>
            ))}
        </AnimatePresence>
      </div>

      {selectedVideo != null && (
        <ApprovalModal
          video={selectedVideo!}
          onClose={(videoId?: string) => {
            setSelectedVideo(null);
            setIsVideoModalOpen(false);

            if (videoId) {
              setInitialVideos((prev) => prev.filter((v) => v.id !== videoId));
            }
          }}
          isOpen={isVideoModalOpen}
        ></ApprovalModal>
      )}
    </>
  );
}