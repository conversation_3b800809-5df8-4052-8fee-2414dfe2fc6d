export interface Video {
  id: string
  title: string
  url: string
  thumbnailUrl: string | null
  shortLink: string
  views: number
  userId: string
  createdAt: Date
  updatedAt: Date
  duration?: number
  description?: string
  musicDisabled?: boolean
  commentsDisabled?: boolean
  isPrivate?: boolean
  showCommunity?: boolean
  approvedForCommunity?: boolean
  user?: {
    id: string
    name?: string
    image?: string
  }
}

export interface ProcessingVideo {
  id: string
  fileName: string
  title: string
  status: "uploading" | "processing" | "muting" | "complete" | "error"
  progress: number
  message: string
  url?: string
  thumbnailUrl?: string | null
  shortLink?: string
  createdAt: Date
  updatedAt: Date
}

export interface VideoUploadResponse {
  success: boolean
  videoId: string
  error?: string
}

export interface VideoUpdateResponse {
  success: boolean
  video: Video
  error?: string
}

export type VideoStatus = "uploading" | "processing" | "muting" | "complete" | "error"