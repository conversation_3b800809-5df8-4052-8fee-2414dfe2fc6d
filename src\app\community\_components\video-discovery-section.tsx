"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { CommunityVideoCard } from "./community-video-card";
import type { Video } from "@/types/video";

interface VideoDiscoverySectionProps {
  videos: Video[];
}

export function VideoDiscoverySection({ videos }: VideoDiscoverySectionProps) {
  const [visibleCount, setVisibleCount] = useState(12);

  const processedVideos = useMemo(() => {
    return videos.map((video, index) => ({
      ...video,
      gridClass: getGridClass(index),
    }));
  }, [videos]);

  const visibleVideos = useMemo(() => {
    return processedVideos.slice(0, visibleCount);
  }, [processedVideos, visibleCount]);

  const handleLoadMore = () => {
    setVisibleCount((prev) => Math.min(prev + 12, videos.length));
  };

  const hasMore = visibleCount < videos.length;

  function getGridClass(index: number): string {
    const position = index % 12;

    if (position === 0 || position === 1) {
      return "col-span-1 md:col-span-2 row-span-2 aspect-[515/353]";
    }

    if (position >= 2 && position <= 4) {
      return "col-span-1 aspect-[339/353]";
    }

    if (position === 5) {
      return "col-span-1 md:col-span-2 aspect-[408/353]";
    }
    if (position === 6) {
      return "col-span-1 md:col-span-3 aspect-[684/353]";
    }

    if (position === 7 || position === 8) {
      return "col-span-1 md:col-span-3 aspect-[642/353]";
    }

    return "col-span-1 aspect-[339/353]";
  }

  return (
    <div className="max-w-[1321px] mx-auto px-4 lg:px-0">
      {/* Section header */}
      <div className="text-center mb-[50px]">
        <div className="max-w-[627px] mx-auto">
          <h2 className="text-white text-[42px] font-semibold font-montserrat leading-[1.3] mb-4">
            Discover Amazing Content
          </h2>
          <p className="text-white/70 text-lg font-normal font-montserrat leading-[1.6]">
            Explore the latest videos from our creative community
          </p>
        </div>
      </div>

      {/* Video grid */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {visibleVideos.slice(0, 2).map((video, index) => (
            <div key={video.id} className="aspect-[515/353] h-[353px]">
              <CommunityVideoCard video={video} />
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {visibleVideos.slice(2, 5).map((video, index) => (
            <div key={video.id} className="aspect-[339/353] h-[353px]">
              <CommunityVideoCard video={video} />
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          {visibleVideos.slice(5, 6).map((video, index) => (
            <div
              key={video.id}
              className="col-span-1 md:col-span-2 aspect-[408/353] h-[353px]"
            >
              <CommunityVideoCard video={video} />
            </div>
          ))}
          {visibleVideos.slice(6, 7).map((video, index) => (
            <div
              key={video.id}
              className="col-span-1 md:col-span-3 aspect-[684/353] h-[353px]"
            >
              <CommunityVideoCard video={video} />
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {visibleVideos.slice(7, 9).map((video, index) => (
            <div key={video.id} className="aspect-[642/353] h-[353px]">
              <CommunityVideoCard video={video} />
            </div>
          ))}
        </div>

        {visibleVideos.length > 9 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {visibleVideos.slice(9).map((video, index) => (
              <div key={video.id} className="aspect-[339/353] h-[353px]">
                <CommunityVideoCard video={video} />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Load More button */}
      {hasMore && (
        <div className="flex justify-center mt-10">
          <Button
            onClick={handleLoadMore}
            className="bg-gradient-to-b from-[#b851e0] to-[#eb489b] hover:opacity-90 text-white font-semibold text-lg font-montserrat rounded-full px-6 py-3.5 min-w-[204px] transition-all duration-300"
          >
            Load More
          </Button>
        </div>
      )}
    </div>
  );
}