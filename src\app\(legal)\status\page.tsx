"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  CheckCircle2,
  Activity,
  Upload,
  Play,
  Code2,
  Shield,
  X,
  AlertTriangle,
  Clock,
  Zap,
  ArrowRight,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import AOS from "aos";
import "aos/dist/aos.css";

const services = [
  {
    name: "Video Upload",
    icon: Upload,
    status: "operational",
    uptime: "99.9%",
    gradient: "from-blue-400 to-cyan-500",
  },
  {
    name: "Video Processing",
    icon: Activity,
    status: "operational",
    uptime: "99.8%",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    name: "Video Streaming",
    icon: Play,
    status: "operational",
    uptime: "99.9%",
    gradient: "from-purple-400 to-pink-500",
  },
  {
    name: "API Services",
    icon: Code2,
    status: "operational",
    uptime: "99.7%",
    gradient: "from-orange-400 to-red-500",
  },
];

const incidents = [
  {
    title: "Scheduled Maintenance - Infrastructure Upgrade",
    date: "2 weeks ago",
    description:
      "Performed system upgrades to improve video processing speed and overall platform performance. No service interruption occurred during the maintenance window.",
    type: "maintenance",
    icon: Shield,
    duration: "2 hours",
    impact: "No impact",
  },
  {
    title: "API Performance Issue Resolved",
    date: "1 month ago",
    description:
      "Resolved increased API latency affecting some users in the EU region. Response times have been restored to normal levels.",
    type: "resolved",
    icon: CheckCircle2,
    duration: "15 minutes",
    impact: "Minor",
  },
  {
    title: "Enhanced Security Measures Deployed",
    date: "2 months ago",
    description:
      "Deployed additional security measures including enhanced DDoS protection and improved authentication systems.",
    type: "improvement",
    icon: Shield,
    duration: "1 hour",
    impact: "No impact",
  },
];

const metrics = [
  {
    label: "Overall Uptime",
    value: "99.9%",
    icon: Zap,
    gradient: "from-green-400 to-emerald-500",
  },
  {
    label: "Average Response Time",
    value: "< 100ms",
    icon: Clock,
    gradient: "from-blue-400 to-indigo-500",
  },
  {
    label: "Active Monitoring",
    value: "24/7",
    icon: Activity,
    gradient: "from-purple-400 to-pink-500",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function StatusPage() {
  const [lastUpdated, setLastUpdated] = useState<Date | null>(new Date());
  const [showSubscribe, setShowSubscribe] = useState(false);
  const [email, setEmail] = useState("");
  const [subscribed, setSubscribed] = useState(false);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    setSubscribed(true);
    setTimeout(() => {
      setShowSubscribe(false);
      setSubscribed(false);
      setEmail("");
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "operational":
        return "text-green-400";
      case "degraded":
        return "text-yellow-400";
      case "down":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  const getIncidentIcon = (type: string) => {
    switch (type) {
      case "maintenance":
        return Shield;
      case "resolved":
        return CheckCircle2;
      case "improvement":
        return Zap;
      default:
        return AlertTriangle;
    }
  };

  const getIncidentColor = (type: string) => {
    switch (type) {
      case "maintenance":
        return "from-blue-500 to-cyan-500";
      case "resolved":
        return "from-green-500 to-emerald-500";
      case "improvement":
        return "from-purple-500 to-pink-500";
      default:
        return "from-yellow-500 to-orange-500";
    }
  };

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <Badge
            variant="outline"
            className="px-6 py-2 border-green-500/30 bg-green-500/10 text-green-400 text-sm font-medium"
          >
            <CheckCircle2 className="h-4 w-4 mr-2" />
            All Systems Operational
          </Badge>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          System Status
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4">
          Real-time status and performance metrics for all StreamBliss services.
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-gray-400">
          <span>
            {lastUpdated
              ? `Last updated: ${lastUpdated.toLocaleTimeString()}`
              : "Loading..."}
          </span>
          <Button
            variant="outline"
            onClick={() => setShowSubscribe(true)}
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10 hover:border-purple-400 transition-all duration-300"
          >
            <Activity className="h-4 w-4 mr-2" />
            Subscribe to Updates
          </Button>
        </div>
      </motion.div>

      {/* Performance Metrics */}
      <motion.div
        className="grid gap-6 md:grid-cols-3"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {metrics.map((metric, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="group relative"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay={index * 100}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 text-center transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
              <div
                className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${metric.gradient} bg-opacity-20 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}
              >
                <metric.icon className="h-7 w-7 text-white" />
              </div>
              <h3 className="text-sm font-medium text-gray-400 mb-1">
                {metric.label}
              </h3>
              <p className="text-2xl font-bold text-white">{metric.value}</p>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Service Status */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Service Status
          </h2>
          <p className="text-lg text-gray-400">
            Current operational status of all StreamBliss services
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div
                      className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${service.gradient} bg-opacity-20`}
                    >
                      <service.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {service.name}
                      </h3>
                      <p className="text-sm text-gray-400">
                        Uptime: {service.uptime}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p
                      className={`text-sm font-medium ${getStatusColor(service.status)}`}
                    >
                      {service.status === "operational"
                        ? "Operational"
                        : "Issues"}
                    </p>
                    <div
                      className={`w-3 h-3 rounded-full ${service.status === "operational" ? "bg-green-400" : "bg-red-400"} mt-1 ml-auto`}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Recent Incidents */}
      <motion.div
        className="space-y-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Recent Activity
          </h2>
          <p className="text-lg text-gray-400">
            Latest incidents, maintenance, and improvements
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          {incidents.map((incident, index) => {
            const IconComponent = getIncidentIcon(incident.type);
            return (
              <motion.div
                key={index}
                className="group relative"
                data-aos="fade-up"
                data-aos-duration="600"
                data-aos-delay={index * 100}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                  <div className="flex items-start gap-4">
                    <div
                      className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${getIncidentColor(incident.type)} bg-opacity-20 flex-shrink-0`}
                    >
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-3">
                        <h3 className="text-lg font-semibold text-white group-hover:text-gray-100 transition-colors">
                          {incident.title}
                        </h3>
                        <div className="flex flex-col sm:text-right text-sm text-gray-400">
                          <span>{incident.date}</span>
                          <span>Duration: {incident.duration}</span>
                        </div>
                      </div>
                      <p className="text-gray-300 leading-relaxed mb-3 group-hover:text-gray-200 transition-colors">
                        {incident.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span
                          className={`px-3 py-1 rounded-full ${
                            incident.impact === "No impact"
                              ? "bg-green-500/20 text-green-400"
                              : "bg-yellow-500/20 text-yellow-400"
                          }`}
                        >
                          {incident.impact} impact
                        </span>
                        <span className="text-gray-500 capitalize">
                          {incident.type}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Subscribe Modal */}
      {showSubscribe && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
          <motion.div
            className="relative w-full max-w-md mx-4"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl" />
            <div className="relative backdrop-blur-xl bg-black/80 border border-white/20 rounded-2xl p-8">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 text-gray-400 hover:text-white"
                onClick={() => setShowSubscribe(false)}
              >
                <X className="h-4 w-4" />
              </Button>

              {subscribed ? (
                <div className="text-center">
                  <CheckCircle2 className="mx-auto h-16 w-16 text-green-400 mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">
                    Subscribed!
                  </h3>
                  <p className="text-gray-300">
                    You&apos;ll now receive updates about our system status.
                  </p>
                </div>
              ) : (
                <>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    Subscribe to Updates
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Get email notifications whenever StreamBliss creates or
                    updates an incident.
                  </p>
                  <form onSubmit={handleSubscribe} className="space-y-4">
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="backdrop-blur-md bg-white/5 border-white/10 focus:border-purple-400 text-white placeholder:text-gray-400"
                    />
                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold"
                    >
                      Subscribe
                    </Button>
                  </form>
                </>
              )}
            </div>
          </motion.div>
        </div>
      )}

      {/* CTA Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Need More Information?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed">
            Visit our help center or contact support for detailed information
            about our services.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button
              asChild
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
            >
              <a href="/help" className="inline-flex items-center">
                Visit Help Center
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </Button>
            <Button
              variant="outline"
              asChild
              className="border-gray-600 text-gray-300 hover:bg-white/10 hover:border-gray-500 transition-all duration-300"
            >
              <a href="/contact" className="inline-flex items-center">
                Contact Support
              </a>
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}