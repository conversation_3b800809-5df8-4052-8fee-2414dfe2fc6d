"use server";

import { stripe } from "@/lib/stripe";

export type Coupon = {
    id: string;
    object: string;
    amount_off: number | null;
    created: number;
    currency: string | null;
    duration: string;
    duration_in_months: number | null;
    livemode: boolean;
    max_redemptions: number | null;
    metadata: object;
    name: string | null;
    percent_off: number | null;
    redeem_by: number | null;
    times_redeemed: number;
    valid: boolean;
};

export type PromotionCode = {
    id: string;
    object: string;
    active: boolean;
    code: string;
    coupon: Coupon;
    created: number;
    customer: string | null;
    expires_at: number | null;
    livemode: boolean;
    max_redemptions: number | null;
    metadata: object;
    restrictions: object;
    times_redeemed: number;
};

export type CreateCouponProps = {
    percent_off?: number;
    amount_off?: number;
    currency?: string;
    duration: "once" | "repeating" | "forever";
    duration_in_months?: number;
    name?: string;
    max_redemptions?: number;
    redeem_by?: number;
    metadata?: Record<string, string>;
    code: string;
};

export async function getCoupons(): Promise<PromotionCode[]> {
    const coupons = await stripe.promotionCodes.list({ limit: 100 });
    return coupons.data as PromotionCode[];
}

export async function doesCouponExist(code: string): Promise<boolean> {
    const promotionCodes = await stripe.promotionCodes.list({
        code,
        limit: 1,
    });
    return promotionCodes.data.length > 0;
}

export async function getCoupon(code: string): Promise<PromotionCode | null> {
    const promotionCodes = await stripe.promotionCodes.list({
        code,
        limit: 1,
        expand: ['data.coupon'],
    });
    return promotionCodes.data[0] as PromotionCode || null;
}

export async function disableCoupon(couponId: string): Promise<PromotionCode> {
    return await stripe.promotionCodes.update(couponId, {active: false}) as unknown as PromotionCode;
}

export async function createCoupon(props: CreateCouponProps): Promise<PromotionCode> {
    const {
        percent_off,
        amount_off,
        currency,
        duration,
        duration_in_months,
        name,
        max_redemptions,
        redeem_by,
        metadata,
        code,
    } = props;

    const coupon = await stripe.coupons.create({
        percent_off,
        amount_off,
        currency,
        duration,
        duration_in_months,
        name,
        max_redemptions,
        redeem_by,
        metadata,
    });

    const promotionCode = await stripe.promotionCodes.create({
        coupon: coupon.id,
        code,
        max_redemptions,
        metadata,
    });

    return promotionCode as PromotionCode;
}
