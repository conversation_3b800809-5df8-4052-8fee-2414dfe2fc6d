import {NextRequest, NextResponse} from "next/server"
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {userId, name, instagram, twitch, twitter, website, youtube} = await request.json()
    if (!userId || !name) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No provided data."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No user session found"});
    }

    const updateRequest = await fetch(process.env.VIDEO_API_URL + "/users/" + userId + "/update", {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.API_SERVER_KEY!,
            "Authorization": "Bearer " + userSession.accessToken
        },
        body: JSON.stringify({
            name,
            instagram,
            twitch,
            twitter,
            website,
            youtube,
        })
    });

    const data = await updateRequest.json();
    if (!updateRequest.ok) {
        return NextResponse.json({status: HttpStatusCode.BadRequest, message: "Failed to update data."});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, data});
}