export const dynamic = "force-dynamic"

import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getUserSession } from "@/server/session"

export async function GET(req: Request) {
  try {
    const session = await getUserSession()
    if (!session?.userId) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    })

    const userPermissions = user?.role.permissions.map((p) => p.permission.name) || []
    const hasAccess = userPermissions.includes("ALL") || userPermissions.includes("TICKET_MANAGEMENT")

    if (!hasAccess) {
      return new NextResponse("Unauthorized", { status: 403 })
    }

    const { searchParams } = new URL(req.url)
    const status = searchParams.get("status")
    const priority = searchParams.get("priority")

    const tickets = await prisma.ticket.findMany({
      where: {
        ...(status && { status: status as any }),
        ...(priority && { priority: priority as any }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        _count: {
          select: {
            responses: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    })

    return NextResponse.json(tickets)
  } catch (error) {
    console.error("[ADMIN_TICKETS_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}