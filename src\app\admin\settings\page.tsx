import { getAdminUser } from "@/server/session";
import SettingsPageClient from "./client";
import { getMaintenance } from "@/server/maintenance";
import { Settings } from "lucide-react";

export default async function SettingsPage() {
  await getAdminUser(["SETTINGS_PAGE"]);
  const maintenance = await getMaintenance();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-800/60">
            <Settings className="h-4 w-4 text-gray-300" />
          </div>
          <h1 className="text-xl font-bold text-white">Platform Settings</h1>
        </div>
        <p className="text-sm text-gray-400">
          Configure platform-wide settings and maintenance
        </p>
      </div>

      {/* Settings Content */}
      <SettingsPageClient maintenance={maintenance} />
    </div>
  );
}