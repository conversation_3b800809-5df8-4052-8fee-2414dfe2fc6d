"use server";

import { prisma } from "@/lib/prisma";
import type { Video } from "@/types/video";

const API_URL = process.env.VIDEO_API_URL || "https://api.streambliss.cloud";

export async function getUserByName(username: string) {
  try {
    const user = await prisma.user.findFirst({
      where: {
        name: username,
      },
      select: {
        id: true,
        name: true,
        website: true,
        twitter: true,
        instagram: true,
        twitch: true,
        youtube: true,
        createdAt: true,
        _count: {
          select: {
            videos: {
              where: {
                showCommunity: true,
                isPrivate: false,
              },
            },
          },
        },
      },
    });

    if (!user) return null;

    return {
      ...user,
      image: `${API_URL}/profile/${user.id}`,
      videosCount: user._count.videos,
    };
  } catch (error) {
    console.error("Error getting user by name:", error);
    return null;
  }
}

export async function getUserVideos(username: string): Promise<Video[]> {
  try {
    const user = await prisma.user.findFirst({
      where: {
        name: username,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!user) return [];

    const videos = await prisma.video.findMany({
      where: {
        userId: user.id,
        showCommunity: true,
        isPrivate: false,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const uniqueVideos = Array.from(
      new Map(videos.map((v) => [v.id, v])).values(),
    );
    const mappedVideos: Video[] = uniqueVideos.map((video) => {
      return {
        ...video,
        thumbnailUrl:
          video.thumbnailUrl ||
          `${API_URL}/video/thumbnail/${user.id}/${video.id}`,
        user: {
          id: user.id,
          name: user.name || "Unknown",
          image: `${API_URL}/profile/${user.id}`,
        },
      };
    });

    return mappedVideos;
  } catch (error) {
    console.error("Error getting user videos:", error);
    return [];
  }
}