"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { stripe } from "@/lib/stripe";
import { getClientIp } from "@/server/geolocation";
import { getUser } from "@/server/session";
import { $Enums } from "@prisma/client";
import <PERSON><PERSON> from "stripe";
import {getCustomerById, getExistingCustomer} from "@/server/subscription";

type BillingData = {
  plan: $Enums.Package;
};

type Callback = {
  success: boolean;
  message: string;
  sessionId?: string;
}

export default async function submitBilling({ plan }: BillingData): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!plan) {
    return { success: false, message: "Invalid plan" };
  }

  const user = await getUser();
  if (!user) {
    return { success: false, message: "User not found" };
  }

  let customer;
  if (!await getExistingCustomer(user.email) && user.stripeAccountId == null) {
    customer = await stripe.customers.create({
      email: user.email,
    });
  } else {
    customer = await getCustomerById(user.stripeAccountId!);
  }

  if (!customer) {
    return { success: false, message: "Failed to create customer" };
  }

  let item;

  if (plan === $Enums.Package.PRO) {
    item = process.env.STRIPE_PRODUCT_ID_PRO;
  } else if (plan === $Enums.Package.CREATOR) {
    item = process.env.STRIPE_PRODUCT_ID_BUSINESS;
  }

  const checkoutSession: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    mode: "subscription",
    submit_type: 'subscribe',
    customer_email: user.email,
    line_items: [
      {
        price: item,
        quantity: 1
      },
    ],
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing/result?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
    allow_promotion_codes: true
  });


  return { success: true, message: "Creating checkout", sessionId: checkoutSession.url! };
}