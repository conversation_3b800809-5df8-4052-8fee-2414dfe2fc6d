"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Bell, Shield } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { $Enums } from "@prisma/client";
import { formatDistance } from "date-fns";
import { useRouter } from "next/navigation";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { motion, AnimatePresence } from "framer-motion";
import submitReadNotification from "../_actions/read-notification";
import submitReadAllNotifications from "../_actions/read-all-notifications";

type Notification = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type: $Enums.NotificationType;
  data: string;
  read: boolean;
};

export function Header({
  notifications: initialNotifications,
  userId,
  hasAccessToAdmin,
}: {
  userId: string;
  notifications: Notification[];
  hasAccessToAdmin?: boolean;
}) {
  const { success, error } = useEnhancedToast();
  const [notifications, setNotifications] =
    useState<Notification[]>(initialNotifications);
  const [isOpen, setIsOpen] = useState(false);
  const [newNotificationCount, setNewNotificationCount] = useState(
    initialNotifications.filter((n) => !n.read).length,
  );
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const [isMarkingAllAsRead, setIsMarkingAllAsRead] = useState(false);
  const notificationSoundRef = useRef<HTMLAudioElement | null>(null);
  const router = useRouter();
  const [audioInitialized, setAudioInitialized] = useState(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const [audioAllowed, setAudioAllowed] = useState(false);

  useEffect(() => {
    console.log("Initializing audio context");
    try {
      audioContextRef.current = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      setAudioInitialized(true);
      console.log("Audio context initialized successfully");
    } catch (error) {
      console.error("Failed to initialize audio context:", error);
    }
  }, []);

  useEffect(() => {
    const allowAudio = () => {
      setAudioAllowed(true);
      window.removeEventListener("click", allowAudio);
      window.removeEventListener("keydown", allowAudio);
    };
    window.addEventListener("click", allowAudio);
    window.addEventListener("keydown", allowAudio);
    return () => {
      window.removeEventListener("click", allowAudio);
      window.removeEventListener("keydown", allowAudio);
    };
  }, []);

  const playNotificationSound = () => {
    console.log("Attempting to play notification sound");
    console.log("Audio initialized:", audioInitialized);
    console.log("Audio context exists:", !!audioContextRef.current);

    if (!audioContextRef.current) {
      console.log("No audio context, creating new one");
      try {
        audioContextRef.current = new (window.AudioContext ||
          (window as any).webkitAudioContext)();
        setAudioInitialized(true);
      } catch (error) {
        console.error("Failed to create audio context:", error);
        return;
      }
    }

    try {
      const context = audioContextRef.current;
      const oscillator = context.createOscillator();
      const gainNode = context.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(context.destination);

      oscillator.type = "sine";
      oscillator.frequency.setValueAtTime(1046.5, context.currentTime);
      gainNode.gain.setValueAtTime(0, context.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.5, context.currentTime + 0.01);
      gainNode.gain.linearRampToValueAtTime(0, context.currentTime + 0.15);

      oscillator.start(context.currentTime);
      oscillator.stop(context.currentTime + 0.15);
      console.log("Notification sound played successfully");
    } catch (error) {
      console.error("Error playing notification sound:", error);
    }
  };

  const handleNotificationRead = async (notificationId: string) => {
    try {
      const response = await submitReadNotification({ notificationId });

      if (!response.success) {
        error("Mark as Read Failed", response.message);
        return;
      }

      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification,
        ),
      );

      setNewNotificationCount((prev) => Math.max(0, prev - 1));
      router.refresh();
    } catch (err) {
      console.error("Error marking notification as read:", err);
      error("Mark as Read Error", "Failed to mark notification as read");
    }
  };

  const markAllAsRead = async () => {
    if (isMarkingAllAsRead) return;

    try {
      setIsMarkingAllAsRead(true);

      const response = await submitReadAllNotifications();
      if (!response.success) {
        error("Mark All Failed", response.message);
        return;
      }

      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true })),
      );
      setNewNotificationCount(0);

      success("All Marked as Read", "All notifications marked as read");
      router.refresh();
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      error("Mark All Failed", "Failed to mark all notifications as read");
    } finally {
      setIsMarkingAllAsRead(false);
    }
  };

  const handleBellClick = () => {
    console.log("Bell clicked, testing sound");
    playNotificationSound();
  };

  return (
    <>
      <header className="relative h-16 px-4 md:px-6 flex items-center justify-end bg-[#110018]/95 border-b border-purple-500/20">
        <div className="flex items-center space-x-2 md:space-x-4">
          {hasAccessToAdmin && (
            <Button
              variant="ghost"
              size="icon"
              className="relative rounded-full h-10 w-10 bg-background/40 hover:bg-primary/10 transition-colors"
              onClick={() => router.push("/admin")}
            >
              <Shield className="h-5 w-5" />
            </Button>
          )}
          <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative rounded-full h-10 w-10 bg-background/40 hover:bg-primary/10 transition-colors"
                onClick={handleBellClick}
              >
                <AnimatePresence>
                  {hasNewNotification && (
                    <motion.div
                      initial={{ scale: 0.5, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 1.5, opacity: 0 }}
                      className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-primary rounded-full"
                    />
                  )}
                </AnimatePresence>
                <Bell className="h-5 w-5" />
                {newNotificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-medium bg-gradient-to-r from-primary to-purple-600 text-white rounded-full">
                    {newNotificationCount > 9 ? "9+" : newNotificationCount}
                  </span>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[350px] border border-primary/20 shadow-xl bg-background/95"
            >
              <div className="flex items-center justify-between px-4 py-2 border-b border-border">
                <DropdownMenuLabel className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
                  Notifications
                </DropdownMenuLabel>
                {notifications.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-muted-foreground hover:text-primary"
                    onClick={markAllAsRead}
                    disabled={isMarkingAllAsRead || newNotificationCount === 0}
                  >
                    Mark all as read
                  </Button>
                )}
              </div>
              <div className="py-2 max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-muted">
                {notifications.length === 0 ? (
                  <div className="px-4 py-6 text-center">
                    <div className="mx-auto mb-3 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bell className="h-5 w-5 text-primary/70" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      No notifications
                    </p>
                  </div>
                ) : (
                  [...notifications]
                    .sort(
                      (a, b) =>
                        new Date(b.createdAt).getTime() -
                        new Date(a.createdAt).getTime(),
                    )
                    .map((notification) => (
                      <DropdownMenuItem
                        key={notification.id}
                        className={`px-4 py-3 cursor-default hover:bg-primary/5 transition-colors ${
                          !notification.read ? "bg-primary/10" : ""
                        }`}
                        onClick={() => handleNotificationRead(notification.id)}
                      >
                        <div className="flex items-start gap-3">
                          <div
                            className={`flex-shrink-0 w-2 h-2 mt-2 rounded-full ${
                              !notification.read
                                ? "bg-primary"
                                : "bg-muted-foreground/30"
                            }`}
                          />
                          <div className="flex-1 min-w-0">
                            <p
                              className={`text-sm ${!notification.read ? "font-medium" : "text-muted-foreground"}`}
                            >
                              {notification.data}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDistance(
                                new Date(notification.createdAt),
                                new Date(),
                                {
                                  addSuffix: true,
                                },
                              )}
                            </p>
                          </div>
                        </div>
                      </DropdownMenuItem>
                    ))
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
    </>
  );
}