"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { hasUserLikedComment, likeComment, unlikeComment } from "@/server/comment";
import { getClientIp } from "@/server/geolocation";

type Callback = {
  success: boolean;
  message: string;
}

type LikeComment = {
  commentId: string;
  userId: string;
}

export async function submitLikeComment({ commentId, userId }: LikeComment): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (commentId == null || userId == null) {
    return { success: false, message: "Invalid request!" };
  }

  const hasLiked = await hasUserLikedComment(userId, commentId);
  if (hasLiked) {
    await unlikeComment(userId, commentId);
  } else {
    await likeComment(userId, commentId);
  }

  return { success: true, message: "Liked video!" };
}
