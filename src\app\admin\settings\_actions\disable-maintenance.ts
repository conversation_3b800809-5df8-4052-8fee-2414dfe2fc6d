"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { clearMaintenanceUpdates, setMaintenance } from "@/server/maintenance";
import { getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

export default async function disableMaintenance(): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_SETTINGS_EDIT"]);
  if (!user) {
    return {
      success: false,
      message: "Unauthorized action",
    };
  }

  await setMaintenance(false);
  await clearMaintenanceUpdates();
  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_MAINTENANCE_UPDATED, LogActions.ACCOUNT, "Disabled Maintenance");

  return {
    success: true,
    message: "Maintenance disabled successfully",
  };
}
