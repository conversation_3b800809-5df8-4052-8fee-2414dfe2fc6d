"use server";

import {stripe} from "@/lib/stripe";
import <PERSON><PERSON> from "stripe";
import {prisma} from "@/lib/prisma";

export async function getCustomersSubscription(customerId: string | null) {
    try {
        if (!customerId) return null;

        const subscription = await stripe.subscriptions.list({
            customer: customerId,
            status: 'active',
            limit: 1,
        });
        if (!subscription) return null;

        return subscription.data[0];
    } catch (error) {
        console.error("Error getting subscription:", error);
        return null;
    }
}

export async function existingCustomerById(customerId: string) {
    try {
        if (!customerId) return false;

        const customer = await stripe.customers.retrieve(customerId);
        if (!customer) return false;

        return customer != null;
    } catch (error) {
        console.error("Error getting customer:", error);
        return false;
    }
}

export async function getCustomerById(customerId: string) {
    try {
        if (!customerId) return false;

        const customer = await stripe.customers.retrieve(customerId);
        if (!customer) return false;

        return customer;
    } catch (error) {
        console.error("Error getting customer:", error);
        return false;
    }
}

export async function getExistingCustomer(email: string) {
    try {
        if (!email) return false;

        const customer = await stripe.customers.list({
            email,
            limit: 1
        });
        if (!customer) return false;

        return customer.data[0] != null;
    } catch (error) {
        console.error("Error getting customer:", error);
        return false;
    }
}

export async function getStripeCustomer(email: string) {
    try {
        if (!email) return null;

        const customer = await stripe.customers.list({
            email,
            limit: 1
        });

        return customer.data[0];
    } catch (error) {
        console.error("Error getting customer:", error);
        return null;
    }
}

export async function createStripeCustomer(email: string) {
    try {
        if (!email) return null;

        const customer = await stripe.customers.create({
            email: email,
        });
        if (!customer) return null;

        return customer;
    } catch (error) {
        console.error("Error creating customer:", error);
        return null;
    }
}

export async function createCustomerSubscription(email: string, subscriptionPlan: "PRO" | "CREATOR") {
    try {
        if (!email) return null;

        let customer: Stripe.Subscription | Stripe.Customer | null;

        if (!await getExistingCustomer(email)) {
            customer = await createStripeCustomer(email);
        } else {
            customer = await getStripeCustomer(email);
        }

        let subscriptionId: string;

        switch (subscriptionPlan.toString()) {
            case "0":
                subscriptionId = process.env.STRIPE_PRODUCT_ID_PRO_FREE as string;
                break;
            case "1":
                subscriptionId = process.env.STRIPE_PRODUCT_ID_BUSINESS_FREE as string;
                break;
            default:
                subscriptionId = process.env.STRIPE_PRODUCT_ID_PRO_FREE as string;
                break;
        }

        if (customer == null) return null;

        const subscription = await getCustomersSubscription(customer.id);
        if (subscription != null) return null;

        await prisma.user.update({
            where: {
                email: email
            },
            data: {
                package: subscriptionPlan.toString() == "0" ? "PRO" : "CREATOR",
                stripeAccountId: customer.id
            }
        });



        return await stripe.subscriptions.create({
            customer: customer.id,
            items: [{price: subscriptionId,}],
        });
    } catch (error) {
        console.error("Error updating customer:", error);
        return null;
    }
}

export async function getCustomerInvoices(customerId: string | null) {
    try {
        if (!customerId) return [];

        const invoices = await stripe.invoices.list({
            customer: customerId,
            limit: 10,
        });

        return invoices.data;
    } catch (error) {
        console.error("Error getting invoices:", error);
        return [];
    }
}

export async function getStripeProductData(productId: string) {
    try {
        if (!productId) return null;

        return stripe.products.retrieve(productId);
    } catch (error) {
        console.error("Error getting product:", error);
        return null;
    }
}

export async function loadUserStripeData(customerId: string | null) {
    if (!customerId) return {subscriptionData: null, invoiceHistory: [], planName: "N/A"};

    if (!await existingCustomerById(customerId)) return {subscriptionData: null, invoiceHistory: [], planName: "N/A"};

    const subscriptionData = await getCustomersSubscription(customerId);
    if (!subscriptionData) return {subscriptionData: null, invoiceHistory: [], planName: "N/A"};

    const invoiceHistory = await getCustomerInvoices(customerId);
    if (!invoiceHistory) return {subscriptionData: null, invoiceHistory: [], planName: "N/A"};

    const productData = await getStripeProductData(subscriptionData?.items.data[0].plan.product as string);
    if (!productData) return {subscriptionData: null, invoiceHistory: [], planName: "N/A"};

    return {subscriptionData, invoiceHistory, planName: productData.name};
}
