import Link from "next/link"
import Footer from "@/components/Footer"
import { FaUserSlash, FaArrowLeft } from "react-icons/fa"
import { CommunityHeader } from "../../_components/community-header"

export default function UserNotFound() {
  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      <CommunityHeader />
      <div className="flex-1 flex flex-col items-center justify-center px-4 text-center" style={{ paddingTop: "calc(4rem + 1px)" }}>
        <div className="relative">
          <div className="absolute -z-10 inset-0 overflow-hidden">
            <div className="absolute -top-24 -right-24 w-96 h-96 bg-purple-500 rounded-full opacity-10 blur-3xl"></div>
            <div className="absolute -bottom-32 -left-32 w-96 h-96 bg-pink-500 rounded-full opacity-10 blur-3xl"></div>
          </div>
          <div className="bg-white/5 border border-white/10 rounded-3xl p-10 shadow-2xl max-w-md">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaUserSlash className="text-white text-3xl" />
            </div>
            <h1 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white via-purple-200 to-white">
              User Not Found
            </h1>  
            <p className="text-xl text-gray-400 mb-8">
              The user profile you&apos;re looking for doesn&apos;t exist or may have been removed.
            </p>
            <Link 
              href="/community"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-full transition-all shadow-lg"
            >
              <FaArrowLeft />
              <span>Back to Community</span>
            </Link>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}