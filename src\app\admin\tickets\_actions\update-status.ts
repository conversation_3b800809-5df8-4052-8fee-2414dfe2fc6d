"use server"

import { prisma } from "@/lib/prisma"
import { getUserSession } from "@/server/session"
import { revalidatePath } from "next/cache"
import { createNotification } from "@/server/notifications"
import {LogActions, NotificationType} from "@prisma/client"
import { getClientIp } from "@/server/geolocation"
import { rateLimiter } from "@/lib/rate-limit"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

export default async function submitUpdateStatus({
  ticketId,
  status,
}: {
  ticketId: string
  status: 'OPEN' | 'IN_PROGRESS' | 'CLOSED' | 'RESOLVED'
}) {
  try {
    const user = await getUserSession()
    const ip = await getClientIp()
    
    await rateLimiter(ip)

    if (!user) {
      throw new Error("Unauthorized")
    }

    const ticket = await prisma.ticket.findUnique({
      where: { id: ticketId },
      include: { user: true }
    })

    if (!ticket) {
      throw new Error("Ticket not found")
    }

    await prisma.ticket.update({
      where: { id: ticketId },
      data: { status }
    })

    await prisma.ticketActivity.create({
      data: {
        ticketId,
        userId: user?.userId,
        action: `Status changed to ${status}`
      }
    })

    await createNotification(
      ticket.userId,
      `Your ticket status has been updated to ${status}`,
      NotificationType.INFO
    )

    revalidatePath('/admin/tickets')
    revalidatePath(`/admin/tickets/${ticketId}`)
    await createLog(user.userId, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_TICKETS_UPDATED, LogActions.TICKET);

    return { success: true, message: "Status updated successfully" }
  } catch (error) {
    return { success: false, message: error.message }
  }
}
