import { Suspense } from "react";
import { UsersTable } from "../_components/users-table";
import { getUsers } from "@/lib/db/admin";
import { getAdminUser } from "@/server/session";
import { getRoles } from "@/server/role";
import { getBanReasons } from "@/server/ban-reasons";
import { Users as UsersIcon } from "lucide-react";

export default async function UsersPage() {
  const user = await getAdminUser(["USER_PAGE"]);
  const users = await getUsers();
  const roles = await getRoles();
  const banReasons = await getBanReasons();
  const permissions = user.role.permissions || [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-800/60">
            <UsersIcon className="h-4 w-4 text-gray-300" />
          </div>
          <h1 className="text-xl font-bold text-white">User Management</h1>
        </div>
        <p className="text-sm text-gray-400">
          Manage and monitor user accounts across the platform
        </p>
      </div>

      {/* Users Table */}
      <Suspense
        fallback={
          <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-8">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2 text-gray-400">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Loading users...</span>
              </div>
            </div>
          </div>
        }
      >
        <UsersTable
          activeUser={user}
          banReasons={banReasons}
          roles={roles}
          initialUsers={users}
          permissions={permissions}
        />
      </Suspense>
    </div>
  );
}