"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { getCommentById } from "@/server/comment";
import { getClientIp } from "@/server/geolocation";
import { getVideoDataById } from "@/server/video";

type Callback = {
  success: boolean;
  message: string;
}

type CommentData = {
  senderId: string;
  videoId: string;
  text: string;
  commentId?: string
};

export async function submitVideoComment({ senderId, videoId, text, commentId }: CommentData): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You are sending requests too fast. Please try again later."
    }
  };

  if (!text) {
    return {
      success: false,
      message: "Comment cannot be empty"
    }
  }

  const video = await getVideoDataById(videoId)
  if (!video) {
    return {
      success: false,
      message: "Video not found"
    }
  }

  const sender = await getUserById(senderId);
  if (!sender) {
    return {
      success: false,
      message: "User not found"
    }
  }

  if (text.length > 500) {
    return {
      success: false,
      message: "Comment is too long"
    }
  }

  let parentId: string | null = null;

  if (commentId != null) {
    const parentComment = await getCommentById(commentId);
    if (!parentComment) {
      return {
        success: false,
        message: "Parent comment not found"
      }
    }

    parentId = parentComment.id;
  }

  await prisma.comments.create({
    data: {
      videoId: videoId,
      userId: sender.id,
      comment: text,
      parentId: parentId
    }
  });

  return {
    success: true,
    message: "Comment has been successfully sent!"
  }
}
