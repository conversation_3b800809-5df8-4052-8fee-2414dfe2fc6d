import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface PriorityBadgeProps {
  priority: string;
  className?: string;
}

export function PriorityBadge({ priority, className }: PriorityBadgeProps) {
  const getCategoryColor = (priority: string) => {
    switch (priority) {
      case "LOW":
        return "bg-gray-600 text-gray-300 border-0";
      case "NORMAL":
        return "bg-blue-600 text-white border-0";
      case "HIGH":
        return "bg-yellow-600 text-white border-0";
      case "URGENT":
        return "bg-red-600 text-white border-0";
      default:
        return "bg-gray-600 text-gray-300 border-0";
    }
  };

  const getCategoryName = (priority: string) => {
    switch (priority) {
      case "LOW":
        return "General Question";
      case "NORMAL":
        return "Technical Support";
      case "HIGH":
        return "Account Issue";
      case "URGENT":
        return "Urgent Problem";
      default:
        return priority;
    }
  };

  return (
    <Badge
      className={cn(
        "rounded-md px-2 py-1 text-xs font-medium",
        getCategoryColor(priority),
        className,
      )}
    >
      {getCategoryName(priority)}
    </Badge>
  );
}