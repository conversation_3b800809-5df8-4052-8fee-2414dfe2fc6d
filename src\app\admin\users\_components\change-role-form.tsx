"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";
import submitMakeAdmin from "../_forms/make-admin";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";

type ChangeRoleFormProps = {
  userId: string | undefined;
  roles: { id: number; name: string }[];
  onComplete: (close: boolean) => void;
};

export default function ChangeRoleForm({ userId, roles, onComplete }: ChangeRoleFormProps) {
  const { success, error } = useEnhancedToast();
  const [role, setRole] = useState<number>(0);

  const submit = async () => {
    if (!userId) return;

    const response = await submitMakeAdmin({ userId, adminId: role });
    if (response.success) {
      success("Role Changed", response.message);
    } else {
      error("Role Change Failed", response.message);
    }

    onComplete(response.success);
  };

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <Select onValueChange={(value) => setRole(parseInt(value))}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose role" />
          </SelectTrigger>
          <SelectContent>
            {
              roles.map(role => {
                return (
                  <SelectItem value={role.id.toString()} key={role.id}>
                    {role.name}
                  </SelectItem>
                )
              })
            }
          </SelectContent>
        </Select>
        <p className="text-sm text-white/70 text-center">
          Here you can select the Role you want to assign to the user.
        </p>
      </div>

      <Button
        type="submit"
        onClick={submit}
        className="w-full bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 text-black font-medium h-10 text-base shadow-lg shadow-teal-500/20"
      >
        Submit
      </Button>
    </div>
  )
}
