import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken ||!userSession.userId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No active user session."});
    }

    try {
        await prisma.userTwoFactor.deleteMany({
            where: { userId: userSession.userId }
        });

        return NextResponse.json({status: HttpStatusCode.Ok, message: "2FA setup cleaned up successfully"});
    } catch (error) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: "Failed to cleanup 2FA setup"});
    }
}