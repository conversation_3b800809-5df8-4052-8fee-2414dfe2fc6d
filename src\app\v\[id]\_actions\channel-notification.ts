"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { disableChannelNotifications, enableChannelNotifications, hasChannelNotificationsEnabled } from "@/server/channel";

type Callback = {
  success: boolean;
  message: string;
  data?: any;
};

type Props = {
  channelId: string;
  enabled: boolean;
};

export async function submitChannelNotification({ channelId, enabled }: Props): Promise<Callback> {
  try {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
      return {
        success: false,
        message: "You are being rate limited. Please try again later.",
      };
    }

    if (!channelId) {
      return {
        success: false,
        message: "Channel ID is required.",
      };
    }

    const userSession = await getUserSession();
    if (!userSession) {
      return {
        success: false,
        message: "You must be logged in to enable notifications.",
      };
    }

    const userId = userSession.userId;
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        message: "User not found.",
      };
    }

    const channel = await getUserById(channelId);
    if (!channel) {
      return {
        success: false,
        message: "Channel not found.",
      };
    }

    if (await hasChannelNotificationsEnabled(userId, channelId)) {
      await disableChannelNotifications(userId, channelId);
      enabled = false;
    } else {
      await enableChannelNotifications(userId, channelId);
      enabled = true;
    }

    return {
      success: true,
      message: enabled ? "Channel notifications have been enabled." : "Channel notifications have been disabled.",
      data: {
        enabled
      },
    }
  } catch (error) {
    console.error("Error in submitChannelNotification:", error);
    return {
      success: false,
      message: "An error occurred while processing your request.",
    };
  }
}
