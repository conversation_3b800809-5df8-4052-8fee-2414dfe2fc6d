name: <PERSON><PERSON><PERSON><PERSON> (dev)

on:
  push:
    branches:
      - dev
  workflow_dispatch:

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install dependencies
        run: npm install --force

      - name: Link shared .env file
        run: ln -s /home/<USER>/Dev/Streambliss/.env .env

      - name: Build project
        run: npm run build

      - name: Start or restart server
        run: |
          pm2 restart dev-streambliss || pm2 start npm --name "dev-streambliss" -- run dev -- -p 8895
