"use client";

import {<PERSON><PERSON>} from "@/components/ui/button";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {useState} from "react";
import submitMakeAdmin from "../_forms/make-admin";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitChangeSubscription from "../_forms/update-subscription";

type UpdateSubscriptionFormProps = {
    userId: string | undefined;
    subscriptions: { name: string }[];
    onComplete: (close: boolean) => void;
};

export default function UpdateSubscriptionForm({userId, subscriptions, onComplete}: UpdateSubscriptionFormProps) {
    const { success, error } = useEnhancedToast();
    const [changedSubscription, setChangeSubscription] = useState<"PRO" | "CREATOR">("PRO");

    const submit = async () => {
        if (!userId) return;

        const response = await submitChangeSubscription({userId, subscription: changedSubscription});
        console.log(response)
        if (response == null) {
            error("Subscription Change Failed", "Failed to change subscription");
            return;
        }

        if (response.success) {
            success("Subscription Changed", response.message);
        } else {
            error("Subscription Change Failed", response.message);
        }

        onComplete(response.success);
    };

    return (
        <div className="space-y-8">
            <div className="space-y-4">
                <Select onValueChange={(value) => setChangeSubscription(value as "PRO" | "CREATOR")}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose role"/>
                    </SelectTrigger>
                    <SelectContent>
                        {
                            subscriptions.map((_subscription, index) => {
                                return (
                                    <SelectItem value={index.toString()} key={index}>
                                        {_subscription.name}
                                    </SelectItem>
                                );
                            })
                        }
                    </SelectContent>
                </Select>
                <p className="text-sm text-white/70 text-center">
                    Here you can select the Role you want to assign to the user.
                </p>
            </div>
            <Button
                type="submit"
                onClick={submit}
                className="w-full bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 text-black font-medium h-10 text-base shadow-lg shadow-teal-500/20"
            >
                Submit
            </Button>
        </div>
    )
}
