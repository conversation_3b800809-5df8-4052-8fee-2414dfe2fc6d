import { getAdminUser } from "@/server/session";
import { getDashboardReports } from "@/server/reports";
import ReportsPageClient from "./client";
import { getCommentReports } from "@/server/comment-reports";

export default async function ReportsPage() {
  const user = await getAdminUser(["REPORTS_VIEW", "REPORTS_EDIT"]);
  const reports = await getDashboardReports();
  const commentReports = await getCommentReports();

  return (
    <ReportsPageClient
      reports={reports}
      commentReports={commentReports}
      currentUser={user}
    />
  );
}