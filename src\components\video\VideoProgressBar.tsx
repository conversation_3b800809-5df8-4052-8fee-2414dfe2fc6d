"use client";

import React, { useState, useRef, useCallback } from "react";

export interface VideoProgressBarProps {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
  className?: string;
}

export const VideoProgressBar: React.FC<VideoProgressBarProps> = ({
  currentTime,
  duration,
  onSeek,
  className = "",
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState<number | null>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const progress = isDragging && dragPosition !== null
    ? dragPosition
    : duration > 0 ? Math.max(0, Math.min(100, (currentTime / duration) * 100)) : 0;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const getTimeFromPosition = useCallback((clientX: number): number => {
    if (!progressBarRef.current) return 0;

    const rect = progressBarRef.current.getBoundingClientRect();
    const position = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    return position * duration;
  }, [duration]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);

    if (progressBarRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      const position = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
      setDragPosition(position);

      const time = getTimeFromPosition(e.clientX);
      onSeek(time);
    }
  }, [getTimeFromPosition, onSeek]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      const time = getTimeFromPosition(e.clientX);
      onSeek(time);
    }
  }, [getTimeFromPosition, onSeek, isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragPosition(null);
  }, []);

  // Global mouse up handler for dragging
  React.useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => {
        setIsDragging(false);
        setDragPosition(null);
      };
      const handleGlobalMouseMove = (e: MouseEvent) => {
        if (progressBarRef.current) {
          const rect = progressBarRef.current.getBoundingClientRect();
          const position = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
          setDragPosition(position);

          const time = getTimeFromPosition(e.clientX);
          onSeek(time);
        }
      };

      document.addEventListener("mouseup", handleGlobalMouseUp);
      document.addEventListener("mousemove", handleGlobalMouseMove);

      return () => {
        document.removeEventListener("mouseup", handleGlobalMouseUp);
        document.removeEventListener("mousemove", handleGlobalMouseMove);
      };
    }
  }, [isDragging, getTimeFromPosition, onSeek]);

  return (
    <div className={`relative ${className}`}>
      {/* Progress Bar Container */}
      <div
        ref={progressBarRef}
        className="relative h-2 bg-white/20 rounded-full cursor-pointer group"
        onMouseDown={handleMouseDown}
        onClick={handleClick}
        onMouseUp={handleMouseUp}
      >
        {/* Background Track */}
        <div className="absolute inset-0 bg-white/20 rounded-full" />

        {/* Progress Fill */}
        <div
          className="absolute top-0 h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
          style={{ width: `${progress}%` }}
        />

        {/* Progress Handle */}
        <div
          className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg ${
            isDragging
              ? "scale-125 opacity-100"
              : "scale-0 opacity-0 group-hover:scale-100 group-hover:opacity-100"
          }`}
          style={{
            left: `calc(${progress}% - 8px)`,
            transition: isDragging ? 'none' : 'transform 0.15s ease, opacity 0.15s ease'
          }}
        />
      </div>
    </div>
  );
};

export default VideoProgressBar;