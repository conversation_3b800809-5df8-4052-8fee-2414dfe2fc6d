"use client";

import React, { useEffect, useRef } from "react";
import gsap from "gsap";

const CustomButton = ({
  children,
  className = "",
  variant = "default",
  disabled = false,
  ...props
}) => {
  const buttonRef = useRef(null);
  const spanRef = useRef(null);

  const variants = {
    default: "border-2 border-white text-white/90 hover:border-white hover:text-white hover:bg-white/5 hover:shadow-lg hover:shadow-white/10 hover:scale-105 transition-all duration-300",
    gradiant: "bg-gradient-to-b from-custom-purple to-custom-pink text-white border-2 border-transparent hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 transition-all duration-300",
  };

  useEffect(() => {
    const button = buttonRef.current;
    const span = spanRef.current;
    if (!button || !span) return;

    const tl = gsap.timeline({ paused: true });
    tl.set(span, { transformOrigin: "center center" });
    tl.to(span, { duration: 0.3, yPercent: -150, ease: "power2.in" })
      .set(span, { yPercent: 150 })
      .to(span, { duration: 0.3, yPercent: 0, ease: "power2.out" });

    const onEnter = () => !disabled && tl.restart();
    const onLeave = () => {
      if (!disabled) {
        tl.pause(0);
        gsap.set(span, { yPercent: 0 });
      }
    };

    button.addEventListener("mouseenter", onEnter);
    button.addEventListener("mouseleave", onLeave);

    return () => {
      button.removeEventListener("mouseenter", onEnter);
      button.removeEventListener("mouseleave", onLeave);
    };
  }, [disabled]);

  return (
    <button
      ref={buttonRef}
      disabled={disabled}
      className={`group py-3.5 px-6 rounded-full text-lg font-semibold leading-160 cursor-pointer max-md:py-2.5 max-md:text-base overflow-hidden relative backdrop-filter-none transform-gpu will-change-transform ${className} ${variants[variant]} ${disabled ? "opacity-50 cursor-not-allowed hover:scale-100 hover:shadow-none" : ""}`}
      {...props}
    >
      {variant === "gradiant" && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out" />
      )}
      <span ref={spanRef} className="block will-change-transform relative z-10">
        {children}
      </span>
    </button>
  );
};

export default CustomButton;