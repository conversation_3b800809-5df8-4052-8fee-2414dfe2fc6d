"use client";

import { formatDistanceToNow } from "date-fns";
import type { Video } from "@/types/video";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { X, Eye, Calendar, User } from "lucide-react";
import Image from "next/image";
import { StreamBlissVideoPlayer } from "@/components/video";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video;
}

const API_URL = process.env.VIDEO_API_URL || "https://api.streambliss.cloud";

export function VideoModal({ isOpen, onClose, video }: VideoModalProps) {
  const [imageError, setImageError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);
  const formattedDate = formatDistanceToNow(new Date(video.createdAt), {
    addSuffix: true,
  });
  const uploader = video.user?.name || "Unknown";
  const uploaderImage = video.user?.image;
  const modalRef = useRef<HTMLDivElement>(null);
  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  const handleVideoMetadataLoaded = (aspectRatio: number) => {
    setVideoAspectRatio(aspectRatio);
  };

  // Get dynamic aspect ratio class based on video dimensions
  const getVideoContainerClass = () => {
    if (videoAspectRatio === null) {
      return "w-full h-full min-h-[400px] md:min-h-[500px]"; // Default fallback
    }

    if (isPortrait) {
      return "w-full h-full min-h-[400px] md:min-h-[500px] max-w-sm mx-auto"; // Portrait/vertical videos
    } else if (videoAspectRatio > 1.5) {
      return "w-full h-full min-h-[400px] md:min-h-[500px]"; // Wide landscape videos
    } else {
      return "w-full h-full min-h-[400px] md:min-h-[500px] max-w-4xl mx-auto"; // Square-ish videos
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 bg-black/95 flex items-center justify-center z-50 p-4">
          <div className="sr-only" aria-live="polite">
            {video.title}
          </div>
          <motion.div
            ref={modalRef}
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", duration: 0.5, bounce: 0.3 }}
            className="relative max-w-6xl w-full bg-slate-900 border border-purple-500/30 overflow-hidden rounded-2xl shadow-2xl shadow-purple-500/20"
          >
            <button
              onClick={onClose}
              className="absolute right-4 top-4 z-50 rounded-full bg-black/90 p-3 text-white hover:bg-black/95 transition-all duration-300 border border-white/20 hover:border-purple-400/50 group"
            >
              <X className="h-5 w-5 group-hover:text-purple-300 transition-colors" />
              <span className="sr-only">Close</span>
            </button>

            <div className="relative w-full h-full overflow-hidden">
              <div className="absolute top-0 left-0 z-10 p-6 bg-gradient-to-r from-black/90 via-black/60 to-transparent w-full pointer-events-none">
                <div className="flex flex-col items-start max-w-2xl">
                  <h2 className="text-white font-bold text-2xl md:text-3xl mb-4 leading-tight">
                    {video.title}
                  </h2>
                  <div className="flex flex-wrap items-center gap-4 text-sm md:text-base">
                    <Link
                      href={`/community/u/${uploader}`}
                      className="flex items-center gap-3 pointer-events-auto hover:opacity-80 transition-all duration-300 bg-black/70 px-4 py-2 rounded-full border border-white/20 hover:border-purple-400/50 group"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClose();
                      }}
                    >
                      {uploaderImage && !imageError ? (
                        <Image
                          src={uploaderImage || "/placeholder.svg"}
                          alt={uploader}
                          width={28}
                          height={28}
                          className="rounded-full object-cover border border-white/30 group-hover:border-purple-400/50 transition-colors"
                          onError={() => setImageError(true)}
                        />
                      ) : (
                        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border border-white/30">
                          <User className="w-3 h-3 text-white" />
                        </div>
                      )}
                      <span className="text-white font-medium group-hover:text-purple-200 transition-colors">
                        {uploader}
                      </span>
                    </Link>

                    <div className="flex items-center gap-2 text-slate-300 bg-black/70 px-4 py-2 rounded-full border border-white/20">
                      <Calendar className="w-4 h-4" />
                      <span>{formattedDate}</span>
                    </div>

                    <div className="flex items-center gap-2 text-slate-300 bg-black/70 px-4 py-2 rounded-full border border-white/20">
                      <Eye className="w-4 h-4" />
                      <span>{formatViews(video.views)} views</span>
                    </div>
                  </div>
                </div>
              </div>

              <StreamBlissVideoPlayer
                src={API_URL + "/video/stream/" + video.userId + "/" + video.id}
                poster={API_URL + "/video/thumbnail/" + video.userId + "/" + video.id}
                title={video.title}
                autoplay={true}
                controls={true}
                className={getVideoContainerClass()}
                onMetadataLoaded={handleVideoMetadataLoaded}
              />
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}