import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {title, content, category, priority} = await request.json();

    if (!title || !category || !priority) {
        return NextResponse.json({
            status: HttpStatusCode.UnprocessableEntity,
            message: "No title, category or priority provided."
        });
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No user session found."});
    }

    const createResponse = await fetch(process.env.VIDEO_API_URL + "/tickets/create", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        },
        body: JSON.stringify({
            title,
            content,
            category,
            priority
        })
    });

    const data = await createResponse.json();
    if (!createResponse.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, data: data});
}