"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";

export async function createLoginHistoryLog(userId: string, ip: string, location: string, success: boolean) {
  const user = await getUserById(userId);
  if (!user) return;

  return await prisma.loginHistory.create({
    data: {
      userId,
      ip,
      email: user.email,
      location: location,
      success: success,
    },
  })
};

export async function getLoginHistoryLogs(userId: string) {
  const user = await getUserById(userId);
  if (!user) return [];

  return await prisma.loginHistory.findMany({
    where: {
      userId: user.id,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
}
