"use client";

import { formatDistanceToNow } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { CheckIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Video } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitApproveVideo from "../_actions/approve-video";
import { StreamBlissVideoPlayer } from "@/components/video";
import submitRejectVideo from "../_actions/reject-video";

type Props = {
  video: Video;
  onClose: (videoId?: string) => void;
  isOpen: boolean;
};

const API_URL = "https://dev-api.streambliss.cloud";

export default function ApprovalModal({ video, onClose, isOpen }: Props) {
  const { success, error } = useEnhancedToast();
  const modalRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<HTMLDivElement>(null);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);

  const formattedDate = video?.createdAt
    ? typeof video.createdAt === "string"
      ? formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })
      : formatDistanceToNow(video.createdAt, { addSuffix: true })
    : "";

  const videoSrc = `${API_URL}/videos/${video.id}`;
  const posterSrc = `${API_URL}/videos/thumbnail/${video.id}`;

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  const handleVideoMetadataLoaded = (aspectRatio: number) => {
    setVideoAspectRatio(aspectRatio);
  };

  // Get modal container class based on video aspect ratio
  const getModalContainerClass = () => {
    if (videoAspectRatio === null) {
      return "max-w-4xl w-full"; // Default fallback
    }

    if (isPortrait) {
      return "max-w-md w-full"; // Portrait videos - narrow modal
    } else if (videoAspectRatio > 1.5) {
      return "max-w-6xl w-full"; // Wide landscape videos - wide modal
    } else {
      return "max-w-3xl w-full"; // Square-ish videos - medium modal
    }
  };

  // Get dynamic aspect ratio class based on video dimensions
  const getVideoContainerClass = () => {
    if (videoAspectRatio === null) {
      return "max-w-4xl max-h-[80vh] min-h-[400px] mx-auto"; // Default fallback - same as dashboard
    }

    if (isPortrait) {
      return "max-w-sm sm:max-w-md max-h-[85vh] aspect-[9/16] mx-auto"; // Portrait/vertical videos - centered
    } else if (videoAspectRatio > 1.5) {
      return "max-w-5xl max-h-[80vh] mx-auto"; // Wide landscape videos - no forced aspect ratio
    } else {
      return "max-w-3xl max-h-[80vh] mx-auto"; // Square-ish videos - no forced aspect ratio, centered
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  const handleVideoAccept = async (videoId: string) => {
    try {
      if (!videoId) {
        error("Accept Failed", "Failed to accept video (no video ID).");
        return;
      }

      const response = await submitApproveVideo({ videoId });
      if (!response || !response.success) {
        error("Accept Failed", response.message);
        return;
      }

      success("Video Accepted", "Video accepted successfully.");

      if (response.data) {
        onClose(response.data.id);
      }
    } catch (error) {
      console.error("Error accepting video", error);
      return;
    }
  };

  const handleVideoReject = async (videoId: string) => {
    try {
      if (!videoId) {
        error("Reject Failed", "Failed to reject video (no video ID).");
        return;
      }

      const response = await submitRejectVideo({ videoId });
      if (!response || !response.success) {
        error("Reject Failed", response.message);
        return;
      }

      success("Video Rejected", "Video rejected successfully.");

      if (response.data) {
        onClose(response.data.id);
      }
    } catch (error) {
      console.error("Error accepting video", error);
      return;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className={cn(
          "relative max-h-[90vh] bg-zinc-900 border border-zinc-800 overflow-hidden rounded-lg shadow-2xl flex flex-col transition-all duration-300",
          getModalContainerClass()
        )}
      >
        <button
          onClick={() => onClose()}
          className="absolute right-4 top-4 z-50 rounded-full bg-black/50 p-2 text-white hover:bg-black/70 transition-colors"
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close</span>
        </button>

        {/* Video Player Container - Adaptive layout (same as dashboard) */}
        <div className="flex-1 flex items-center justify-center p-4">
          <div
            className={cn(
              "w-full h-full bg-black rounded-lg overflow-hidden transition-all duration-300",
              getVideoContainerClass()
            )}
          >
            <div className="relative w-full h-full">
              <div className="absolute top-0 left-0 z-10 p-3 bg-gradient-to-b from-black/80 via-black/40 to-transparent w-full pointer-events-none">
                <div className="flex flex-col items-start">
                  <h2 className="text-white font-medium text-base line-clamp-2">
                    {video.title}
                  </h2>
                  <div className="flex items-center gap-2 mt-1">
                    {formattedDate && (
                      <p className="text-gray-300 text-xs">{formattedDate}</p>
                    )}
                    {videoAspectRatio !== null && (
                      <p className="text-gray-400 text-xs">
                        {isPortrait ? "Portrait" : "Landscape"} ({videoAspectRatio.toFixed(2)})
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <StreamBlissVideoPlayer
                src={videoSrc}
                poster={posterSrc}
                title={video.title}
                autoplay={false}
                controls={true}
                className="w-full h-full"
                onMetadataLoaded={handleVideoMetadataLoaded}
              />
            </div>
          </div>
        </div>
          <div className="border-t border-zinc-800 bg-zinc-900">
            <div className="p-4 flex items-center justify-start gap-3">
              <Button
                size="sm"
                variant="outline"
                className="h-9 bg-green-600/20 hover:bg-green-600/30 border-green-500/50 text-green-400 hover:text-green-300"
                onClick={() => handleVideoAccept(video.id)}
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                Accept
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="h-9 bg-red-600/20 hover:bg-red-600/30 border-red-500/50 text-red-400 hover:text-red-300"
                onClick={() => handleVideoReject(video.id)}
              >
                <X className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}