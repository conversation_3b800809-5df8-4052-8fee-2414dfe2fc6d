"use client";

import { formatDistanceToNow } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { CheckIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Video } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitApproveVideo from "../_actions/approve-video";
import { StreamBlissVideoPlayer } from "@/components/video";
import submitRejectVideo from "../_actions/reject-video";

type Props = {
  video: Video;
  onClose: (videoId?: string) => void;
  isOpen: boolean;
};

const API_URL = "https://dev-api.streambliss.cloud";

export default function ApprovalModal({ video, onClose, isOpen }: Props) {
  const { success, error } = useEnhancedToast();
  const modalRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<HTMLDivElement>(null);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);

  const formattedDate = video?.createdAt
    ? typeof video.createdAt === "string"
      ? formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })
      : formatDistanceToNow(video.createdAt, { addSuffix: true })
    : "";

  const videoSrc = `${API_URL}/videos/${video.id}`;
  const posterSrc = `${API_URL}/videos/thumbnail/${video.id}`;

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  const handleVideoMetadataLoaded = (aspectRatio: number) => {
    setVideoAspectRatio(aspectRatio);
  };

  // Get dynamic aspect ratio class based on video dimensions
  const getVideoContainerClass = () => {
    if (videoAspectRatio === null) {
      return "aspect-video"; // Default fallback
    }

    if (isPortrait) {
      return "aspect-[9/16] max-h-[80vh]"; // Portrait/vertical videos
    } else if (videoAspectRatio > 1.5) {
      return "max-h-[80vh]"; // Wide landscape videos - no forced aspect ratio
    } else {
      return "max-h-[80vh]"; // Square-ish videos - no forced aspect ratio
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  const handleVideoAccept = async (videoId: string) => {
    try {
      if (!videoId) {
        error("Accept Failed", "Failed to accept video (no video ID).");
        return;
      }

      const response = await submitApproveVideo({ videoId });
      if (!response || !response.success) {
        error("Accept Failed", response.message);
        return;
      }

      success("Video Accepted", "Video accepted successfully.");

      if (response.data) {
        onClose(response.data.id);
      }
    } catch (error) {
      console.error("Error accepting video", error);
      return;
    }
  };

  const handleVideoReject = async (videoId: string) => {
    try {
      if (!videoId) {
        error("Reject Failed", "Failed to reject video (no video ID).");
        return;
      }

      const response = await submitRejectVideo({ videoId });
      if (!response || !response.success) {
        error("Reject Failed", response.message);
        return;
      }

      success("Video Rejected", "Video rejected successfully.");

      if (response.data) {
        onClose(response.data.id);
      }
    } catch (error) {
      console.error("Error accepting video", error);
      return;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="relative max-w-6xl w-full max-h-[90vh] bg-zinc-900 border border-zinc-800 overflow-hidden rounded-lg shadow-2xl flex flex-col"
      >
        <button
          onClick={() => onClose()}
          className="absolute right-4 top-4 z-50 rounded-full bg-black/50 p-2 text-white hover:bg-black/70 transition-colors"
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close</span>
        </button>

        <div className="relative w-full flex flex-col h-full">
          <div
            className={cn(
              "w-full bg-black transition-all duration-300",
              getVideoContainerClass()
            )}
          >
            <div className="relative w-full h-full">
              <div className="absolute top-0 left-0 z-10 p-4 bg-gradient-to-r from-black/80 to-transparent w-full pointer-events-none">
                <div className="flex flex-col items-start">
                  <h2 className="text-white font-medium text-lg">
                    {video.title}
                  </h2>
                  {formattedDate && (
                    <p className="text-gray-300 text-sm">{formattedDate}</p>
                  )}
                  {/* Aspect ratio indicator for debugging */}
                  {videoAspectRatio !== null && (
                    <p className="text-gray-400 text-xs mt-1">
                      {isPortrait ? "Portrait" : "Landscape"} ({videoAspectRatio.toFixed(2)})
                    </p>
                  )}
                </div>
              </div>

              <StreamBlissVideoPlayer
                src={videoSrc}
                poster={posterSrc}
                title={video.title}
                autoplay={false}
                controls={true}
                className="w-full h-full"
                onMetadataLoaded={handleVideoMetadataLoaded}
              />
            </div>
          </div>
          <div className="border-b border-zinc-800 bg-zinc-900">
            <div className="container py-2 flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 bg-zinc-800 hover:bg-zinc-700 border-zinc-700 text-white"
                  onClick={() => handleVideoAccept(video.id)}
                >
                  <CheckIcon className="h-4 w-4 mr-1" />
                  Accept
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 bg-zinc-800 hover:bg-zinc-700 border-zinc-700 text-white"
                  onClick={() => handleVideoReject(video.id)}
                >
                  <X className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}