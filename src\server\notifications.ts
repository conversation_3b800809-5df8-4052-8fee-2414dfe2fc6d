"use server";

import { prisma } from "@/lib/prisma";
import { NotificationType } from "@prisma/client";

export const getUserNotifications = async (userId: string) => {
  return await prisma.notifications.findMany({
    where: {
      userId
    }
  });
};

export const createNotification = async (userId: string, message: string, type: NotificationType) => {
  return await prisma.notifications.create({
    data: {
      userId,
      data: message,
      type: type
    }
  });
};

export const getNotificationById = async (notificationId: string) => {
  return await prisma.notifications.findUnique({
    where: {
      id: notificationId
    }
  })
};

export const updateNotification = async (notificationId: string, data: Partial<{ read: boolean; deleted: boolean }>) => {
  return await prisma.notifications.update({
    where: {
      id: notificationId
    },
    data
  });
}

export const updateNotifications = async (userId: string, data: Partial<{ read: boolean; deleted: boolean }>) => {
  return await prisma.notifications.updateMany({
    where: {
      userId: userId
    },
    data
  });
}
