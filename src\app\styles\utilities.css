/* Utility Classes and Helper Styles */

/* Hover and interaction effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
}

.glow-purple:hover {
  box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

.glow-pink:hover {
  box-shadow: 0 10px 30px rgba(236, 72, 153, 0.3);
}

.btn-magnetic {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-magnetic:hover {
  transform: translateY(-1px) scale(1.02);
  filter: brightness(1.1);
}

.card-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Card hover effects for all cards */
.card-hover-effect {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 60px rgba(138, 43, 226, 0.1);
}

/* Magnetic hover effect for buttons */
.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-hover:hover {
  transform: scale(1.05) translateZ(0);
}

/* Smooth page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease-in;
}

/* Global portaled component styles */
[data-radix-dropdown-menu-content],
[data-radix-select-content],
[data-radix-popover-content],
[data-radix-tooltip-content],
[data-radix-context-menu-content],
[data-radix-menubar-content],
[data-radix-navigation-menu-content] {
  background-color: hsl(222 47% 11%) !important;
  color: hsl(210 40% 98%) !important;
  border: 1px solid hsl(262 83% 58% / 0.2) !important;
}

[data-radix-dropdown-menu-item],
[data-radix-select-item],
[data-radix-context-menu-item],
[data-radix-menubar-item] {
  color: hsl(210 40% 98%) !important;
}

[data-radix-dropdown-menu-item]:hover,
[data-radix-dropdown-menu-item][data-highlighted],
[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted],
[data-radix-context-menu-item]:hover,
[data-radix-menubar-item]:hover {
  background-color: hsl(262 83% 58%) !important;
  color: hsl(210 40% 98%) !important;
}

[data-radix-dialog-overlay],
[data-radix-alert-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

[data-radix-dialog-content],
[data-radix-alert-dialog-content] {
  background-color: hsl(222 47% 11%) !important;
  color: hsl(210 40% 98%) !important;
  border: 1px solid hsl(262 83% 58% / 0.2) !important;
}

[data-radix-toast-root] {
  background-color: hsl(222 47% 11%) !important;
  color: hsl(210 40% 98%) !important;
  border: 1px solid hsl(262 83% 58% / 0.2) !important;
}

/* Ensure portaled components use dashboard theme variables */
body:has(.dashboard-theme) [data-radix-dropdown-menu-content],
body:has(.dashboard-theme) [data-radix-select-content],
body:has(.dashboard-theme) [data-radix-popover-content],
body:has(.dashboard-theme) [data-radix-tooltip-content],
body:has(.dashboard-theme) [data-radix-context-menu-content],
body:has(.dashboard-theme) [data-radix-menubar-content],
body:has(.dashboard-theme) [data-radix-navigation-menu-content] {
  --background: 222 47% 11%;
  --foreground: 210 40% 98%;
  --card: 222 47% 11%;
  --card-foreground: 210 40% 98%;
  --popover: 222 47% 11%;
  --popover-foreground: 210 40% 98%;
  --primary: 294 100% 70%;
  --primary-foreground: 0 0% 0%;
  --secondary: 215 25% 27%;
  --secondary-foreground: 210 40% 98%;
  --muted: 215 25% 27%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 262 83% 58%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 262 83% 58% / 0.2;
  --input: 215 25% 27%;
  --ring: 294 100% 70%;
  --radius: 0.75rem;
}
