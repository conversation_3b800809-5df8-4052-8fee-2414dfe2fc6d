"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, Check, X } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import AOS from "aos";
import {useRouter} from "next/navigation";
import "aos/dist/aos.css";
import gsap from "gsap";
import Heading from "@/components/common/Heading";
import Description from "@/components/common/Description";
import Icons from "@/components/common/Icons";
import compareEllips from "../../../../public/assets/images/webp/compare-left-img.webp";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";

export default function PasswordResetClient() {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);
  const leftBadgeRef = useRef(null);
  const rightBadgeRef = useRef(null);
  const leftContainerRef = useRef(null);
  const rightContainerRef = useRef(null);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  const handleBadgeMouseMove = (e: any, badgeRef: any, containerRef: any) => {
    if (!badgeRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const badge = badgeRef.current;

    const containerRect = container.getBoundingClientRect();
    const centerX = containerRect.left + containerRect.width / 2;
    const centerY = containerRect.top + containerRect.height / 2;

    const x = (e.clientX - centerX) * 0.3;
    const y = (e.clientY - centerY) * 0.3;

    const maxMove = 40;
    const limitedX = Math.max(-maxMove, Math.min(maxMove, x));
    const limitedY = Math.max(-maxMove, Math.min(maxMove, y));

    gsap.to(badge, {
      x: limitedX,
      y: limitedY,
      duration: 0.3,
      ease: "power2.out",
    });
  };

  const handleBadgeMouseLeave = (badgeRef: any) => {
    if (!badgeRef.current) return;

    gsap.to(badgeRef.current, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage(null);

    try {
      const request = await fetch("/api/auth/password-reset/request", {
        method: "POST",
        body: JSON.stringify({
          email: email,
        })
      });

      const data = await request.json();
      if (data.status !== 200) {
        error("Reset Failed", data.message);
        return;
      }

      success("Reset Requested", "Password Reset Request has been made");
    } catch (error) {
      setMessage({
        type: "error",
        text: "An error occurred. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-dark-purple via-custom-black to-light-purple">
      {/* Background Ellipse */}
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-events-none top-[-5%] max-lg:hidden z-[0] opacity-60"
        src={compareEllips}
        alt="background ellipse"
      />

      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-custom-purple to-custom-pink opacity-20"
            style={{
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 15}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-8">
        <div className="max-w-6xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            {/* Left Side - Branding & Features */}
            <div className="relative">
              {/* Floating Badges */}
              <div
                ref={leftContainerRef}
                className="absolute max-lg:hidden top-[-10%] right-[10%] z-10"
                data-aos="fade-right"
                data-aos-duration={1050}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, leftBadgeRef, leftContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(leftBadgeRef)}
              >
                <div
                  ref={leftBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[226px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-2.5 cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="secureStorage" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute top-[-40%] end-[-6%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Secure Recovery
                  </p>
                </div>
              </div>

              <div
                ref={rightContainerRef}
                className="absolute max-lg:hidden top-[-8%] left-[-5%] z-10"
                data-aos="fade-left"
                data-aos-duration={1300}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, rightBadgeRef, rightContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(rightBadgeRef)}
              >
                <div
                  ref={rightBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[264px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-[9px] cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="effort" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute rotate-[-90deg] top-[-38%] start-[-5%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Quick Access
                  </p>
                </div>
              </div>

              {/* Main Content */}
              <div data-aos="zoom-in" data-aos-duration={500}>
                <Link href="/" className="flex items-center mb-8 lg:mb-12">
                  <Image
                    src="/assets/images/svg/footer-logo.svg"
                    alt="StreamBliss"
                    width={180}
                    height={40}
                    className="h-10 lg:h-12 w-auto"
                  />
                </Link>

                <Heading
                  variant="6xl"
                  className="!text-left !mb-6 max-w-[500px] !font-bold"
                >
                  Recover Your Account Access
                </Heading>

                <Description className="!text-left max-w-[480px] mb-8 !opacity-80">
                  Don&apos;t worry! It happens to the best of us. Enter your
                  email address and we&apos;ll send you a secure link to reset
                  your password and get back to your creative work.
                </Description>

                {/* Feature highlights */}
                <div className="space-y-4 max-lg:hidden">
                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={200}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="secureStorage" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Secure email verification
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={400}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="effort" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Instant password reset
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={600}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="easySharing" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Back to your dashboard
                    </span>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Right Side - Password Reset Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full max-w-md mx-auto lg:max-w-none"
            >
              <div className="relative">
                <div className="backdrop-blur-md bg-white/5 rounded-2xl border border-white/10 p-8 shadow-2xl relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  <div className="relative z-10">
                    <div className="mb-8 text-center lg:text-left">
                      <div className="flex items-center gap-3 mb-4 justify-center lg:justify-start">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                          <Mail className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h1 className="text-2xl lg:text-3xl font-bold text-white">
                            Reset Password
                          </h1>
                        </div>
                      </div>
                      <p className="text-white/70">
                        Enter your email to receive reset instructions
                      </p>
                    </div>

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="email"
                          className="text-white font-medium"
                        >
                          Email Address
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/50 h-5 w-5" />
                          <Input
                            id="email"
                            type="email"
                            placeholder="Enter your email address"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="pl-12 h-12 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-custom-purple focus:ring-custom-purple/20 transition-all duration-300"
                            required
                          />
                        </div>
                      </div>

                      {/* Message */}
                      {message && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className={`flex items-center gap-3 p-4 rounded-xl ${
                            message.type === "success"
                              ? "bg-green-500/10 border border-green-500/20 text-green-400"
                              : "bg-red-500/10 border border-red-500/20 text-red-400"
                          }`}
                        >
                          {message.type === "success" ? (
                            <Check className="w-5 h-5 flex-shrink-0" />
                          ) : (
                            <X className="w-5 h-5 flex-shrink-0" />
                          )}
                          <span className="text-sm">{message.text}</span>
                        </motion.div>
                      )}

                      {/* Submit Button */}
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full h-12 bg-gradient-to-r from-custom-purple to-custom-pink hover:from-custom-purple/80 hover:to-custom-pink/80 text-white font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                            Sending Reset Link...
                          </>
                        ) : (
                          "Send Reset Link"
                        )}
                      </Button>
                    </form>

                    {/* Additional links */}
                    <div className="mt-8 text-center space-y-3">
                      <p className="text-sm text-white/60">
                        Remember your password?{" "}
                        <Link
                          href="/login"
                          className="text-custom-purple hover:text-custom-pink transition-colors font-medium"
                        >
                          Sign in here
                        </Link>
                      </p>

                      <div className="flex justify-center space-x-6 text-xs text-white/50">
                        <Link
                          href="/privacy"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Privacy
                        </Link>
                        <Link
                          href="/terms"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Terms
                        </Link>
                        <Link
                          href="/help"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Help
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-custom-purple to-custom-pink rounded-full opacity-20 blur-xl" />
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-custom-pink to-custom-purple rounded-full opacity-15 blur-xl" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating animations */}
      <style>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          25% {
            transform: translateY(-20px) rotate(90deg);
          }
          50% {
            transform: translateY(-10px) rotate(180deg);
          }
          75% {
            transform: translateY(-30px) rotate(270deg);
          }
        }
      `}</style>
    </div>
  );
}