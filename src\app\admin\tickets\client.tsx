"use client";

import { useState } from "react";
import { format, formatDistanceToNow } from "date-fns";
import type { TicketStatus, TicketPriority } from "@prisma/client";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Search, Filter, Inbox } from "lucide-react";
import { StatusBadge } from "../tickets/_components/status-badge";
import * as dropdownMenu from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
interface User {
  name: string | null;
  email: string;
}

interface TicketResponse {
  id: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  ticketId: string;
  user: User;
}

interface Ticket {
  id: string;
  title: string;
  content: string;
  status: TicketStatus;
  priority: TicketPriority;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  user: User;
  responses: TicketResponse[];
  _count: {
    responses: number;
  };
}

interface AdminTicketsClientProps {
  openTickets: Ticket[];
  inProgressTickets: Ticket[];
  resolvedTickets: Ticket[];
  closedTickets: Ticket[];
}

const getStatusStyles = (status: string) => {
  switch (status) {
    case "CLOSED":
      return "bg-red-500";
    case "IN_PROGRESS":
      return "bg-blue-500";
    case "RESOLVED":
      return "bg-purple-500";
    default:
      return "bg-green-500";
  }
};

export default function AdminTicketsClient({
  openTickets,
  inProgressTickets,
  resolvedTickets,
  closedTickets,
}: AdminTicketsClientProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  const filteredTickets = [
    ...openTickets,
    ...inProgressTickets,
    ...resolvedTickets,
    ...closedTickets,
  ].filter((ticket) => {
    const matchesSearch = ticket.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter ? ticket.status === statusFilter : true;
    return matchesSearch && matchesStatus;
  });

  const getFilterLabel = () => {
    switch (statusFilter) {
      case "OPEN":
        return "Open";
      case "IN_PROGRESS":
        return "In Progress";
      case "RESOLVED":
        return "Resolved";
      case "CLOSED":
        return "Closed";
      default:
        return "All Tickets";
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="relative w-72">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search tickets..."
                className="pl-10 bg-gray-900/30 border-gray-800/40 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <dropdownMenu.DropdownMenu>
              <dropdownMenu.DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40 hover:text-white"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {getFilterLabel()}
                </Button>
              </dropdownMenu.DropdownMenuTrigger>
              <dropdownMenu.DropdownMenuContent
                align="end"
                className="bg-gray-950 border-gray-800/40"
              >
                <dropdownMenu.DropdownMenuItem
                  onClick={() => setStatusFilter(null)}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  All Tickets
                </dropdownMenu.DropdownMenuItem>
                <dropdownMenu.DropdownMenuSeparator className="bg-gray-800/40" />
                <dropdownMenu.DropdownMenuItem
                  onClick={() => setStatusFilter("OPEN")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  Open
                </dropdownMenu.DropdownMenuItem>
                <dropdownMenu.DropdownMenuItem
                  onClick={() => setStatusFilter("IN_PROGRESS")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  In Progress
                </dropdownMenu.DropdownMenuItem>
                <dropdownMenu.DropdownMenuItem
                  onClick={() => setStatusFilter("RESOLVED")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  Resolved
                </dropdownMenu.DropdownMenuItem>
                <dropdownMenu.DropdownMenuItem
                  onClick={() => setStatusFilter("CLOSED")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  Closed
                </dropdownMenu.DropdownMenuItem>
              </dropdownMenu.DropdownMenuContent>
            </dropdownMenu.DropdownMenu>
          </div>
          <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
            <div className="w-2 h-2 rounded-full bg-purple-400" />
            <span className="text-sm font-medium text-purple-400">
              {filteredTickets.length} tickets found
            </span>
          </div>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl overflow-hidden">
        {/* Desktop Header */}
        <div className="hidden md:grid grid-cols-12 gap-4 bg-gray-900/50 p-4 border-b border-gray-800/40">
          <div className="col-span-5 text-sm font-semibold text-gray-300">
            SUBJECT
          </div>
          <div className="col-span-2 text-center text-sm font-semibold text-gray-300">
            TICKET ID
          </div>
          <div className="col-span-2 text-center text-sm font-semibold text-gray-300">
            STATUS
          </div>
          <div className="col-span-3 text-right text-sm font-semibold text-gray-300">
            LAST ACTION
          </div>
        </div>

        {/* Tickets List */}
        <div className="divide-y divide-gray-800/40">
          {filteredTickets.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-12 text-center">
              <div className="bg-gray-900/50 rounded-full p-4 mb-4">
                <Inbox className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-lg font-medium text-white mb-2">
                No tickets found
              </p>
              <p className="text-sm text-gray-400 max-w-md mb-4">
                {searchQuery || statusFilter
                  ? "Try adjusting your search or filter criteria to find what you're looking for."
                  : "There are currently no active support tickets in the system."}
              </p>
              {(searchQuery || statusFilter) && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setStatusFilter(null);
                  }}
                  className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
                >
                  Clear filters
                </Button>
              )}
            </div>
          ) : (
            filteredTickets.map((ticket) => (
              <Link
                href={`/admin/tickets/${ticket.id}`}
                key={ticket.id}
                className="block hover:bg-gray-900/30 transition-colors"
              >
                <div className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                    <div className="col-span-1 md:col-span-5 space-y-2 min-w-0">
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-2 h-2 rounded-full shrink-0 ${getStatusStyles(ticket.status)}`}
                        />
                        <span className="font-medium text-white truncate">
                          {ticket.title}
                        </span>
                      </div>
                      <div className="flex flex-wrap items-center gap-2 text-sm text-gray-400">
                        <time
                          dateTime={ticket.createdAt.toISOString()}
                          className="shrink-0"
                        >
                          {format(new Date(ticket.createdAt), "MMM dd, HH:mm")}
                        </time>
                        <span className="md:hidden shrink-0">
                          <StatusBadge status={ticket.status} />
                        </span>
                      </div>
                    </div>

                    <div className="hidden md:flex col-span-2 items-center justify-center">
                      <span className="text-sm font-mono bg-gray-800/60 text-gray-300 px-2 py-1 rounded">
                        #{ticket.id.substring(0, 5)}
                      </span>
                    </div>

                    <div className="hidden md:flex col-span-2 items-center justify-center">
                      <StatusBadge status={ticket.status} />
                    </div>

                    <div className="flex md:col-span-3 items-end justify-between md:justify-end gap-4">
                      <span className="md:hidden text-sm font-mono bg-gray-800/60 text-gray-300 px-2 py-1 rounded shrink-0">
                        #{ticket.id.substring(0, 5)}
                      </span>
                      <div className="text-right min-w-0">
                        <div className="text-sm font-medium text-purple-400 truncate">
                          {(ticket.responses?.length ?? 0) > 0
                            ? ticket.responses?.[0]?.user.name ||
                              ticket.responses?.[0]?.user.email
                            : ticket.user.name || ticket.user.email}
                        </div>
                        <div className="text-xs text-gray-400">
                          {(ticket.responses?.length ?? 0) > 0
                            ? formatDistanceToNow(
                                new Date(
                                  ticket.responses?.[0]?.createdAt ?? "",
                                ),
                                { addSuffix: true },
                              )
                            : formatDistanceToNow(new Date(ticket.createdAt), {
                                addSuffix: true,
                              })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </div>

      {/* Footer Stats */}
      {filteredTickets.length > 0 && (
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-400">
            Showing {filteredTickets.length}{" "}
            {filteredTickets.length === 1 ? "ticket" : "tickets"}
          </span>
          <Button
            variant="outline"
            size="sm"
            className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
          >
            Export Tickets
          </Button>
        </div>
      )}
    </div>
  );
}