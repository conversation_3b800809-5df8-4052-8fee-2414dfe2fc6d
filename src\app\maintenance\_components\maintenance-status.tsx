import { Clock } from "lucide-react"

export function MaintenanceStatus({ estimatedCompletion }: { estimatedCompletion: string }) {
  return (
    <div
      className="bg-[#121118] border border-white/5 rounded-xl p-6 max-w-md mx-auto animate-fadeIn"
      style={{ animationDuration: "1.4s", animationDelay: "0.4s" }}
    >
      <div className="flex items-center justify-center space-x-3 mb-3">
        <Clock className="w-5 h-5 text-[#B066FF]" />
        <h3 className="text-lg font-medium text-white">Estimated Completion</h3>
      </div>
      <p className="text-2xl font-bold text-[#B066FF]">{estimatedCompletion}</p>
      <p className="text-white/60 mt-2">Our team is working hard to get everything back online as soon as possible.</p>
    </div>
  )
}