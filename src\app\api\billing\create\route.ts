import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";
import {$Enums} from "@prisma/client";

export async function POST(request: NextRequest) {
    const {plan} = await request.json();
    if (!plan) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No plan has been provided."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No user session has been provided."});
    }

    let item;

    if (plan === $Enums.Package.PRO) {
        item = process.env.STRIPE_PRODUCT_ID_PRO;
    } else if (plan === $Enums.Package.CREATOR) {
        item = process.env.STRIPE_PRODUCT_ID_BUSINESS;
    }

    if (item == null) {
        return NextResponse.json({status: HttpStatusCode.NotFound, message: "No item found."});
    }

    const checkoutResponse = await fetch(process.env.VIDEO_API_URL + "/subscription/checkout", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!,
        },
        body: JSON.stringify({
            priceId: item
        }),
    })

    const data = await checkoutResponse.json();
    if (!checkoutResponse.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message });
    }

    return NextResponse.json({status: HttpStatusCode.Ok, checkoutUrl: data.url});
}