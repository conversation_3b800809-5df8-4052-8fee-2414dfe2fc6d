"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogHeader,
  DialogDescription,
  DialogTitle,
  DialogContent,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import editMaintenance from "../_actions/edit-maintenance";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";

export default function MaintenanceEditDialog({ isOpen, onClose }) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [endTime, setEndTime] = useState<Date | null>(null);

  const handleSave = async () => {
    if (!startTime || !endTime) {
      error("Invalid Range", "Invalid date range");
      return;
    }

    const response = await editMaintenance({
      startDate: startTime,
      endDate: endTime,
    });
    if (response.success) {
      success("Maintenance Updated", response.message);
    } else {
      error("Update Failed", response.message);
    }

    if (response.success) {
      onClose();
      router.refresh();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-950 border-gray-800/40">
        <DialogHeader>
          <DialogTitle className="text-white">
            Maintenance Mode Schedule
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Configure the maintenance window for your platform
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              Start Time
            </label>
            <Input
              onChange={(event) => setStartTime(new Date(event.target.value))}
              type="datetime-local"
              className="bg-gray-900/30 border-gray-800/40 text-white focus:border-purple-400 focus:ring-purple-400/20 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">
              End Time
            </label>
            <Input
              onChange={(event) => setEndTime(new Date(event.target.value))}
              type="datetime-local"
              className="bg-gray-900/30 border-gray-800/40 text-white focus:border-purple-400 focus:ring-purple-400/20 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert"
            />
          </div>
          <div className="bg-amber-500/10 border border-amber-500/30 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <div className="w-4 h-4 rounded-full bg-amber-500/20 flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 rounded-full bg-amber-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-amber-400">
                  Maintenance Mode Warning
                </p>
                <p className="text-xs text-amber-300 mt-1">
                  During maintenance, only admin users will be able to access
                  the platform.
                </p>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-purple-600 hover:bg-purple-700 text-white border-0"
          >
            Enable Maintenance
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}