import ffmpeg from "fluent-ffmpeg"
import ffmpegStatic from "ffmpeg-static"
import ffprobeStatic from "ffprobe-static"
import { join } from "path"
import { prisma } from "@/lib/prisma"
import { unlink } from "fs/promises"
import path from "path"

// Set both ffmpeg and ffprobe paths
const ffmpegPath =
  process.platform === "win32" ? path.join(process.cwd(), "node_modules", "ffmpeg-static", "ffmpeg.exe") : ffmpegStatic

const ffprobePath = ffprobeStatic.path

if (!ffmpegPath) {
  throw new Error("FFmpeg path not found")
}

ffmpeg.setFfmpegPath(ffmpegPath)
ffmpeg.setFfprobePath(ffprobePath)

export type ProcessingStatus = {
  status: "uploading" | "processing" | "complete" | "error"
  progress: number
  message: string
  createdAt: Date
  updatedAt: Date
}

export async function processVideo(
  inputPath: string,
  videoId: string,
  userId: string,
  statusCallback?: (status: ProcessingStatus) => void,
) {
  try {
    const videoDir = path.join(process.cwd(), "storage", "videos")
    const thumbnailDir = path.join(process.cwd(), "storage", "thumbnails")

    const outputFilename = `${videoId}.mp4`
    const thumbnailFilename = `${videoId}.jpg`

    const outputPath = join(videoDir, outputFilename)
    const thumbnailPath = join(thumbnailDir, thumbnailFilename)

    const now = new Date()

    statusCallback?.({
      status: "processing",
      progress: 0,
      message: "Generating thumbnail...",
      createdAt: now,
      updatedAt: now,
    })

    // Generate thumbnail (20% of progress)
    await new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .screenshots({
          timestamps: [1],
          filename: thumbnailFilename,
          folder: thumbnailDir,
        })
        .on("end", resolve)
        .on("error", reject)
    })

    statusCallback?.({
      status: "processing",
      progress: 20,
      message: "Processing video...",
      createdAt: now,
      updatedAt: new Date(),
    })

    // Process video (20-100% of progress)
    await new Promise((resolve, reject) => {
      let lastProgress = 20
      let lastTimestamp = Date.now()

      ffmpeg(inputPath)
        .outputOptions(["-c:v libx264", "-crf 23", "-c:a aac", "-movflags +faststart"])
        .output(outputPath)
        .on("progress", (progress) => {
          // Calculate progress based on frames processed
          const currentTime = Date.now()
          const timeDiff = currentTime - lastTimestamp

          // Update progress every 500ms to avoid too frequent updates
          if (timeDiff > 500) {
            // Increment progress smoothly
            lastProgress = Math.min(95, lastProgress + 5)

            statusCallback?.({
              status: "processing",
              progress: lastProgress,
              message: `Processing video: ${Math.round(lastProgress)}%`,
              createdAt: now,
              updatedAt: new Date(),
            })

            lastTimestamp = currentTime
          }
        })
        .on("end", resolve)
        .on("error", reject)
        .run()
    })

    // Delete the original uploaded file
    await unlink(inputPath).catch(console.error)

    await prisma.video.update({
      where: { id: videoId },
      data: {
        url: `/${userId}/${videoId}`,
        thumbnailUrl: `/${userId}/thumbnail/${videoId}`,
      },
    })

    statusCallback?.({
      status: "complete",
      progress: 100,
      message: "Video processing complete!",
      createdAt: now,
      updatedAt: new Date(),
    })
  } catch (error) {
    console.error("Video processing error:", error)
    statusCallback?.({
      status: "error",
      progress: 0,
      message: "Error processing video",
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    throw error
  }
}