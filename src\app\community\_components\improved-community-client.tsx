"use client";

import { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import { OptimizedVideoGrid } from "./optimized-video-grid";
import { VideoGridSkeleton } from "./video-grid-skeleton";
import { FeaturedVideoHero } from "./featured-video-hero";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  TrendingUp,
  Clock,
  Eye,
  Search,
  Filter,
  Sparkles,
  Calendar,
  PlayCircle,
  Users,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { Video } from "@/types/video";

interface ImprovedCommunityClientProps {
  videos: Video[];
  trendingVideos?: Video[];
  children?: React.ReactNode;
}

type FilterType = "all" | "trending" | "recent" | "popular" | "short" | "long";
type SortType = "algorithm" | "views" | "recent" | "duration";

const FILTER_OPTIONS: Array<{
  key: FilterType;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}> = [
  {
    key: "all",
    label: "For You",
    icon: Sparkles,
    description: "Personalized recommendations",
  },
  {
    key: "trending",
    label: "Trending",
    icon: TrendingUp,
    description: "What's hot right now",
  },
  { key: "recent", label: "Latest", icon: Clock, description: "Fresh uploads" },
  { key: "popular", label: "Popular", icon: Eye, description: "Most viewed" },
  {
    key: "short",
    label: "Quick Watch",
    icon: PlayCircle,
    description: "Under 2 minutes",
  },
  {
    key: "long",
    label: "Deep Dive",
    icon: Calendar,
    description: "5+ minutes",
  },
];

export function ImprovedCommunityClient({
  videos,
  trendingVideos = [],
  children,
}: ImprovedCommunityClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [activeFilter, setActiveFilter] = useState<FilterType>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const featuredVideo = useMemo(() => {
    return videos.length > 0 ? videos[0] : null;
  }, [videos]);

  useEffect(() => {
    if (videos.length > 0) {
      const timer = setTimeout(() => setIsLoading(false), 300);
      return () => clearTimeout(timer);
    }
  }, [videos]);

  const filteredVideos = useMemo(() => {
    let filtered = [...videos];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (video) =>
          video.title.toLowerCase().includes(query) ||
          video.user?.name?.toLowerCase().includes(query),
      );
    }

    switch (activeFilter) {
      case "trending":
        if (trendingVideos.length > 0) {
          filtered = trendingVideos;
        } else {
          const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          filtered = filtered.filter(
            (video) =>
              new Date(video.createdAt) > oneDayAgo && (video.views || 0) > 100,
          );
        }
        break;

      case "recent":
        filtered = filtered.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
        break;

      case "popular":
        filtered = filtered.sort((a, b) => (b.views || 0) - (a.views || 0));
        break;

      case "short":
        filtered = filtered.filter(
          (video) => video.duration && video.duration <= 120,
        );
        break;

      case "long":
        filtered = filtered.filter(
          (video) => video.duration && video.duration >= 300,
        );
        break;

      default:
        break;
    }

    return filtered;
  }, [videos, trendingVideos, activeFilter, searchQuery]);

  useEffect(() => {
    const enableSound = () => {
      setSoundEnabled(true);
      sessionStorage.setItem("soundEnabled", "true");
    };

    if (sessionStorage.getItem("soundEnabled") === "true") {
      setSoundEnabled(true);
    } else {
      const events = ["click", "touchstart", "keydown"];
      events.forEach((event) => {
        document.addEventListener(event, enableSound, { once: true });
      });

      return () => {
        events.forEach((event) => {
          document.removeEventListener(event, enableSound);
        });
      };
    }
  }, []);

  useEffect(() => {
    const filterParam = searchParams.get("filter") as FilterType;
    if (filterParam && FILTER_OPTIONS.find((opt) => opt.key === filterParam)) {
      setActiveFilter(filterParam);
    }
  }, [searchParams]);

  const handleFilterChange = (filter: FilterType) => {
    setActiveFilter(filter);
    const params = new URLSearchParams(searchParams);
    if (filter === "all") {
      params.delete("filter");
    } else {
      params.set("filter", filter);
    }
    router.replace(`/community?${params.toString()}`, { scroll: false });
  };

  const activeFilterOption = FILTER_OPTIONS.find(
    (opt) => opt.key === activeFilter,
  )!;

  return (
    <>
      {/* Header */}
      {children}

      {/* Sound Enable Notification */}
      <AnimatePresence>
        {!soundEnabled && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-4 right-4 z-50 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium"
          >
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
              </svg>
              Click anywhere to enable sound
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Featured video hero section */}
      {featuredVideo && <FeaturedVideoHero video={featuredVideo} />}

      {/* Main content */}
      <main className="relative z-10 bg-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header section */}
          <div className="mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center mb-8"
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-4 font-montserrat">
                <span className="bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                  Discover Amazing Content
                </span>
              </h1>
              <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
                Explore the latest videos from our creative community, powered
                by smart recommendations
              </p>
            </motion.div>

            {/* Search and filters */}
            <div className="max-w-4xl mx-auto space-y-4">
              {/* Search bar */}
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-zinc-400" />
                <Input
                  type="text"
                  placeholder="Search videos, creators..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-3 bg-zinc-900/50 border-zinc-700 focus:border-purple-500 rounded-xl text-white placeholder:text-zinc-400"
                />
              </div>

              {/* Filter tabs */}
              <div className="flex flex-wrap gap-2 justify-center">
                {FILTER_OPTIONS.map((option) => {
                  const Icon = option.icon;
                  const isActive = activeFilter === option.key;

                  return (
                    <Button
                      key={option.key}
                      variant={isActive ? "default" : "ghost"}
                      size="sm"
                      onClick={() => handleFilterChange(option.key)}
                      className={cn(
                        "rounded-full transition-all duration-200 flex items-center gap-2",
                        isActive
                          ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg hover:shadow-purple-500/25"
                          : "text-zinc-400 hover:text-white hover:bg-zinc-800",
                      )}
                    >
                      <Icon className="w-4 h-4" />
                      {option.label}
                      {option.key === "trending" &&
                        trendingVideos.length > 0 && (
                          <Badge
                            variant="secondary"
                            className="ml-1 text-xs bg-orange-500/20 text-orange-400 border-orange-500/30"
                          >
                            {trendingVideos.length}
                          </Badge>
                        )}
                    </Button>
                  );
                })}
              </div>

              {/* Active filter description */}
              <motion.div
                key={activeFilter}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center"
              >
                <p className="text-sm text-zinc-500">
                  {activeFilterOption.description} • {filteredVideos.length}{" "}
                  videos
                </p>
              </motion.div>
            </div>
          </div>

          {/* Video grid */}
          <motion.div
            key={`${activeFilter}-${searchQuery}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {isLoading ? (
              <VideoGridSkeleton count={8} />
            ) : (
              <OptimizedVideoGrid
                videos={filteredVideos}
                soundEnabled={soundEnabled}
              />
            )}
          </motion.div>

          {/* No results state */}
          {filteredVideos.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16"
            >
              <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center mb-6">
                <Search className="w-12 h-12 text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                No videos found
              </h3>
              <p className="text-zinc-400 mb-6">
                {searchQuery
                  ? `No results for "${searchQuery}"`
                  : `No ${activeFilterOption.label.toLowerCase()} videos available`}
              </p>
              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                >
                  Clear search
                </Button>
              )}
            </motion.div>
          )}
        </div>
      </main>
    </>
  );
}