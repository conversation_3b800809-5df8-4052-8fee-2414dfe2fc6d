"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { Bo<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  type: "message" | "status" | "assign" | "bot";
  content: string;
  createdAt: Date;
  user: {
    id: string;
    name?: string | null;
    email: string;
    image?: string | null;
    isBot?: boolean;
  };
  icon?: string;
}

interface TicketChatTimelineProps {
  messages: Message[];
  currentUserId?: string;
  renderBubbleClass?: (message: Message) => string | undefined;
  renderContainerClass?: (message: Message) => string | undefined;
}

export function TicketChatTimeline({
  messages,
  currentUserId,
  renderBubbleClass,
  renderContainerClass,
}: TicketChatTimelineProps) {
  if (!messages.length) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        No messages in this conversation yet.
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      {messages.map((message) => {
        const isCurrentUser = message.user.id === currentUserId;
        const userName = message.user.name || message.user.email.split("@")[0];
        const displayName = message.user.isBot ? "Streambliss Bot" : userName;
        const isBot = message.type === "bot" || message.user.isBot;

        // System messages
        if (message.type === "status" || message.type === "assign") {
          return (
            <div key={message.id} className="flex items-center justify-center">
              <span className="px-3 py-1.5 text-xs bg-purple-500/10 text-purple-400 rounded-full border-0">
                {message.content}
              </span>
            </div>
          );
        }

        const containerClass = renderContainerClass
          ? renderContainerClass(message)
          : isCurrentUser
            ? "flex flex-row-reverse items-start gap-3"
            : "flex items-start gap-3";

        const bubbleClass = renderBubbleClass
          ? renderBubbleClass(message)
          : isBot
            ? "bg-purple-500/10 text-white border-0"
            : isCurrentUser
              ? "bg-purple-500/10 text-white border-0"
              : "bg-gray-800/50 text-white border-0";

        return (
          <div
            key={message.id}
            className={cn("group relative", containerClass)}
          >
            <Avatar className="h-8 w-8 mt-1 ring-1 ring-gray-700">
              {isBot ? (
                <div className="h-full w-full bg-purple-500/20 flex items-center justify-center">
                  <Bot className="h-4 w-4 text-purple-400" />
                </div>
              ) : message.user.image ? (
                <AvatarImage src={message.user.image} alt={displayName} />
              ) : (
                <AvatarFallback className="bg-purple-500/20 text-purple-400">
                  {displayName[0]?.toUpperCase()}
                </AvatarFallback>
              )}
            </Avatar>

            <div
              className={cn(
                "max-w-[85%] space-y-1.5",
                isCurrentUser ? "items-end" : "items-start",
              )}
            >
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-gray-300">
                  {displayName}
                </span>
                <span className="text-[10px] text-gray-500">
                  {formatDistanceToNow(new Date(message.createdAt), {
                    addSuffix: true,
                  })}
                </span>
              </div>

              <div
                className={cn(
                  "p-3 rounded-xl text-sm break-words relative border-0",
                  bubbleClass,
                  isCurrentUser ? "rounded-tr-sm" : "rounded-tl-sm",
                )}
              >
                {message.content}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}