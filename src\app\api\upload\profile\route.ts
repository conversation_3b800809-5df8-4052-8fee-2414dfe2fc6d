import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { updateUserProfile } from "@/lib/db/user"
import { getUser } from "src/server/session"

export async function POST(req: Request) {
  try {
    const user = await getUser();
    if (!user || !user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const formData = await req.formData()
    const file = formData.get("file") as File
    if (!file) {
      return new NextResponse("No file provided", { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return new NextResponse("Invalid file type", { status: 400 })
    }

    // Convert File to Buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Save and process the image
    const imageUrl = ""

    // Update user profile with new image URL
    await updateUserProfile(user.id, {
      image: imageUrl,
    })

    return NextResponse.json({ url: imageUrl })
  } catch (error) {
    console.error("[PROFILE_UPLOAD_ERROR]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}