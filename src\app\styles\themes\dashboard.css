/* Dashboard Theme Styles */

.dashboard-theme {
  --background: 222 47% 11%;
  --foreground: 210 40% 98%;
  --card: 222 47% 11%;
  --card-foreground: 210 40% 98%;
  --popover: 222 47% 11%;
  --popover-foreground: 210 40% 98%;
  --primary: 294 100% 70%;
  --primary-foreground: 0 0% 0%;
  --secondary: 215 25% 27%;
  --secondary-foreground: 210 40% 98%;
  --muted: 215 25% 27%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 262 83% 58%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 262 83% 58% / 0.2;
  --input: 215 25% 27%;
  --ring: 294 100% 70%;
  --radius: 0.75rem;

  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: hsl(var(--foreground));
  font-family: "Montserrat", sans-serif;
}

.dashboard-theme .solid-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dashboard-theme .gradient-border {
  position: relative;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dashboard-theme .gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #b851e0, #eb489b);
  border-radius: inherit;
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

.dashboard-theme * {
  border-color: hsl(var(--border));
}

.dashboard-theme [data-radix-popper-content-wrapper],
.dashboard-theme [data-radix-popper-content-wrapper] > div {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [role="dialog"],
.dashboard-theme [data-radix-dialog-content] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-state="open"][data-side],
.dashboard-theme [data-radix-dropdown-menu-content],
.dashboard-theme [data-radix-select-content],
.dashboard-theme [data-radix-popover-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.dashboard-theme [data-radix-dropdown-menu-item],
.dashboard-theme [data-radix-select-item],
.dashboard-theme [role="menuitem"] {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [data-radix-dropdown-menu-item]:hover,
.dashboard-theme [data-radix-select-item]:hover,
.dashboard-theme [role="menuitem"]:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme .bg-white {
  background-color: hsl(var(--card)) !important;
}

.dashboard-theme .text-black {
  color: hsl(var(--card-foreground)) !important;
}

.dashboard-theme .border-gray-200,
.dashboard-theme .border-gray-300,
.dashboard-theme .border-slate-200 {
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme .text-gray-900,
.dashboard-theme .text-slate-900 {
  color: hsl(var(--foreground)) !important;
}

.dashboard-theme .text-gray-600,
.dashboard-theme .text-slate-600 {
  color: hsl(var(--muted-foreground)) !important;
}

.dashboard-theme .bg-gray-50,
.dashboard-theme .bg-slate-50 {
  background-color: hsl(var(--muted)) !important;
}

.dashboard-theme input,
.dashboard-theme textarea,
.dashboard-theme select {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme input::placeholder,
.dashboard-theme textarea::placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

/* Button overrides */
.dashboard-theme button:not(.bg-primary):not([class*="bg-"]) {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme button:not(.bg-primary):not([class*="bg-"]):hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme .gradient-text {
  background: linear-gradient(to right, hsl(var(--primary)), #ec4899, #a855f7);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Password toggle button styles */
div.password-toggle-btn,
div.password-toggle-btn *,
div.password-toggle-btn svg,
div.password-toggle-btn svg *,
div.password-toggle-btn svg path,
div.password-toggle-btn svg circle,
div.password-toggle-btn svg line {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  backdrop-filter: none !important;
  filter: none !important;
}

div.password-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

div.password-toggle-btn:hover *,
div.password-toggle-btn:hover svg,
div.password-toggle-btn:hover svg *,
div.password-toggle-btn:hover svg path,
div.password-toggle-btn:hover svg circle,
div.password-toggle-btn:hover svg line {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

[data-theme] div.password-toggle-btn,
[data-theme] div.password-toggle-btn *,
[data-theme] div.password-toggle-btn svg,
[data-theme] div.password-toggle-btn svg * {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

.dashboard-theme .bg-grid-white {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.04)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-primary\/20::-webkit-scrollbar-thumb {
  background-color: rgba(168, 85, 247, 0.2);
}

/* Smooth scrolling for dashboard */
.dashboard-theme {
  scroll-behavior: smooth;
}

.dashboard-theme .scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.dashboard-theme .scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Enhanced shadcn/ui component styling for dashboard */
.dashboard-theme [data-radix-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.dashboard-theme [data-radix-dialog-content] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme .bg-background {
  background-color: hsl(var(--background)) !important;
}

.dashboard-theme .bg-card {
  background-color: hsl(var(--card)) !important;
}

.dashboard-theme .text-card-foreground {
  color: hsl(var(--card-foreground)) !important;
}

.dashboard-theme .text-foreground {
  color: hsl(var(--foreground)) !important;
}

.dashboard-theme .text-muted-foreground {
  color: hsl(var(--muted-foreground)) !important;
}

.dashboard-theme .bg-muted {
  background-color: hsl(var(--muted)) !important;
}

.dashboard-theme .bg-accent {
  background-color: hsl(var(--accent)) !important;
}

.dashboard-theme .text-accent-foreground {
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme .bg-primary {
  background-color: hsl(var(--primary)) !important;
}

.dashboard-theme .text-primary-foreground {
  color: hsl(var(--primary-foreground)) !important;
}

.dashboard-theme .bg-secondary {
  background-color: hsl(var(--secondary)) !important;
}

.dashboard-theme .text-secondary-foreground {
  color: hsl(var(--secondary-foreground)) !important;
}

.dashboard-theme .border {
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme .ring-ring {
  --tw-ring-color: hsl(var(--ring)) !important;
}

.dashboard-theme .ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background)) !important;
}

.dashboard-theme [data-radix-dialog-overlay],
.dashboard-theme [data-state="open"][data-side] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.dashboard-theme [data-radix-dropdown-menu-content],
.dashboard-theme [data-radix-select-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-tooltip-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-toast-root] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme .bg-input {
  background-color: hsl(var(--input)) !important;
}

.dashboard-theme .bg-destructive {
  background-color: hsl(var(--destructive)) !important;
}

.dashboard-theme .text-destructive-foreground {
  color: hsl(var(--destructive-foreground)) !important;
}

.dashboard-theme .text-destructive {
  color: hsl(var(--destructive)) !important;
}

.dashboard-theme [role="dialog"],
.dashboard-theme [role="alertdialog"],
.dashboard-theme [data-radix-dialog-content],
.dashboard-theme [data-radix-alert-dialog-content] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-popper-content-wrapper] > div,
.dashboard-theme [data-radix-popper-content-wrapper] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme .bg-popover {
  background-color: hsl(var(--popover)) !important;
}

.dashboard-theme .text-popover-foreground {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme .focus\:bg-accent:focus {
  background-color: hsl(var(--accent)) !important;
}

.dashboard-theme .focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme .data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent)) !important;
}

.dashboard-theme [data-radix-select-trigger] {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-select-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-select-item] {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [data-radix-select-item]:hover,
.dashboard-theme [data-radix-select-item][data-highlighted] {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Command palette styling */
.dashboard-theme [cmdk-root] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [cmdk-input] {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dashboard-theme [cmdk-item] {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [cmdk-item]:hover,
.dashboard-theme [cmdk-item][data-selected] {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Tabs styling */
.dashboard-theme [data-radix-tabs-trigger] {
  color: hsl(var(--muted-foreground)) !important;
}

.dashboard-theme [data-radix-tabs-trigger][data-state="active"] {
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

.dashboard-theme [data-radix-tabs-content] {
  color: hsl(var(--foreground)) !important;
}

/* Accordion styling */
.dashboard-theme [data-radix-accordion-trigger] {
  color: hsl(var(--foreground)) !important;
}

.dashboard-theme [data-radix-accordion-content] {
  color: hsl(var(--foreground)) !important;
}

/* Alert dialog styling */
.dashboard-theme [data-radix-alert-dialog-overlay] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.dashboard-theme [data-radix-alert-dialog-content] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-dialog-overlay][data-state="open"] {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.dashboard-theme [data-radix-popover-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

/* Context menu styling */
.dashboard-theme [data-radix-context-menu-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-context-menu-item] {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [data-radix-context-menu-item]:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Hover card styling */
.dashboard-theme [data-radix-hover-card-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

/* Menubar styling */
.dashboard-theme [data-radix-menubar-trigger] {
  color: hsl(var(--foreground)) !important;
}

.dashboard-theme [data-radix-menubar-trigger][data-state="open"] {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme [data-radix-menubar-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dashboard-theme [data-radix-menubar-item] {
  color: hsl(var(--popover-foreground)) !important;
}

.dashboard-theme [data-radix-menubar-item]:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Navigation Menu styling */
.dashboard-theme [data-radix-navigation-menu-trigger] {
  color: hsl(var(--foreground)) !important;
}

.dashboard-theme [data-radix-navigation-menu-content] {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

/* Separator styling */
.dashboard-theme .bg-border {
  background-color: hsl(var(--border)) !important;
}

/* Additional utility class overrides */
.dashboard-theme .hover\:bg-accent:hover {
  background-color: hsl(var(--accent)) !important;
}

.dashboard-theme .hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground)) !important;
}

.dashboard-theme .data-\[highlighted\]\:bg-accent[data-highlighted] {
  background-color: hsl(var(--accent)) !important;
}

.dashboard-theme
  .data-\[highlighted\]\:text-accent-foreground[data-highlighted] {
  color: hsl(var(--accent-foreground)) !important;
}
