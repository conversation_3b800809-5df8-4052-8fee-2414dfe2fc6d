type GoogleAccountData = {
  sub: string;
  name: string;
  given_name: string;
  picture: string;
  email: string;
  email_verified: boolean;
}

export const getProfileData = async (access_token: string, token_type: string): Promise<GoogleAccountData | null> => {
  if (!access_token || !token_type) return null;

  const response = await fetch("https://www.googleapis.com/oauth2/v3/userinfo", {
    headers: {
      Authorization: `${token_type} ${access_token}`,
    },
  });

  if (!response.ok) return null;
  const data = await response.json();

  return data;
}