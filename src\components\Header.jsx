"use client";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import Icons from "./common/Icons";
import CustomButton from "./common/CustomButton";
import { NAV_DATA_LIST } from "../../utils/helper";
import logo from "../../public/assets/images/webp/logo.webp";

const Header = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleLinkClick = (e, link) => {
    if (link.startsWith("#")) {
      e.preventDefault();
      const targetId = link.substring(1);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: "smooth" });
      }
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  return (
    <div id="navbar" className="z-40  border-b border-custom-light-gray">
      <div className="container max-w-[1080px] relati ve z-[3] mx-auto items-center flex justify-between max-xl:px-4 md:py-[21px] py-3">
        <Link href="/" className="relative !z-[2]">
          <Image
            src={logo}
            alt="logo"
            className="max-sm:max-w-[170px] max-md:max-w-[200px] relative z-[2] h-auto max-w-[260px] lg:h-[50px] pointer-events-none"
            width={260}
            height={50}
            quality={100}
            priority
          />
        </Link>
        <ul className="flex gap-7 max-lg:hidden">
          {NAV_DATA_LIST.map((item, i) => (
            <li key={i}>
              <Link
                aria-label="nav links"
                href={item.link}
                onClick={(e) => handleLinkClick(e, item.link)}
                className={`relative font-normal text-base leading-160 transition-all duration-700 text-white
    after:content-[''] after:absolute after:left-0 after:bottom-[-8%] after:h-[1.5px] after:bg-white
    after:w-0 after:transition-all after:duration-500 hover:after:w-full
    ${pathname === item.link ? "text-stroke font-semibold" : ""}`}
              >
                {item.title}
              </Link>
            </li>
          ))}
        </ul>
        <div className="flex items-center gap-x-3">
          <Link href="/login" className="max-lg:hidden">
            <CustomButton
              variant="default"
              className="xl:!py-[11px] xl:!px-8 text-sm"
            >
              Login
            </CustomButton>
          </Link>
          <Link href="/register">
            <CustomButton
              variant="gradiant"
              className="max-lg:hidden xl:!py-[11px] xl:!px-8 text-sm"
            >
              Sign Up
            </CustomButton>
          </Link>
        </div>
        <div className="lg:hidden z-50 cursor-pointer">
          <button
            onClick={toggleMenu}
            className={`lg:hidden relative z-50 cursor-pointer hover:scale-[1.05] duration-300 py-2 flex justify-end items-center rounded-lg w-[46px] h-[45px] `}
          >
            {isOpen ? (
              <Icons
                icon="close"
                stroke={pathname === "/" ? "#FFFFFF" : "#FFFFFF"}
              />
            ) : (
              <Icons
                icon="menu"
                fill={pathname === "/" ? "#FFFFFF" : "#FFFFFF"}
              />
            )}
          </button>
        </div>
      </div>
      <div
        className={`w-full h-[100vh] bg-dark-purple transition-all duration-500 left-0 lg:-top-full z-40 fixed flex gap-6 flex-col justify-center items-center ${
          isOpen ? "top-0 left-0" : "-top-full"
        }`}
      >
        {NAV_DATA_LIST.map((item, i) => (
          <Link
            key={i}
            onClick={(e) => {
              handleLinkClick(e, item.link);
              setIsOpen(false);
            }}
            href={item.link}
            className={`relative font-normal text-base leading-160 transition-all duration-700 text-white
    after:content-[''] after:absolute after:left-0 after:bottom-[-8%] after:h-[1.5px] after:bg-white
    after:w-0 after:transition-all after:duration-500 hover:after:w-full${
      pathname === item.link ? "text-white font-semibold" : "text-white"
    }`}
          >
            {item.title}
          </Link>
        ))}
        <div className="flex flex-col gap-3 mt-4">
          <Link href="/login">
            <CustomButton
              variant="default"
              className="w-full"
              onClick={() => setIsOpen(false)}
            >
              Login
            </CustomButton>
          </Link>
          <Link href="/register">
            <CustomButton
              variant="gradiant"
              className="w-full"
              onClick={() => setIsOpen(false)}
            >
              Sign Up
            </CustomButton>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Header;