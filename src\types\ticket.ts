export type TicketStatus = "OPEN" | "IN_PROGRESS" | "CLOSED" | "RESOLVED"
export type TicketPriority = "LOW" | "NORMAL" | "HIGH" | "URGENT"

export interface Ticket {
  id: string
  title: string
  content: string
  status: TicketStatus
  priority: TicketPriority
  userId: string
  assignedToId?: string | null
  createdAt: Date
  updatedAt: Date
  user?: {
    id: string
    name?: string | null
    email: string
    image?: string | null
    roleId?: number
  }
  assignedTo?: {
    id: string
    name?: string | null
    email: string
    image?: string | null
    roleId?: number
  } | null
  activities?: TicketActivity[]
  responses?: TicketResponse[]
}

export interface TicketResponse {
  id: string
  content: string
  ticketId: string
  userId: string
  createdAt: Date
  updatedAt: Date
  user?: {
    id: string
    name?: string | null
    email: string
    image?: string | null
  }
}

export interface TicketActivity {
  id: string
  ticketId: string
  userId: string
  action: string
  createdAt: Date
  user?: {
    id: string
    name?: string | null
    email: string
    image?: string | null
  }
}

export interface CreateTicketData {
  title: string
  content: string
  priority: TicketPriority
}

export interface UpdateTicketData {
  title?: string
  content?: string
  status?: TicketStatus
  priority?: TicketPriority
  assignedTo?: string | null
}

export type MessageType = "message" | "status" | "assign" | "bot";

export interface Message {
  id: string;
  type: MessageType;
  content: string;
  createdAt: Date;
  user: {
    id: string;
    name?: string | null;
    email: string;
    image?: string | null;
    roleId?: number;
    isBot?: boolean;
  };
  icon?: string;
}