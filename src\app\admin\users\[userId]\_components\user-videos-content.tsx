"use client";

import { UserPagination } from "../../_components/user-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ImageIcon, Lock, MessageSquare, Music } from "lucide-react";
import { format } from "date-fns";
import { Video } from "@/types/video";
import { Badge } from "@/components/ui/badge";
import { User } from "@/lib/db/user";

function formatDate(dateValue, formatString = "dd.MM.yyyy") {
  if (!dateValue) return "N/A";

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid date";
    return format(date, formatString);
  } catch (error) {
    console.error("Date formatting error:", error);
    return "Invalid date";
  }
}

type Props = {
  user: User;
  videos: Video[];
  totalVideos: number;
  videoPage: number;
  ITEMS_PER_PAGE: number;
};

export default function UserVideosContent({
  user,
  videos,
  totalVideos,
  videoPage,
  ITEMS_PER_PAGE,
}: Props) {
  return (
    <div className="space-y-6">
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Recent Uploads</h2>
          <UserPagination
            currentPage={videoPage}
            totalPages={Math.ceil(totalVideos / ITEMS_PER_PAGE)}
            type="video"
          />
        </div>
        {videos.length > 0 ? (
          <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-800/40 hover:bg-gray-800/20">
                  <TableHead className="text-gray-300 font-semibold">
                    Title
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Short Link
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Views
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Status
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Uploaded
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {videos.map((video) => (
                  <TableRow
                    key={video.id}
                    className="border-gray-800/40 hover:bg-gray-800/20"
                  >
                    <TableCell className="font-medium text-white">
                      {video.title}
                    </TableCell>
                    <TableCell>
                      <a
                        href={`/admin/users/${user.id}/videos/${video.shortLink}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300 hover:underline transition-colors"
                      >
                        {video.shortLink}
                      </a>
                    </TableCell>
                    <TableCell className="text-gray-300">
                      {video.views}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {video.isPrivate && (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1 border-gray-600 text-gray-300"
                          >
                            <Lock className="h-3 w-3" /> Private
                          </Badge>
                        )}
                        {video.commentsDisabled && (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1 border-gray-600 text-gray-300"
                          >
                            <MessageSquare className="h-3 w-3" /> Comments Off
                          </Badge>
                        )}
                        {video.musicDisabled && (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1 border-gray-600 text-gray-300"
                          >
                            <Music className="h-3 w-3" /> Music Off
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-300">
                      {formatDate(video.createdAt)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center p-8 bg-gray-900/30 border border-gray-800/40 rounded-xl">
            <p className="text-gray-400">No videos found for this user.</p>
          </div>
        )}
      </div>
    </div>
  );
}