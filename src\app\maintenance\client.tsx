"use client"

import { useState, useEffect } from "react"
import { MaintenanceHeader } from "./_components/maintenance-header"
import { MaintenanceStatus } from "./_components/maintenance-status"
import { MaintenanceUpdates } from "./_components/maintenance-updates"
import { MaintenanceActions } from "./_components/maintenance-actions"
import { MaintenanceFooter } from "./_components/maintenance-footer"
import { $Enums } from "@prisma/client"

interface MaintenanceData {
  enabled: boolean
  duration: string
  updates: {
    timestamp: string
    message: string
    type: $Enums.MaintenanceTypes
  }[]
}

export default function MaintenanceClient({
  maintenanceData,
}: {
  maintenanceData: MaintenanceData
}) {
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission | "default">("default")
  const [showNotificationSuccess, setShowNotificationSuccess] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setNotificationPermission(Notification.permission)
    }
  }, [])

  const requestNotification = async () => {
    if (typeof window !== "undefined" && "Notification" in window) {
      try {
        const permission = await Notification.requestPermission()
        setNotificationPermission(permission)

        if (permission === "granted") {
          const notification = new Notification("StreamBliss Maintenance", {
            body: "You'll be notified when StreamBliss is back online.",
          })

          setShowNotificationSuccess(true)
          setTimeout(() => setShowNotificationSuccess(false), 3000)
        }
      } catch (error) {
        console.error("Error requesting notification permission:", error)
      }
    }
  }

  const openMailto = () => {
    window.location.href = "mailto:<EMAIL>?subject=Maintenance%20Inquiry"
  }

  return (
    <div className="min-h-screen w-full bg-black flex flex-col items-center justify-between relative overflow-hidden">
      {/* Background styling to match landing page */}
      <div className="fixed inset-0">
        {/* Deep purple glow in the center-left area */}
        <div
          className="absolute w-[800px] h-[800px] rounded-full opacity-20 blur-[120px]"
          style={{
            background:
              "radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, rgba(138, 43, 226, 0.1) 40%, transparent 70%)",
            top: "30%",
            left: "20%",
            transform: "translate(-50%, -50%)",
          }}
        />

        {/* Subtle top-right glow */}
        <div
          className="absolute w-[400px] h-[400px] rounded-full opacity-10 blur-[80px]"
          style={{
            background:
              "radial-gradient(circle, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.05) 50%, transparent 80%)",
            top: "15%",
            right: "10%",
            transform: "translate(50%, -50%)",
          }}
        />

        {/* Very subtle noise texture */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
            backgroundSize: "128px 128px",
          }}
        />
      </div>

      {/* Simple logo header with animation */}
      <header className="relative z-10 w-full py-6 px-6 animate-fadeIn" style={{ animationDuration: "1s" }}>
        <div className="text-2xl font-bold text-[#B066FF]">StreamBliss</div>
      </header>

      {/* Main content */}
      <div className="relative z-10 text-center space-y-8 p-8 max-w-2xl flex-1 flex flex-col justify-center">
        <MaintenanceHeader />
        <MaintenanceStatus estimatedCompletion={maintenanceData.duration} />
        <MaintenanceUpdates updates={maintenanceData.updates} />
        <MaintenanceActions
          onNotify={requestNotification}
          onContact={openMailto}
          notificationPermission={notificationPermission}
          showSuccess={showNotificationSuccess}
        />
      </div>

      {/* Simple footer */}
      <MaintenanceFooter />

      {/* Floating particles animation */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-[#B066FF]/10"
            style={{
              width: `${Math.random() * 6 + 2}px`,
              height: `${Math.random() * 6 + 2}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 15}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
              opacity: Math.random() * 0.3 + 0.1,
            }}
          />
        ))}
      </div>
    </div>
  )
}