"use server";

import {$Enums} from "@prisma/client";
import {prisma} from "@/lib/prisma";
import {getUserById} from "@/lib/db/user";

export const createLog = async (userId: string, info: string, actionType: $Enums.LogActions, details?: string) => {
    try {
        if (!userId || !info || !actionType) {
            throw new Error("Missing required parameters");
        }

        const executor = await getUserById(userId);
        if (!executor) {
            throw new Error("No Executor not found");
        }

        return prisma.logs.create({
            data: {
                info: info,
                action: actionType,
                user: executor.id,
                furtherInfo: details || ""
            }
        });
    } catch (error) {
        throw new Error("Error creating log: " + error.message + "");
    }
};

export const getLogs = async () => {
    try {
        return prisma.logs.findMany({});
    } catch (error) {
        throw new Error("Error getting logs: " + error.message + "");
    }
};

export const getLogsByAction = async (actionType: $Enums.LogActions) => {
    try {
        if (!actionType) {
            throw new Error("Missing required parameters");
        }

        return prisma.logs.findMany({
            where: {
                action: actionType
            }
        });
    } catch (error) {
        throw new Error("Error getting logs by action: " + error.message + "");
    }
};

export const getUserLogs = async (userId: string) => {
    try {
        if (!userId) {
            throw new Error("Missing required parameters");
        }

        const executor = await getUserById(userId);
        if (!executor) {
            throw new Error("No Executor not found");
        }

        return prisma.logs.findMany({
            where: {
                user: executor.id
            }
        });
    } catch (error) {
        throw new Error("Error getting user logs: " + error.message + "");
    }
};

export const getUserLogsByAction = async (userId: string, actionType: $Enums.LogActions) => {
    try {
        if (!userId || !actionType) {
            throw new Error("Missing required parameters");
        }

        const executor = await getUserById(userId);
        if (!executor) {
            throw new Error("No Executor not found");
        }

        return prisma.logs.findMany({
            where: {
                user: executor.id,
                action: actionType
            }
        });
    } catch (error) {
        throw new Error("Error getting user logs with action: " + error.message + "");
    }
};
