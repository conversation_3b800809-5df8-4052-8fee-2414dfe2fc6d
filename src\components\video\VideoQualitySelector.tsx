"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronUp, Check } from "lucide-react";

export interface QualityOption {
  label: string;
  value: string;
  resolution: string;
  fps?: number;
  bitrate?: number;
}

export interface VideoQualitySelectorProps {
  currentQuality: string;
  qualities: QualityOption[];
  onQualityChange: (quality: string) => void;
  className?: string;
}

const defaultQualities: QualityOption[] = [
  { label: "4K60", value: "4k60", resolution: "3840x2160", fps: 60, bitrate: 25000 },
  { label: "1440p60", value: "1440p60", resolution: "2560x1440", fps: 60, bitrate: 16000 },
  { label: "1080p60", value: "1080p60", resolution: "1920x1080", fps: 60, bitrate: 8000 },
  { label: "1080p", value: "1080p", resolution: "1920x1080", fps: 30, bitrate: 5000 },
  { label: "720p60", value: "720p60", resolution: "1280x720", fps: 60, bitrate: 4500 },
  { label: "720p", value: "720p", resolution: "1280x720", fps: 30, bitrate: 2500 },
  { label: "480p", value: "480p", resolution: "854x480", fps: 30, bitrate: 1000 },
  { label: "360p", value: "360p", resolution: "640x360", fps: 30, bitrate: 600 },
  { label: "Auto", value: "auto", resolution: "Adaptive", fps: 60 },
];

export const VideoQualitySelector: React.FC<VideoQualitySelectorProps> = ({
  currentQuality,
  qualities = defaultQualities,
  onQualityChange,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentQualityOption = qualities.find(q => q.value === currentQuality) || qualities[0];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleQualitySelect = (quality: string) => {
    onQualityChange(quality);
    setIsOpen(false);
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Quality Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 text-white text-sm font-medium bg-white/10 hover:bg-white/20 px-3 py-1.5 rounded transition-all duration-200 hover:scale-105 backdrop-blur-sm"
      >
        <span>{currentQualityOption.label}</span>
        <ChevronUp
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "rotate-180" : "rotate-0"
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute bottom-full mb-2 right-0 bg-black/90 backdrop-blur-md rounded-lg shadow-2xl border border-white/10 min-w-[200px] z-50">
          <div className="py-2">
            <div className="px-3 py-2 text-xs text-gray-400 font-medium border-b border-white/10">
              Quality Settings
            </div>
            
            {qualities.map((quality) => (
              <button
                key={quality.value}
                onClick={() => handleQualitySelect(quality.value)}
                className="w-full flex items-center justify-between px-3 py-2 text-sm text-white hover:bg-white/10 transition-colors duration-150"
              >
                <div className="flex flex-col items-start">
                  <span className="font-medium">{quality.label}</span>
                  <span className="text-xs text-gray-400">
                    {quality.resolution}
                    {quality.fps && quality.fps > 30 && (
                      <span className="ml-1 text-purple-400">• {quality.fps}fps</span>
                    )}
                  </span>
                </div>
                
                {currentQuality === quality.value && (
                  <Check className="w-4 h-4 text-purple-400" />
                )}
              </button>
            ))}
          </div>
          
          {/* StreamBliss 60fps Badge */}
          <div className="px-3 py-2 border-t border-white/10">
            <div className="flex items-center space-x-2 text-xs">
              <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse" />
              <span className="text-gray-400">StreamBliss 60fps Enhanced</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoQualitySelector;