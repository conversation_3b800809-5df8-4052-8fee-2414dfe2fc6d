"use client";

import {
  Globe,
  Lock,
  ArrowUpDown,
  Upload,
  User,
  ChevronDown,
  Search,
  Check,
  Shield,
} from "lucide-react";
import { $Enums } from "@prisma/client";
import { useState, useRef } from "react";
import Image from "next/image";
import { SortPopup, SortOption } from "./sort-popup";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { DashboardNotifications } from "./dashboard-notifications";

type NewDashboardHeaderProps = {
  userName: string;
  userImage?: string | null;
  notifications: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  hasAccessToAdmin?: boolean;
  activeFilter: string;
  setActiveFilter: (filter: string) => void;
  isPrivate: boolean;
  setIsPrivate: (isPrivate: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortBy: SortOption;
  setSortBy: (sort: SortOption) => void;
  onUpload: () => void;
};

export function NewDashboardHeader({
  userName,
  userImage,
  notifications,
  hasAccessToAdmin,
  activeFilter,
  setActiveFilter,
  isPrivate,
  setIsPrivate,
  searchQuery,
  setSearchQuery,
  sortBy,
  setSortBy,
  onUpload,
}: NewDashboardHeaderProps) {
  const [avatarError, setAvatarError] = useState(false);
  const [showSortPopup, setShowSortPopup] = useState(false);
  const sortButtonRef = useRef<HTMLButtonElement>(null);
  const router = useRouter();

  return (
    <>
      <SortPopup
        isOpen={showSortPopup}
        onClose={() => setShowSortPopup(false)}
        currentSort={sortBy}
        onSortChange={setSortBy}
        triggerRef={sortButtonRef}
      />
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 lg:mb-8 gap-4 lg:gap-0">
          <div data-tutorial="welcome-area">
            <h1 className="text-lg md:text-xl font-semibold text-white leading-[1.4] mb-2 font-['Montserrat']">
              Your Recent Files
            </h1>
            <p className="text-white/70 text-sm font-normal leading-[1.6] font-['Montserrat']">
              Manage and organize your Video and images.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
            <DashboardNotifications notifications={notifications} />

            {/* Admin Shield Button */}
            {hasAccessToAdmin && (
              <Button
                variant="ghost"
                size="icon"
                className="relative w-10 h-10 rounded-full bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                onClick={() => router.push("/admin")}
              >
                <Shield className="h-5 w-5 text-white/70" />
              </Button>
            )}

            {/* User Profile */}
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border border-white/10 bg-white/5 overflow-hidden">
                {userImage && userImage.trim() !== "" && !avatarError ? (
                  <Image
                    src={userImage}
                    alt={userName || "Avatar"}
                    width={40}
                    height={40}
                    className="h-full w-full object-cover rounded-full"
                    onError={() => setAvatarError(true)}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <User className="h-4 w-4 sm:h-5 sm:w-5 text-white/70" />
                  </div>
                )}
              </div>
              <div className="text-white hidden sm:block">
                <p className="text-sm font-medium">Welcome Back</p>
                <p className="text-xs text-white/70">{userName}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div
          className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-6 min-w-0"
          data-tutorial="filter-controls"
        >
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 min-w-0 flex-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center gap-2 px-3 sm:px-5 py-1.5 sm:py-2 rounded-full bg-white/8 text-white hover:bg-white/12 transition-all duration-200 border border-white hover:border-white/80 will-change-transform transform-gpu flex-shrink-0">
                  <span className="text-sm font-semibold font-['Montserrat'] whitespace-nowrap">
                    {activeFilter === "all"
                      ? "All"
                      : activeFilter === "videos"
                        ? "Videos"
                        : "Images"}
                  </span>
                  <ChevronDown className="w-3 h-3 sm:w-3 sm:h-6" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="start"
                className="w-[140px] sm:w-[152px] p-3 sm:p-3.5 border border-white/24 bg-black/80 rounded-lg shadow-xl"
              >
                <div className="flex flex-col gap-3">
                  {/* All Option */}
                  <DropdownMenuItem
                    onClick={() => setActiveFilter("all")}
                    className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                  >
                    <div className="w-5 h-5 rounded border border-white/20 bg-white/8 flex items-center justify-center">
                      {activeFilter === "all" && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                    <span className="text-sm sm:text-lg font-normal text-white font-['Montserrat']">
                      All
                    </span>
                  </DropdownMenuItem>
                  <div className="w-full h-px bg-white/10"></div>
                  <DropdownMenuItem
                    onClick={() => setActiveFilter("videos")}
                    className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                  >
                    <div className="w-5 h-5 rounded border border-white/20 bg-white/8 flex items-center justify-center">
                      {activeFilter === "videos" && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                    <span className="text-sm sm:text-lg font-normal text-white font-['Montserrat']">
                      Videos
                    </span>
                  </DropdownMenuItem>
                  <div className="w-full h-px bg-white/10"></div>
                  <DropdownMenuItem
                    onClick={() => setActiveFilter("images")}
                    className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                  >
                    <div className="w-5 h-5 rounded border border-white/20 bg-white/8 flex items-center justify-center">
                      {activeFilter === "images" && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                    <span className="text-sm sm:text-lg font-normal text-white font-['Montserrat']">
                      Images
                    </span>
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            <div className="flex items-center gap-3 px-3 sm:px-4 py-2 rounded-[40px] border border-[#ccdbe9]/16 bg-white/2 flex-1 max-w-full sm:max-w-[400px] min-w-0 sm:min-w-[200px]">
              <Search className="w-5 h-5 sm:w-5.5 sm:h-6.5 text-white/70 flex-shrink-0" />
              <input
                type="text"
                placeholder="Search for something"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-transparent text-white placeholder:text-white/70 text-sm sm:text-base font-normal font-['Montserrat'] outline-none min-w-0"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 flex-shrink-0 w-full lg:w-auto">
            <div className="flex items-center gap-2 sm:gap-3 overflow-x-auto pb-2 sm:pb-0">
              <div className="flex items-center gap-1.5 flex-shrink-0">
                <Globe
                  className={`w-5 h-5 ${!isPrivate ? "text-[#b851e0]" : "text-white"}`}
                />
                <span
                  className={`text-sm font-semibold font-['Montserrat'] ${!isPrivate ? "text-[#b851e0]" : "text-white"} whitespace-nowrap`}
                >
                  Public
                </span>
              </div>
              <div
                className="w-12 h-6 rounded-full bg-[#1e1920] cursor-pointer relative flex-shrink-0"
                onClick={() => setIsPrivate(!isPrivate)}
              >
                <div
                  className={`absolute top-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-300 ${
                    isPrivate ? "translate-x-6" : "translate-x-0.5"
                  }`}
                />
              </div>
              <div className="flex items-center gap-1.5 flex-shrink-0">
                <Lock
                  className={`w-5 h-5 ${isPrivate ? "text-[#b851e0]" : "text-white"}`}
                />
                <span
                  className={`text-sm font-semibold font-['Montserrat'] ${isPrivate ? "text-[#b851e0]" : "text-white"} whitespace-nowrap`}
                >
                  Private
                </span>
              </div>
            </div>

            <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto">
              <button
                ref={sortButtonRef}
                onClick={() => setShowSortPopup(!showSortPopup)}
                className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-white/24 bg-white/12 text-white/70 hover:bg-white/16 transition-all duration-200 flex-shrink-0"
              >
                <ArrowUpDown className="w-4 h-4" />
                <span className="text-sm font-semibold font-['Montserrat'] whitespace-nowrap">
                  Sort by
                </span>
              </button>
              <button
                onClick={() => onUpload()}
                data-tutorial="upload-button"
                className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-gradient-to-r from-[#B851E0] to-[#EB489B] text-white hover:opacity-90 transition-opacity duration-200 h-8 sm:h-9 min-w-[120px] sm:min-w-[140px] justify-center flex-shrink-0"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 31 31"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-4 h-4 flex-shrink-0"
                >
                  <path
                    d="M15.5 19.25H18V11.75H21.125L15.5 6.125M15.5 19.25H13V11.75H9.875L15.5 6.125"
                    fill="white"
                  />
                  <path
                    d="M15.5 19.25H18V11.75H21.125L15.5 6.125L9.875 11.75H13V19.25H15.5Z"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M8 24.25H23"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="text-white text-center font-['Montserrat'] text-sm font-semibold leading-[160%] whitespace-nowrap">
                  Upload New File
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}