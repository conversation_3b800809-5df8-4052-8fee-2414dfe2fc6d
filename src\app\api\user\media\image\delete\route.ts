import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function DELETE(request: NextRequest) {
    const {imageId} = await request.json();
    if (!imageId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No Image ID provided."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: "No valid user-session found."});
    }

    const imageDeleteRequest = await fetch(process.env.VIDEO_API_URL + "/images/image/" + imageId, {
        method: "DELETE",
        headers: {
            "Authorization": `Bearer ${userSession.accessToken}`,
            "x-api-key": process.env.API_SERVER_KEY!,
        }
    });

    if (!imageDeleteRequest.ok) {
        const data = await response.json();
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, message: "Image successfully deleted."});
}