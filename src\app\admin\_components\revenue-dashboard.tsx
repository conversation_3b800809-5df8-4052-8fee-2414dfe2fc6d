"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Download,
  FileText,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
} from "recharts";
import {
  getStripeRevenue,
  type StripeRevenue,
} from "../_actions/get-stripe-data";

const COLORS = ["#22d3ee", "#a855f7", "#f87171"];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black border border-gray-800 rounded-lg shadow-xl p-3">
        <p className="font-medium text-white text-sm mb-2">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}:{" "}
            {entry.value?.toLocaleString("en-US", {
              style: "currency",
              currency: "USD",
            })}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function RevenueDashboard() {
  const [period, setPeriod] = useState("monthly");
  const [data, setData] = useState<StripeRevenue | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const revenueData = await getStripeRevenue();
        setData(revenueData);
      } catch (error) {
        console.error("Error fetching revenue data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className="bg-black border border-gray-800/60 rounded-lg p-4 animate-pulse"
            >
              <div className="h-3 w-20 bg-gray-800 rounded mb-2" />
              <div className="h-6 w-16 bg-gray-800 rounded mb-1" />
              <div className="h-2 w-12 bg-gray-800 rounded" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">Failed to load revenue data</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Financial Overview */}
      <div>
        <h2 className="text-base font-semibold mb-3 text-white">
          Financial Overview
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
          <Card className="bg-black border border-gray-800/60 hover:border-gray-700/80 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2 px-4 pt-4">
              <CardTitle className="text-xs font-medium text-gray-300">
                Total Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-white">
                {formatCurrency(data.totalRevenue)}
              </div>
              <div className="text-xs text-green-400 mt-1">
                +{data.yearlyGrowth}% from last year
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black border border-gray-800/60 hover:border-gray-700/80 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2 px-4 pt-4">
              <CardTitle className="text-xs font-medium text-gray-300">
                Monthly Revenue
              </CardTitle>
              <Calendar className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-white">
                {formatCurrency(data.monthlyRevenue)}
              </div>
              <div className="text-xs text-green-400 mt-1">
                +{data.conversionRate}% conversion rate
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black border border-gray-800/60 hover:border-gray-700/80 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2 px-4 pt-4">
              <CardTitle className="text-xs font-medium text-gray-300">
                Active Subscriptions
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-white">
                {data.activeSubscriptions}
              </div>
              <div className="text-xs text-gray-400 mt-1">Active customers</div>
            </CardContent>
          </Card>

          <Card className="bg-black border border-gray-800/60 hover:border-gray-700/80 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2 px-4 pt-4">
              <CardTitle className="text-xs font-medium text-gray-300">
                Profit Margin
              </CardTitle>
              <DollarSign className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-white">NaN%</div>
              <div className="text-xs text-gray-400 mt-1">
                Net profit margin
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Revenue Analytics */}
      <Card className="bg-black border border-gray-800/60">
        <CardHeader className="px-4 pb-2">
          <CardTitle className="text-base text-white">
            Revenue Analytics
          </CardTitle>
          <CardDescription className="text-xs text-gray-400">
            Track your revenue and subscription growth
          </CardDescription>
        </CardHeader>
        <CardContent className="px-4 pb-4">
          <Tabs value={period} onValueChange={setPeriod} className="mt-2">
            <TabsList className="w-full justify-start overflow-x-auto bg-transparent border-b border-gray-800">
              <TabsTrigger
                value="monthly"
                className="text-sm data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 data-[state=active]:bg-transparent text-gray-400 border-b-2 border-transparent px-4 py-2 bg-transparent"
              >
                Monthly
              </TabsTrigger>
              <TabsTrigger
                value="packages"
                className="text-sm data-[state=active]:text-blue-400 data-[state=active]:border-b-2 data-[state=active]:border-blue-400 data-[state=active]:bg-transparent text-gray-400 border-b-2 border-transparent px-4 py-2 bg-transparent"
              >
                Package Distribution
              </TabsTrigger>
            </TabsList>
            <div className="h-64 mt-4">
              <ResponsiveContainer width="100%" height="100%">
                {period === "monthly" ? (
                  <LineChart
                    data={data.monthlyData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                  >
                    <CartesianGrid
                      strokeDasharray="1 1"
                      stroke="#1f2937"
                      opacity={0.3}
                    />
                    <XAxis
                      dataKey="month"
                      stroke="#6b7280"
                      tick={{ fill: "#6b7280", fontSize: 12 }}
                      axisLine={{ stroke: "#374151" }}
                      tickLine={{ stroke: "#374151" }}
                    />
                    <YAxis
                      yAxisId="left"
                      orientation="left"
                      stroke="#6b7280"
                      tick={{ fill: "#6b7280", fontSize: 12 }}
                      axisLine={{ stroke: "#374151" }}
                      tickLine={{ stroke: "#374151" }}
                      tickFormatter={(value) => `$${value}`}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      stroke="#6b7280"
                      tick={{ fill: "#6b7280", fontSize: 12 }}
                      axisLine={{ stroke: "#374151" }}
                      tickLine={{ stroke: "#374151" }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend
                      verticalAlign="top"
                      height={36}
                      wrapperStyle={{
                        paddingBottom: "20px",
                        fontSize: "12px",
                      }}
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#22d3ee"
                      strokeWidth={3}
                      dot={{ r: 4, strokeWidth: 2, fill: "#22d3ee" }}
                      activeDot={{ r: 6, strokeWidth: 2, fill: "#0891b2" }}
                      name="Revenue"
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="expenses"
                      stroke="#f87171"
                      strokeWidth={3}
                      dot={{ r: 4, strokeWidth: 2, fill: "#f87171" }}
                      activeDot={{ r: 6, strokeWidth: 2, fill: "#dc2626" }}
                      name="Expenses"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="subscriptions"
                      stroke="#22c55e"
                      strokeWidth={3}
                      dot={{ r: 4, strokeWidth: 2, fill: "#22c55e" }}
                      activeDot={{ r: 6, strokeWidth: 2, fill: "#16a34a" }}
                      name="Subscriptions"
                    />
                  </LineChart>
                ) : (
                  <PieChart>
                    <Pie
                      data={data.packageData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }: any) =>
                        `${name} ${((percent ?? 0) * 100).toFixed(0)}%`
                      }
                    >
                      {data.packageData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                          strokeWidth={2}
                          stroke="#000000"
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          return (
                            <div className="bg-black border border-gray-700 rounded-lg shadow-lg p-3">
                              <p className="font-medium text-white">
                                {payload[0].name}
                              </p>
                              <p
                                className="text-sm"
                                style={{ color: payload[0].color }}
                              >
                                {payload[0].value} customers
                              </p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      wrapperStyle={{
                        paddingTop: "20px",
                        fontSize: "12px",
                      }}
                    />
                  </PieChart>
                )}
              </ResponsiveContainer>
            </div>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-between gap-2 px-4 pb-4">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2 w-full sm:w-auto text-xs h-8 bg-black border-gray-700 text-gray-300 hover:bg-gray-900 hover:text-white"
          >
            <Download className="h-3 w-3" />
            Export Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2 w-full sm:w-auto text-xs h-8 bg-black border-gray-700 text-gray-300 hover:bg-gray-900 hover:text-white"
          >
            <FileText className="h-3 w-3" />
            Generate Report
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}