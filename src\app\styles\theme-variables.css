/* Theme Variables and Color Definitions */

@theme {
  /*  */
  --color-custom-pink: #eb489b;
  --color-custom-purple: #b851e0;
  --color-dark-purple: #110018;
  --color-light-purple: #3a1348;
  --color-light-pink: #e649a21f;
  --color-custom-black: #1f0c1d;
  --color-custom-skyblue: #42e8e014;
  --color-custom-gray: #28202c99;
  --background-hero-img: url("/assets/images/webp/hero-bg.webp");

  --background-border-gradient: linear-gradient(
    90deg,
    #000000 0%,
    #ffffff 54.03%,
    #000000 100%
  );
  --background-badge-gradient: linear-gradient(
    180deg,
    rgba(184, 81, 224, 0.08) 0%,
    rgba(235, 72, 155, 0.08) 100%
  );
  --background-light-badge-gradient: linear-gradient(
    350.29deg,
    rgba(255, 255, 255, 0.2) 22.72%,
    rgba(255, 255, 255, 0) 74.04%
  );
  --background-back-to-top: linear-gradient(180deg, #b851e0 0%, #eb489b 100%);

  --color-custom-light-gray: #26252a;
  --color-pink-100: #e649a208;
  --color-grey: #11001880;
  --background-ellipse-gradient: linear-gradient(
    180deg,
    #b851e0 0%,
    #eb489b 100%
  );
  --blur-ellipse: 600.0999755859375px;
  --color-custom-blur-gray: #8a858d;
  --color-light-white: #fcfcfc;

  /*  */
  --text-custom-3xl: 32px;
  --text-custom-4xl: 40px;
  --text-custom-5xl: 42px;
  --text-custom-6xl: 58px;

  /*  */
  --leading-100: 100%;
  --leading-120: 120%;
  --leading-130: 130%;
  --leading-140: 140%;
  --leading-160: 160%;
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-size: 200% 200%;
  animation: gradient-move 8s ease infinite;
}

.bg-badge {
  background: linear-gradient(
    180deg,
    rgba(184, 81, 224, 0.08) 0%,
    rgba(235, 72, 155, 0.08) 100%
  );
}

.glass-effect {
  background-color: rgba(17, 0, 24, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.bg-grid-pattern {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.1)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

/* Glassmorphism utilities */
.glass-card {
  background-color: rgba(17, 0, 24, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card:hover {
  background-color: rgba(17, 0, 24, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Backdrop filter utilities */
.backdrop-blur-enhanced {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
}

.backdrop-filter-none {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Border utilities */
.border-crisp {
  border-style: solid;
  border-width: 2px;
  border-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: none;
}

.border-crisp:hover {
  border-color: rgba(255, 255, 255, 1);
}

/* Disable backdrop blur on specific elements */
button {
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

button.backdrop-blur-xl,
button .backdrop-blur-xl {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

img {
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

img.backdrop-blur-md,
img.backdrop-blur-xl {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}
