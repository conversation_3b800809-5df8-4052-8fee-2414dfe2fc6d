"use server";

import { getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { nanoid } from "nanoid";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
  videoId?: string;
};

type Params = {
  title: string;
  userId: string;
  fileName: string;
};

export default async function submitUploadVideo({ title, userId, fileName }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!title) {
    return {
      success: false,
      message: "Title is required",
    }
  }

  if (!userId) {
    return {
      success: false,
      message: "User ID is required",
    }
  }

  const user = await getUserById(userId)
  if (!user) {
    return {
      success: false,
      message: "User not found",
    }
  }

  const video = await prisma.video.create({
    data: {
      title: title || fileName,
      url: `/videos/${fileName}`,
      shortLink: nanoid(10),
      userId: userId,
    },
  })

  return {
    success: true,
    message: "Video uploaded successfully",
    videoId: video.id,
  }
}
