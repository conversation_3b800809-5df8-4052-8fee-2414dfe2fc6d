"use server";

import {getClientIp} from "@/server/geolocation";
import {rateLimiter} from "@/lib/rate-limit";
import {getUserSession} from "@/server/session";
import {getUserById} from "@/lib/db/user";
import {hasPermission} from "@/server/admin";
import {disableCoupon, PromotionCode} from "@/server/coupons";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
    success: boolean;
    message: string;
    coupon?: PromotionCode;
}

type Props = {
    couponId: string;
}

export default async function submitDeleteCoupon({ couponId }: Props): Promise<Callback> {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
        return {
            success: false,
            message: "You have exceeded the rate limit. Please try again later.",
        };
    }

    try {
        if (!couponId) {
            return {
                success: false,
                message:
                    "Please provide a valid coupon id.",
            };
        }

        const userSession = await getUserSession();
        if (!userSession) {
            return {
                success: false,
                message: "Not authenticated",
            };
        }

        const userId = userSession.userId;
        const user = await getUserById(userId);
        if (!user) {
            return {
                success: false,
                message: "User not found. Please log in again.",
            };
        }

        if (!(await hasPermission(user.roleId, ["COUPONS_MANAGE"]))) {
            return {
                success: false,
                message: "You do not have permission to manage coupons.",
            };
        }

        const coupon = await disableCoupon(couponId);
        if (!coupon) {
            return {
                success: false,
                message: "Failed to disable coupon.",
            };
        }

        await createLog(user.id, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_COUPON_DISABLED, LogActions.COUPON);

        return {
            success: true,
            message: "Coupon disabled successfully.",
            coupon: coupon
        }
    } catch (error) {
        return {
            success: false,
            message: "An unexpected error occurred. Please try again.",
        };
    }
}