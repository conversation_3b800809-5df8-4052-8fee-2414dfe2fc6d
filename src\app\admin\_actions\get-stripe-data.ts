"use server"

import { stripe } from "@/lib/stripe";

export async function getStripeRevenue() {
  try {
    const now = Math.floor(Date.now() / 1000)
    const thirtyDaysAgo = now - 30 * 24 * 60 * 60
    const oneYearAgo = now - 365 * 24 * 60 * 60
    const charges = await stripe.charges.list({
      created: {
        gte: thirtyDaysAgo,
        lte: now,
      },
      limit: 100,
    })

    const totalRevenue = charges.data.reduce((sum, charge) => sum + (charge.amount || 0), 0) / 100

    const subscriptions = await stripe.subscriptions.list({
      status: "active",
      limit: 100,
      expand: ["data.default_payment_method"],
    })

    const monthlyRevenue = subscriptions.data.reduce((sum, subscription) => {
      const interval = subscription.items.data[0]?.price?.recurring?.interval || "month"
      const intervalCount = subscription.items.data[0]?.price?.recurring?.interval_count || 1
      const amount = subscription.items.data[0]?.price?.unit_amount || 0

      let monthlyAmount = amount
      if (interval === "year") {
        monthlyAmount = amount / 12
      } else if (interval === "week") {
        monthlyAmount = amount * 4
      } else if (interval === "day") {
        monthlyAmount = amount * 30
      }

      return sum + (monthlyAmount * intervalCount) / 100
    }, 0)

    const customers = await stripe.customers.list({
      limit: 100,
    })

    const totalCustomers = customers.data.length
    const subscribedCustomers = new Set(subscriptions.data.map((sub) => sub.customer)).size
    const conversionRate = totalCustomers > 0 ? (subscribedCustomers / totalCustomers) * 100 : 0

    const monthlyData: {
      month: string
      revenue: number
      subscriptions: number
      expenses: number
    }[] = []

    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date()
      monthStart.setMonth(monthStart.getMonth() - i)
      monthStart.setDate(1)
      monthStart.setHours(0, 0, 0, 0)
      
      const monthEnd = new Date(monthStart)
      monthEnd.setMonth(monthEnd.getMonth() + 1)
      monthEnd.setDate(0)
      monthEnd.setHours(23, 59, 59, 999)

      const monthCharges = await stripe.charges.list({
        created: {
          gte: Math.floor(monthStart.getTime() / 1000),
          lte: Math.floor(monthEnd.getTime() / 1000),
        },
        limit: 100,
      })

      const monthRevenue = monthCharges.data.reduce((sum, charge) => sum + (charge.amount || 0), 0) / 100
      const monthSubscriptions = await stripe.subscriptions.list({
        created: {
          gte: Math.floor(monthStart.getTime() / 1000),
          lte: Math.floor(monthEnd.getTime() / 1000),
        },
        status: "active",
        limit: 100,
      })

      monthlyData.push({
        month: monthStart.toLocaleString('default', { month: 'short' }),
        revenue: monthRevenue,
        subscriptions: monthSubscriptions.data.length,
        expenses: monthRevenue * 0.3,
      })
    }

    const packageDistribution = await Promise.all([
      stripe.customers.list({ limit: 100, expand: ['data.subscriptions'] }),
      stripe.prices.list({ active: true, expand: ['data.product'] }),
    ])

    const packageData = [
      { name: "Free", value: 0 },
      { name: "Pro", value: 0 },
      { name: "Creator", value: 0 },
    ]

    packageDistribution[0].data.forEach(customer => {
      const subscription = customer.subscriptions?.data[0]
      if (subscription) {
        const price = packageDistribution[1].data.find(p => p.id === subscription.items.data[0].price.id)
        if (price) {
          const productName = (price.product as {name: string}).name as string
          const index = packageData.findIndex(p => p.name === productName)
          if (index !== -1) {
            packageData[index].value++
          }
        }
      }
    })

    const lastYearCharges = await stripe.charges.list({
      created: {
        gte: oneYearAgo,
        lte: now,
      },
      limit: 100,
    })

    const lastYearRevenue = lastYearCharges.data.reduce((sum, charge) => sum + (charge.amount || 0), 0) / 100
    const yearlyGrowth = lastYearRevenue > 0
      ? ((totalRevenue - lastYearRevenue) / lastYearRevenue) * 100
      : 0

    const profitMargin = ((totalRevenue - (totalRevenue * 0.3)) / totalRevenue) * 100

    return {
      totalRevenue,
      monthlyRevenue,
      activeSubscriptions: subscriptions.data.length,
      conversionRate: Number.parseFloat(conversionRate.toFixed(1)),
      yearlyGrowth: Number.parseFloat(yearlyGrowth.toFixed(1)),
      profitMargin: Number.parseFloat(profitMargin.toFixed(1)),
      monthlyData,
      packageData,
    }
  } catch (error) {
    console.error("Error fetching Stripe data:", error)
    return {
      totalRevenue: 0,
      monthlyRevenue: 0,
      activeSubscriptions: 0,
      conversionRate: 0,
      yearlyGrowth: 0,
      profitMargin: 0,
      monthlyData: [],
      packageData: [
        { name: "Free", value: 0 },
        { name: "Pro", value: 0 },
        { name: "Creator", value: 0 },
      ],
    }
  }
}

// Add types for the return value
export type StripeRevenue = {
  totalRevenue: number
  monthlyRevenue: number
  activeSubscriptions: number
  conversionRate: number
  yearlyGrowth: number
  profitMargin: number
  monthlyData: {
    month: string
    revenue: number
    subscriptions: number
    expenses: number
  }[]
  packageData: {
    name: string
    value: number
  }[]
}