"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Shield, Trash2, UserX, AlertCircle } from "lucide-react"
import { useEnhancedToast } from "@/components/ui/enhanced-toast"
import { useRouter } from "next/navigation"
import { useState } from "react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { resolveCommentReport } from "../_actions/resolve-comment-report"
import { submitDeleteComment } from "../_actions/delete-comment"
import submitBanUser from "../_actions/ban-user"

interface CommentReportDialogProps {
  isOpen: boolean
  onClose: () => void
  report: any
}

type Report = {
  id: string
  status: string
  reason: string
  details?: string
  createdAt?: string
  reportedComment: {
    id: string
    message: string
  }
  reportedUser: {
    id: string
    name: string
  }
  reportedBy?: {
    id: string
    name: string
  }
  reportedVideo: {
    shortLink: string
    title: string
  }
}

const reasonLabels = {
  spam: "Spam or misleading",
  harassment: "Harassment or bullying",
  hate_speech: "Hate speech",
  inappropriate: "Inappropriate content",
  misinformation: "Misinformation",
  other: "Other",
}

export function CommentReportDialog({ isOpen, onClose, report }: CommentReportDialogProps) {
  const router = useRouter()
  const { success, error } = useEnhancedToast();
  const [actionNotes, setActionNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleResolveReport = async () => {
    setIsSubmitting(true)

    try {
      const response = await resolveCommentReport({
        reportId: report.id,
        status: "CLOSED",
      });

      if (response.success) {
        success("Report Resolved", response.message);
        onClose()
        router.refresh()
      } else {
        error("Resolve Failed", response.message);
      }
    } catch (err) {
      error("Resolve Error", "Failed to resolve the report. Please try again.");
      console.error("Error resolving report:", err)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteComment = async () => {
    setIsSubmitting(true)

    try {
      const response = await submitDeleteComment({
        commentId: report.reportedComment.id,
        reportId: report.id,
      })

      if (response.success) {
        success("Comment Deleted", response.message);
        onClose();
        router.refresh();
      } else {
        error("Delete Failed", response.message);
      }
    } catch (err) {
      error("Delete Error", "Failed to delete the comment. Please try again.");
      console.error("Error deleting comment:", err)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleBanUser = async () => {
    setIsSubmitting(true)

    try {
      const response = await submitBanUser({
        userId: report.reportedUser.id,
        reason: report.reason || "User banned due to report",
      })

      if (response.success) {
        success("User Banned", response.message);

        const resolveResponse = await resolveCommentReport({
          reportId: report.id,
          status: "CLOSED",
        });

        if (resolveResponse.success) {
          success("Report Resolved", resolveResponse.message);
          onClose()
          router.refresh()
        } else {
          error("Resolve Failed", resolveResponse.message);
        }
      } else {
        error("Ban Failed", response.message);
      }
    } catch (err) {
      error("Ban Error", "Failed to ban the user. Please try again.");
      console.error("Error banning user:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!report) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-black text-white border-zinc-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-yellow-500" />
            Comment Report Review
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Reported Comment */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-blue-400">
              <div className="w-5 h-5 flex items-center justify-center">📝</div>
              Reported Comment
            </div>
            <div className="bg-zinc-900 p-3 rounded-md text-sm">
              {report.reportedComment.message || "Comment content not available"}
            </div>
          </div>

          {/* Two column layout for user info and status */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-zinc-400">Reported User</div>
              <div>{report.reportedUser?.name}</div>
            </div>
            <div>
              <div className="text-sm text-zinc-400">Report Status</div>
              <Badge variant="outline" className="bg-zinc-800 text-white border-zinc-700">
                {report.status}
              </Badge>
            </div>
          </div>

          {/* Report Reason */}
          <div>
            <div className="text-sm text-zinc-400">Report Reason</div>
            <Badge variant="outline" className="bg-zinc-800 text-white border-zinc-700">
              {reasonLabels[report.reason as keyof typeof reasonLabels] || report.reason}
            </Badge>
          </div>

          {/* Two column layout for reporter and date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-zinc-400">Reported By</div>
              <Link
                href={`/admin/users/${report.reportedBy?.id}`}
                className="text-purple-400 hover:text-purple-300 hover:underline"
              >
                {report.reportedBy?.name}
              </Link>
            </div>
            <div>
              <div className="text-sm text-zinc-400">Reported On</div>
              <div className="text-zinc-300">
                {report.createdAt ? formatDistanceToNow(new Date(report.createdAt), { addSuffix: true }) : "Unknown"}
              </div>
            </div>
          </div>

          {/* Video */}
          <div>
            <div className="text-sm text-zinc-400">Video</div>
            <Link
              href={`/v/${report.reportedVideo.shortLink}`}
              className="text-blue-400 hover:text-blue-300 hover:underline"
              target="_blank"
            >
              {report.reportedVideo.title}
            </Link>
          </div>

          {/* Additional Details */}
          {report.details && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-yellow-500">
                <AlertCircle className="w-5 h-5" />
                Additional Details from Reporter
              </div>
              <div className="bg-zinc-900 p-3 rounded-md text-sm">{report.details}</div>
            </div>
          )}

          {/* Action Notes */}
          {report.status === "pending" && (
            <div className="space-y-2">
              <div className="text-sm text-zinc-400">Action Notes (optional)</div>
              <Textarea
                id="action-notes"
                placeholder="Add notes about the action taken..."
                value={actionNotes}
                onChange={(e) => setActionNotes(e.target.value)}
                rows={3}
                className="bg-zinc-900 border-zinc-700 text-white"
              />
            </div>
          )}

          {/* Action Buttons */}
          {report.status === "PENDING" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-zinc-400 mb-2">Comment Actions</div>
                <div className="space-y-2">
                  <Button
                    variant="destructive"
                    className="w-full justify-start"
                    onClick={handleDeleteComment}
                    disabled={isSubmitting}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Comment
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start bg-zinc-800 border-zinc-700 hover:bg-zinc-700"
                    onClick={handleResolveReport}
                    disabled={isSubmitting}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    Dismiss Report
                  </Button>
                </div>
              </div>
              <div>
                <div className="text-sm text-zinc-400 mb-2">User Actions</div>
                <Button
                  variant="destructive"
                  className="w-full justify-start"
                  onClick={handleBanUser}
                  disabled={isSubmitting}
                >
                  <UserX className="w-4 h-4 mr-2" />
                  Ban User
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="bg-zinc-800 border-zinc-700 hover:bg-zinc-700"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}