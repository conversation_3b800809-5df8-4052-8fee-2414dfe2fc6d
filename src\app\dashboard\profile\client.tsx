"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  Instagram,
  Twitch,
  Twitter,
  Globe,
  Youtube,
  Edit,
  Camera,
  User,
  Trash2,
} from "lucide-react";
import { ProfileUploader } from "../_components/profile-uploader";
import updateProfile from "./_forms/updateProfile";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitRemoveProfileImage from "./_forms/remove-profile-image";
import submitDownloadShareXConfig from "./_forms/download-sharex-config";
import Image from "next/image";
import { DashboardWrapper } from "../_components/dashboard-wrapper";

const profileFormSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  userId: z.string().min(2, {
    message: "User ID is not defined. Contact support.",
  }),
  instagram: z.string().optional(),
  twitch: z.string().optional(),
  twitter: z.string().optional(),
  website: z.string().optional(),
  youtube: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

export default function ProfilePageClient({
  userId,
  name,
  userPackage,
  image,
  email,
  instagram = "",
  twitch = "",
  twitter = "",
  website = "",
  youtube = "",
}: {
  userId: string;
  name: string;
  userPackage: string;
  image: string;
  email: string;
  instagram?: string;
  twitch?: string;
  twitter?: string;
  website?: string;
  youtube?: string;
}) {
  const router = useRouter();
  const { success, error, info } = useEnhancedToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [userImage, setUserImage] = useState(image);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [isEditingSocial, setIsEditingSocial] = useState(false);
  const [avatarError, setAvatarError] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: name,
      userId: userId,
      instagram: instagram,
      twitch: twitch,
      twitter: twitter,
      website: website,
      youtube: youtube,
    },
  });

  async function onSubmit(data: ProfileFormValues) {
    setIsLoading(true);
    try {
      if (
        data.name === name &&
        data.instagram === instagram &&
        data.twitch === twitch &&
        data.twitter === twitter &&
        data.website === website &&
        data.youtube === youtube
      ) {
        info("No Changes", "No changes made to the profile.");
        return;
      }

      const response = await fetch("/api/user/profile", {
        method: "POST",
        body: JSON.stringify(data),
      });

      const responseData = await response.json();
      if (responseData.status !== 200) {
        error("Update Failed", responseData.message);
        return;
      }

      success("Profile Updated", "Data has been successfully updated.");

      router.refresh();
    } catch (err) {
      error("Update Error", "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  }

  async function removeProfileImage() {
    setIsRemoving(true);

    try {
      const response = await fetch("/api/user/profile-picture/delete", {
        method: "DELETE",
      });

      const data = await response.json();
      if (!response.ok) {
        error("Delete Failed", data.message);
        return;
      }

      if (data.success) {
        success("Profile Picture Deleted", "Profile Picture has been successfully deleted.");
        setUserImage("");
      }
    } catch (err) {
      error("Delete Error", "Failed to remove profile image");
    } finally {
      setIsRemoving(false);
    }
  }

  async function downloadShareXConfig() {
    setIsDownloading(true);

    try {
      const response = await submitDownloadShareXConfig();
      if (response.success && response.config) {
        const blob = new Blob([response.config], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "streambliss-sharex-config.sxcu";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        success("Config Downloaded", "ShareX config downloaded successfully!");
      } else {
        error("Download Failed", response.message);
      }
    } catch (err) {
      error("Download Error", "Failed to download ShareX config");
    } finally {
      setIsDownloading(false);
    }
  }

  return (
    <DashboardWrapper
      userName={name}
      userImage={userImage}
      notifications={[]}
      className="font-['Montserrat']"
    >
      {/* Header */}
      <div className="bg-[#0B050D] rounded-2xl px-6 md:px-8 py-6 md:py-8 mb-6 md:mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3">
          <div className="max-w-3xl">
            <h1 className="text-white text-lg md:text-xl font-semibold leading-[140%] mb-1 md:mb-2">
              Edit Profile
            </h1>
            <p className="text-white/70 text-xs md:text-sm font-normal leading-[160%]">
              Update your profile information. These details will be visible on
              your public profile.
            </p>
          </div>
          <Button
            onClick={() => router.push("/dashboard/billing")}
            className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white text-base md:text-lg font-semibold rounded-full px-4 md:px-6 py-3 md:py-[14px] h-auto flex-shrink-0"
          >
            <svg
              className="w-5 h-5 md:w-7 md:h-7 mr-2"
              viewBox="0 0 29 29"
              fill="none"
            >
              <path
                d="M17.1733 4H8.39946C8.19511 4.00021 7.99486 4.05734 7.82116 4.16498C7.64745 4.27262 7.50717 4.42652 7.41603 4.60942L4.11592 11.2096C4.02476 11.3922 3.98616 11.5964 4.00442 11.7996C4.02268 12.0028 4.09708 12.1969 4.21933 12.3603L14.1042 25.5387C14.2467 25.7384 14.4518 25.8848 14.687 25.9546C14.9222 26.0243 15.174 26.0135 15.4023 25.9237C15.6001 25.8464 15.7712 25.7134 15.8951 25.5409L25.78 12.3603C25.9027 12.1967 25.9773 12.0022 25.9956 11.7986C26.0139 11.5949 25.975 11.3902 25.8834 11.2074L22.5833 4.60722C22.4919 4.42473 22.3514 4.27128 22.1778 4.16405C22.0041 4.05681 21.804 4.00001 21.5999 4H17.1733ZM16.4077 6.20007L17.873 10.6002H12.1264L13.5916 6.20007H16.4077ZM20.1918 10.6002L18.7266 6.20007H20.9201L23.1201 10.6002H20.1918ZM17.873 12.8003L14.9997 21.4246L12.1264 12.8003H17.873ZM18.1876 18.8175L20.1918 12.8003H22.6999L18.1876 18.8175ZM11.8118 18.8175L7.29942 12.8003H9.8075L11.8118 18.8175ZM9.8075 10.6002H6.87921L9.07928 6.20007H11.2728L9.8075 10.6002Z"
                fill="white"
              />
            </svg>
            <span className="hidden sm:inline">Upgrade Your Plan</span>
            <span className="sm:hidden">Upgrade</span>
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col xl:flex-row gap-6 xl:gap-8">
        {/* Left Profile Card */}
        <div
          className="bg-[#110018] border border-white/12 rounded-2xl p-6 md:p-8 h-fit flex-shrink-0"
          style={{
            width: "300px",
            maxWidth: "300px",
            minWidth: "300px",
            overflow: "visible",
            position: "relative",
          }}
        >
          <div
            className="flex flex-col items-center"
            style={{
              width: "100%",
              maxWidth: "100%",
              overflow: "visible",
            }}
          >
            {/* Profile Image */}
            <div className="relative mb-3 md:mb-4">
              <ProfileUploader
                initialImage={userImage}
                updateImage={(image) => {
                  setAvatarError(false);
                  setUserImage(image);
                  setTimeout(() => {
                    setIsImageLoading(false);
                  }, 100);
                }}
                removeImage={removeProfileImage}
                name={name}
                isLoading={isImageLoading || isRemoving}
              />
            </div>

            {/* User Info */}
            <div
              className="text-center mb-4 md:mb-5 w-full px-2"
              style={{
                maxWidth: "100%",
                overflow: "visible",
              }}
            >
              <h3
                className="text-white text-base md:text-lg font-semibold leading-[160%] mb-1 truncate"
                title={name}
              >
                {name}
              </h3>
              <p
                className="text-white text-xs md:text-sm font-normal leading-[160%] truncate"
                title={email}
              >
                {email}
              </p>
            </div>

            {/* Download Button */}
            <Button
              onClick={downloadShareXConfig}
              disabled={isDownloading}
              className="w-full h-[36px] md:h-[40px] bg-white/[0.12] border border-white/24 rounded-full text-white text-xs md:text-sm font-semibold hover:bg-white/[0.16] transition-all duration-200"
            >
              {isDownloading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <span className="text-center">Download ShareX Config</span>
              )}
            </Button>
          </div>
        </div>

        {/* Right Content */}
        <div className="flex-1 space-y-6 md:space-y-8 min-w-0 overflow-hidden">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              {/* Personal Information */}
              <div
                className={`bg-[#110018] border ${isEditingPersonal ? "border-[#B851E0] shadow-lg shadow-[#B851E0]/20" : "border-white/12"} rounded-2xl p-6 md:p-8 transition-all duration-200 overflow-hidden`}
                style={{ minWidth: 0 }}
              >
                {isEditingPersonal && (
                  <div className="bg-gradient-to-r from-[#B851E0]/10 to-[#EB489B]/10 border border-[#B851E0]/30 rounded-lg px-3 md:px-4 py-2 md:py-3 mb-4 md:mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-[#B851E0] rounded-full animate-pulse"></div>
                      <span className="text-[#B851E0] text-sm font-medium">
                        Editing Personal Information
                      </span>
                    </div>
                  </div>
                )}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                  <h2 className="text-white text-base md:text-lg font-semibold">
                    Personal Information
                  </h2>
                  <Button
                    type="button"
                    onClick={() => setIsEditingPersonal(!isEditingPersonal)}
                    className={`${isEditingPersonal ? "bg-[#B851E0]/20 border-[#B851E0] text-[#B851E0]" : "bg-white/[0.12] border-white/24 text-white"} rounded-full px-4 md:px-5 py-2 md:py-[10px] text-sm md:text-base font-semibold hover:bg-white/[0.16] h-auto flex-shrink-0 transition-all duration-200`}
                  >
                    <Edit className="w-4 h-4 md:w-6 md:h-6 mr-1 md:mr-[6px]" />
                    {isEditingPersonal ? "Cancel" : "Edit"}
                  </Button>
                </div>

                <div
                  className="space-y-4 md:space-y-5"
                  style={{ minWidth: 0, maxWidth: "100%" }}
                >
                  <div style={{ minWidth: 0, maxWidth: "100%" }}>
                    <label className="text-white text-sm font-medium mb-2 block">
                      Display Name
                    </label>
                    {isEditingPersonal ? (
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-1 md:py-2 text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0"
                                placeholder="Enter display name"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <div
                        className="border-b border-white/14 py-1 md:py-2"
                        style={{
                          minWidth: 0,
                          maxWidth: "100%",
                          overflow: "hidden",
                        }}
                      >
                        <span
                          className="text-white/70 text-sm font-medium"
                          style={{
                            display: "block",
                            width: "100%",
                            maxWidth: "100%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {name || "Not set"}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5">
                    <div>
                      <label className="text-white text-sm font-medium mb-2 block">
                        Email Address
                      </label>
                      <div
                        className="border-b border-white/14 py-1 md:py-2"
                        style={{
                          minWidth: 0,
                          maxWidth: "100%",
                          overflow: "hidden",
                        }}
                      >
                        <span
                          className="text-white/70 text-sm font-medium"
                          style={{
                            display: "block",
                            width: "100%",
                            maxWidth: "100%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {email}
                        </span>
                      </div>
                    </div>

                    <div>
                      <label className="text-white text-sm font-medium mb-2 block">
                        Phone Number
                      </label>
                      <div className="border-b border-white/14 py-1 md:py-2">
                        <span className="text-white/70 text-sm font-medium">
                          Not set
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="text-white text-sm font-medium mb-2 block">
                      Choose Language
                    </label>
                    <div className="border-b border-white/14 py-1 md:py-2 max-w-full">
                      <span className="text-white/70 text-sm font-medium">
                        English
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Media Links */}
              <div
                className={`bg-[#110018] border ${isEditingSocial ? "border-[#B851E0] shadow-lg shadow-[#B851E0]/20" : "border-white/12"} rounded-2xl p-4 md:p-5 mt-4 md:mt-6 transition-all duration-200`}
              >
                {isEditingSocial && (
                  <div className="bg-gradient-to-r from-[#B851E0]/10 to-[#EB489B]/10 border border-[#B851E0]/30 rounded-lg px-3 md:px-4 py-2 md:py-3 mb-4 md:mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-[#B851E0] rounded-full animate-pulse"></div>
                      <span className="text-[#B851E0] text-sm font-medium">
                        Editing Social Media Links
                      </span>
                    </div>
                  </div>
                )}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                  <h2 className="text-white text-base md:text-lg font-semibold">
                    Social Media Links
                  </h2>
                  <Button
                    type="button"
                    onClick={() => setIsEditingSocial(!isEditingSocial)}
                    className={`${isEditingSocial ? "bg-[#B851E0]/20 border-[#B851E0] text-[#B851E0]" : "bg-white/[0.12] border-white/24 text-white"} rounded-full px-4 md:px-5 py-2 md:py-[10px] text-sm md:text-base font-semibold hover:bg-white/[0.16] h-auto flex-shrink-0 transition-all duration-200`}
                  >
                    <Edit className="w-4 h-4 md:w-6 md:h-6 mr-1 md:mr-[6px]" />
                    {isEditingSocial ? "Cancel" : "Edit"}
                  </Button>
                </div>

                <div className="space-y-4 md:space-y-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5">
                    <div>
                      <label className="text-white text-sm font-medium mb-2 flex items-center gap-2">
                        <Instagram className="w-3 h-3 md:w-4 md:h-4" />
                        Instagram Link
                      </label>
                      {isEditingSocial ? (
                        <FormField
                          control={form.control}
                          name="instagram"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-1 md:py-2 text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0"
                                  placeholder="https://www.instagram.com/"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <div className="border-b border-white/14 py-1 md:py-2 min-w-0 max-w-full">
                          <span className="text-white/70 text-sm font-medium block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {instagram || "https://www.instagram.com/"}
                          </span>
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="text-white text-sm font-medium mb-2 flex items-center gap-2">
                        <Twitch className="w-3 h-3 md:w-4 md:h-4" />
                        Twitch Link
                      </label>
                      {isEditingSocial ? (
                        <FormField
                          control={form.control}
                          name="twitch"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-2 md:py-[6px] text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0"
                                  placeholder="https://www.Twitch.com/"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <div className="border-b border-white/14 py-1 md:py-2 min-w-0 max-w-full">
                          <span className="text-white/70 text-sm font-medium block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {twitch || "https://www.Twitch.com/"}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5 min-w-0">
                    <div className="min-w-0">
                      <label className="text-white text-sm font-medium mb-2 flex items-center gap-2">
                        <Twitter className="w-3 h-3 md:w-4 md:h-4" />
                        Twitter Link
                      </label>
                      {isEditingSocial ? (
                        <FormField
                          control={form.control}
                          name="twitter"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-1 md:py-2 text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0 w-full"
                                  placeholder="https://www.Twitter.com/"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <div className="border-b border-white/14 py-1 md:py-2 min-w-0 max-w-full">
                          <span className="text-white/70 text-sm font-medium block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {twitter || "https://www.Twitter.com/"}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="min-w-0">
                      <label className="text-white text-sm font-medium mb-2 flex items-center gap-2">
                        <Youtube className="w-3 h-3 md:w-4 md:h-4" />
                        YouTube Link
                      </label>
                      {isEditingSocial ? (
                        <FormField
                          control={form.control}
                          name="youtube"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-1 md:py-2 text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0 w-full"
                                  placeholder="https://www.Youtube.com/"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      ) : (
                        <div className="border-b border-white/14 py-1 md:py-2 min-w-0 max-w-full">
                          <span className="text-white/70 text-sm font-medium block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {youtube || "https://www.Youtube.com/"}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="min-w-0">
                    <label className="text-white text-sm font-medium mb-2 flex items-center gap-2">
                      <Globe className="w-3 h-3 md:w-4 md:h-4" />
                      Website Link
                    </label>
                    {isEditingSocial ? (
                      <FormField
                        control={form.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-transparent border-0 border-b border-white/14 rounded-none px-0 py-1 md:py-2 text-white/70 text-sm font-medium focus:border-white/30 focus-visible:ring-0 w-full"
                                placeholder="hhttps://streambliss.cloud"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <div className="border-b border-white/14 py-1 md:py-2 min-w-0 max-w-full">
                        <span className="text-white/70 text-sm font-medium block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                          {website || "https://streambliss.cloud"}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Save Button - Only show when editing */}
              {(isEditingPersonal || isEditingSocial) && (
                <div className="flex justify-center mt-6 md:mt-8">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white text-sm font-semibold rounded-full px-6 md:px-8 py-2 md:py-3 h-auto w-full sm:w-auto"
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : null}
                    Save Changes
                  </Button>
                </div>
              )}
            </form>
          </Form>
        </div>
      </div>
    </DashboardWrapper>
  );
}