"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Share2, Flag, User, X, Eye } from "lucide-react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { ReportDialog } from "./_components/report-dialog";
import { StreamBlissVideoPlayer } from "@/components/video";
import Link from "next/link";
import Image from "next/image";

interface ShortLinkPageClientProps {
  videoUrl: string;
  title: string;
  views: number;
  videoId: string;
  thumbnailUrl: string;
  uploader: {
    name: string;
    email: string;
    avatarUrl: string;
    id: string;
  };
  createdAt: Date;
  musicDisabled: boolean;
  commentsDisabled: boolean;
  isPrivate: boolean;
  currentUser?: {
    id: string;
    name: string;
    email: string;
    image?: string;
  } | null;
  isAdmin: boolean;
  adminView?: boolean;
  hasLikedVideo?: boolean;
  hasDislikedVideo?: boolean;
  isSubscribed?: boolean;
  channelNotificationsEnabled?: boolean;
}

export default function ShortLinkPageClient({
  videoUrl,
  title,
  views,
  videoId,
  thumbnailUrl,
  isPrivate,
}: ShortLinkPageClientProps) {
  const { success, error } = useEnhancedToast();
  const [isReportOpen, setIsReportOpen] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [isPortrait, setIsPortrait] = useState(false);

  // Detect video orientation based on aspect ratio
  useEffect(() => {
    if (videoAspectRatio !== null) {
      setIsPortrait(videoAspectRatio < 1);
    }
  }, [videoAspectRatio]);

  // Handle video metadata loaded to get aspect ratio
  const handleVideoMetadataLoaded = (aspectRatio: number) => {
    setVideoAspectRatio(aspectRatio);
  };

  // Get dynamic aspect ratio class based on video dimensions
  const getVideoContainerClass = () => {
    if (videoAspectRatio === null) {
      return "aspect-video max-w-4xl";
    }

    if (isPortrait) {
      return "aspect-[9/16] max-w-sm mx-auto";
    } else if (videoAspectRatio > 1.5) {
      return "max-w-5xl";
    } else {
      return "max-w-3xl mx-auto";
    }
  };

  // Get alignment classes for title and buttons to match video container
  const getTitleContainerClass = () => {
    if (videoAspectRatio === null) {
      return "max-w-4xl";
    }

    if (isPortrait) {
      return "max-w-sm mx-auto";
    } else if (videoAspectRatio > 1.5) {
      return "max-w-5xl";
    } else {
      return "max-w-3xl mx-auto";
    }
  };

  if (!videoUrl) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <X className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">
            Video Unavailable
          </h1>
          <p className="text-gray-400 mb-6">
            This content could not be found or is no longer available.
          </p>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => (window.location.href = "/community")}
          >
            Discover Community Content
          </Button>
        </div>
      </div>
    );
  }

  if (isPrivate) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">
            Private Content
          </h1>
          <p className="text-gray-400 mb-6">
            This video has been made private by its creator.
          </p>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => (window.location.href = "/community")}
          >
            Discover Community Content
          </Button>
        </div>
      </div>
    );
  }

  const shareVideo = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          url: window.location.href,
        });
      } catch (err) {
        if ((err as Error).name !== "AbortError") {
          error("Share Failed", "Failed to share video");
        }
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
      success("Link Copied", "Link copied to clipboard");
    }
  };

  return (
    <div className="h-screen bg-black overflow-hidden flex flex-col">
      <nav className="bg-black border-b border-gray-800 flex-shrink-0">
        <div className="w-full px-4 py-3 flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <Image
              src="/assets/images/webp/logo.webp"
              alt="StreamBliss"
              width={120}
              height={24}
              className="h-6 w-auto"
              priority
            />
          </Link>

          <Link href="/dashboard">
            <Button className="bg-gradient-to-r from-custom-pink to-custom-purple hover:scale-105 transition-transform duration-300 text-white text-sm font-semibold px-4 py-2 h-9 cursor-pointer">
              Open StreamBliss
            </Button>
          </Link>
        </div>
      </nav>

      {/* Purple Promo Banner */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 flex-shrink-0">
        <div className="max-w-[1200px] mx-auto px-4 py-3 flex items-center justify-center gap-3">
          <div className="flex items-center gap-2 text-white">
            <Image
              src="/assets/images/webp/logo.webp"
              alt="StreamBliss"
              width={100}
              height={20}
              className="h-5 w-auto filter brightness-0 invert"
            />
            <span className="text-sm font-medium">Video hosting made easy</span>
          </div>
          <Link href="/dashboard">
            <Button className="bg-white hover:bg-gray-100 hover:scale-105 transition-all duration-300 text-purple-600 text-sm font-semibold px-4 py-1.5 h-8 cursor-pointer border border-white/20 shadow-lg">
              Get started
            </Button>
          </Link>
        </div>
      </div>

      <main className="flex-1 bg-black px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <div
            className={`bg-black rounded-lg overflow-hidden mb-4 transition-all duration-300 ${getVideoContainerClass()}`}
            id="video-container"
          >
            <StreamBlissVideoPlayer
              src={videoUrl}
              title={title}
              autoplay={false}
              controls={true}
              className="w-full h-full"
              onMetadataLoaded={handleVideoMetadataLoaded}
            />
          </div>

          <div className={`flex items-center justify-between flex-shrink-0 ${getTitleContainerClass()}`}>
            <div className="flex-1 min-w-0">
              <h1 className="text-base font-normal text-white mb-1">{title}</h1>
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <Eye className="h-4 w-4" />
                <span>{views.toLocaleString()} views</span>
              </div>
            </div>

            <div className="flex items-center gap-3 ml-4">
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white/5 text-white border-white/20 hover:bg-white/10 hover:border-white/30 hover:scale-105 h-9 px-4 cursor-pointer transition-all duration-200 font-medium"
                onClick={shareVideo}
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-red-500/10 text-red-400 border-red-500/30 hover:bg-red-500/20 hover:border-red-500/50 hover:text-red-300 hover:scale-105 h-9 px-4 cursor-pointer transition-all duration-200 font-medium"
                onClick={() => setIsReportOpen(true)}
              >
                <Flag className="h-4 w-4" />
                Report
              </Button>
            </div>
          </div>
        </div>
      </main>

      <ReportDialog
        open={isReportOpen}
        onOpenChange={setIsReportOpen}
        videoId={videoId}
      />
    </div>
  );
}