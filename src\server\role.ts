"use server";

import { prisma } from "@/lib/prisma";

export const getRoleById = async (roleId: number | null | undefined) => {
  if (roleId === null || roleId === undefined) {
    return null; // added because of shortlink page as guest user
  }

  return await prisma.role.findUnique({
    where: { id: roleId },
    include: {
      permissions: {
        include: { permission: true },
      },
    },
  });
};

export const getRoles = async () => {
  return await prisma.role.findMany({
    select: {
      id: true,
      name: true,
    }
  });
}

export const updateUserRole = async (userId: string, roleId: number) => {
  return await prisma.user.update({
    where: { id: userId },
    data: { roleId },
  });
}