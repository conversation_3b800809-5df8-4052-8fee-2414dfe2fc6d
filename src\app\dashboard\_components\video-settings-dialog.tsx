"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect, useRef } from "react";
import { Loader2, X, Settings as SettingsIcon } from "lucide-react";
interface VideoSettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  video: any;
  onSave: (videoId: string, settings: VideoSettings) => Promise<void>;
}

interface VideoSettings {
  title: string;
  isPrivate: boolean;
  commentsDisabled: boolean;
  musicDisabled: boolean;
  showCommunity: boolean;
}

export function VideoSettingsDialog({
  isOpen,
  onClose,
  video,
  onSave,
}: VideoSettingsDialogProps) {
  const [settings, setSettings] = useState<VideoSettings>({
    title: video?.title || "",
    isPrivate: video?.isPrivate || false,
    commentsDisabled: video?.commentsDisabled || false,
    musicDisabled: video?.musicDisabled || false,
    showCommunity: video?.showCommunity || false,
  });
  const [isSaving, setIsSaving] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSettings({
      title: video?.title || "",
      isPrivate: video?.isPrivate || false,
      commentsDisabled: video?.commentsDisabled || false,
      musicDisabled: video?.musicDisabled || false,
      showCommunity: video?.showCommunity || false,
    });
  }, [video]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  if (video === null || !isOpen) {
    return null;
  }

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(video.id, settings);
      onClose();
    } catch (error) {
      console.error("Error saving video settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="w-full max-w-[520px] bg-[#110018] border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <SettingsIcon className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-white text-xl font-semibold">Video Settings</h2>
          </div>
          <button
            onClick={onClose}
            disabled={isSaving}
            className="text-white/60 hover:text-white transition-colors p-1 disabled:opacity-50"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Title Input */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-white text-sm font-medium">
              Title
            </Label>
            <Input
              id="title"
              value={settings.title}
              onChange={(e) =>
                setSettings({ ...settings, title: e.target.value })
              }
              placeholder="Enter video title"
              className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-purple-500 focus:bg-white/10 rounded-lg h-12 px-4"
            />
          </div>

          {/* Settings Grid */}
          <div className="bg-white/5 border border-white/10 rounded-xl p-5 space-y-5">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-white text-base font-medium">
                  Private Video
                </Label>
                <p className="text-white/60 text-sm">
                  Only accessible via direct link
                </p>
              </div>
              <Switch
                checked={settings.isPrivate}
                onCheckedChange={(checked) =>
                  setSettings({ ...settings, isPrivate: checked })
                }
                className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
              />
            </div>

            <div className="border-t border-white/5 pt-5">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-white text-base font-medium">
                    Disable Comments
                  </Label>
                  <p className="text-white/60 text-sm">
                    Turn off commenting on this video
                  </p>
                </div>
                <Switch
                  checked={settings.commentsDisabled}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, commentsDisabled: checked })
                  }
                  className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                />
              </div>
            </div>

            <div className="border-t border-white/5 pt-5">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-white text-base font-medium">
                    Disable Audio
                  </Label>
                  <p className="text-white/60 text-sm">
                    Remove all sound from this video
                  </p>
                </div>
                <Switch
                  checked={settings.musicDisabled}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, musicDisabled: checked })
                  }
                  className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                />
              </div>
            </div>

            <div className="border-t border-white/5 pt-5">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-white text-base font-medium">
                    Community Enable
                  </Label>
                  <p className="text-white/60 text-sm">
                    Show in community feed
                  </p>
                </div>
                <Switch
                  checked={settings.showCommunity}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, showCommunity: checked })
                  }
                  className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 pt-0">
          <Button
            onClick={onClose}
            disabled={isSaving}
            variant="outline"
            className="flex-1 border-white/20 text-white hover:bg-white/5 hover:border-white/30 rounded-lg py-3 transition-all duration-200 disabled:opacity-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg py-3 transition-all duration-200 disabled:opacity-50"
          >
            {isSaving ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </div>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}