"use client";

import { UserPagination } from "../../_components/user-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ImageIcon } from "lucide-react";
import { format } from "date-fns";

function formatDate(dateValue, formatString = "dd.MM.yyyy") {
  if (!dateValue) return "N/A";

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid date";
    return format(date, formatString);
  } catch (error) {
    console.error("Date formatting error:", error);
    return "Invalid date";
  }
}

type Props = {
  images: {
    url: string;
    name: string;
    id: string;
    createdAt: Date;
    userId: string;
    shortLink: string;
  }[];
  totalImages: number;
  imagePage: number;
  ITEMS_PER_PAGE: number;
};

export default function UserImagesContent({
  images,
  totalImages,
  imagePage,
  ITEMS_PER_PAGE,
}: Props) {
  return (
    <div className="space-y-6">
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Uploaded Images</h2>
          <UserPagination
            currentPage={imagePage}
            totalPages={Math.ceil(totalImages / ITEMS_PER_PAGE)}
            type="image"
          />
        </div>
        {images.length > 0 ? (
          <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-800/40 hover:bg-gray-800/20">
                  <TableHead className="text-gray-300 font-semibold">
                    Preview
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Short Link
                  </TableHead>
                  <TableHead className="text-gray-300 font-semibold">
                    Uploaded
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {images.map((image) => (
                  <TableRow
                    key={image.id}
                    className="border-gray-800/40 hover:bg-gray-800/20"
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <ImageIcon className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-white">
                          {image.name}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <a
                        href={`/i/${image.shortLink}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300 hover:underline transition-colors"
                      >
                        {image.shortLink}
                      </a>
                    </TableCell>
                    <TableCell className="text-gray-300">
                      {formatDate(image.createdAt)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center p-8 bg-gray-900/30 border border-gray-800/40 rounded-xl">
            <p className="text-gray-400">No images found for this user.</p>
          </div>
        )}
      </div>
    </div>
  );
}