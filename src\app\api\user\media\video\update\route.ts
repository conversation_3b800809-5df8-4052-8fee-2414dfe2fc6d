import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";
import {Video} from "@prisma/client";

export async function PATCH(request: NextRequest) {
    const {
        id,
        title,
        isPrivate,
        commentsDisabled,
        musicDisabled,
        showCommunity
    } = await request.json() as Partial<Video>;

    if (!id || !title) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No videoId or title defined."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No user session found."});
    }

    const updateResponse = await fetch(process.env.VIDEO_API_URL + "/videos/" + id + "/settings", {
        method: "PATCH",
        headers: {
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!,
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            title,
            isPrivate,
            commentsDisabled,
            musicDisabled,
            showCommunity
        })
    });

    const data = await updateResponse.json();
    if (!updateResponse.ok) {
        return NextResponse.json({staus: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, data: data});
}