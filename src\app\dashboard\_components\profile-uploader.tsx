"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { Camera, Trash, Loader2, Upload, X } from "lucide-react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";
import { useDropzone } from "react-dropzone";
import { getUserProfilePicture } from "@/server/profile";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface ProfileUploaderProps {
  initialImage?: string | null;
  name?: string | null;
  updateImage: (image: string) => void;
  removeImage: () => void;
  isLoading?: boolean;
  renderTrigger?: () => React.ReactNode;
}

export function ProfileUploader({
  initialImage,
  name,
  updateImage,
  removeImage,
  isLoading,
  renderTrigger,
}: ProfileUploaderProps) {
  const { success, error } = useEnhancedToast();
  const [isUploading, setIsUploading] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  const [currentImage, setCurrentImage] = useState(initialImage);
  const [mounted, setMounted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    setCurrentImage(initialImage);
    setAvatarError(false);
  }, [initialImage]);

  const handleFileUpload = useCallback(
    async (file: File) => {
      if (!file.type.startsWith("image/")) {
        error("Invalid File", "Please upload a valid image.");
        return;
      }

      setIsUploading(true);
      setAvatarError(false);
      try {
          const formData = new FormData();
          formData.append("file", file);

          const response = await fetch("/api/user/profile-picture/upload", {
              method: "POST",
              body: formData
          });

          const responseData = await response.json();
          if (!response.ok) {
              error("Upload Failed", responseData.message);
              return;
          }

          success("Profile Updated", "Profile image updated.");

          const userImage = await getUserProfilePicture(responseData.userId);
          updateImage(userImage);
          router.refresh();
      } catch (err) {
        error("Upload Error", "Failed to upload profile image.");
      } finally {
        setIsUploading(false);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    },
    [updateImage, router],
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (file) handleFileUpload(file);
    },
    [handleFileUpload],
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: { "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"] },
      maxFiles: 1,
      maxSize: 5 * 1024 * 1024,
    });

  const handleRemoveImage = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImage(null);
    await removeImage();
  };

  if (renderTrigger) {
    return (
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {renderTrigger()}
      </div>
    );
  }

  if (!mounted) {
    return (
      <div className="flex flex-col items-center gap-3">
        <div className="w-[80px] h-[80px] md:w-[100px] md:h-[100px] rounded-full bg-white/10 animate-pulse"></div>
        <div className="text-center">
          <span className="text-white/60 text-xs font-medium">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-3">
      <div
        {...getRootProps()}
        className={cn(
          "relative group rounded-full transition-all duration-300 ease-out",
          "w-[80px] h-[80px] md:w-[100px] md:h-[100px]",
          isDragActive &&
            !isDragReject &&
            "scale-110 shadow-2xl shadow-[#B851E0]/50",
          isDragReject && "scale-105 shadow-2xl shadow-red-500/50",
          isLoading && "opacity-50 pointer-events-none",
          !isLoading &&
            "cursor-pointer hover:scale-105 hover:shadow-xl hover:shadow-[#B851E0]/30",
          isDragActive
            ? "border-2 border-[#B851E0] border-dashed"
            : "border-2 border-transparent",
        )}
      >
        {/* Main Avatar Container */}
        <div className="w-full h-full rounded-full overflow-hidden bg-white relative">
          {currentImage && !avatarError ? (
            <Image
              src={currentImage}
              alt={name || "Profile"}
              className="w-full h-full object-cover"
              onError={() => setAvatarError(true)}
              width={200}
              height={200}
            />
          ) : (
            <div className="w-full h-full bg-[#110018] flex items-center justify-center p-4">
              <Image
                src="/assets/images/svg/logo.svg"
                alt="StreamBliss"
                className="w-full h-full object-contain"
                width={100}
                height={100}
              />
            </div>
          )}
        </div>

        {/* Loading Overlay */}
        {(isLoading || isUploading) && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center rounded-full">
            <Loader2 className="h-6 w-6 md:h-8 md:h-8 text-[#B851E0] animate-spin" />
          </div>
        )}

        {/* Drag Active Overlay */}
        {isDragActive && !isDragReject && !isLoading && (
          <div className="absolute inset-0 bg-gradient-to-br from-[#B851E0]/30 to-[#EB489B]/30 flex items-center justify-center rounded-full border-2 border-[#B851E0] border-dashed">
            <div className="text-center">
              <Upload className="h-6 w-6 md:h-8 md:w-8 text-white mx-auto mb-1" />
              <span className="text-xs text-white font-medium">Drop here</span>
            </div>
          </div>
        )}

        {/* Drag Reject Overlay */}
        {isDragReject && (
          <div className="absolute inset-0 bg-red-500/30 flex items-center justify-center rounded-full border-2 border-red-500 border-dashed">
            <div className="text-center">
              <X className="h-6 w-6 md:h-8 md:w-8 text-white mx-auto mb-1" />
              <span className="text-xs text-white font-medium">Invalid</span>
            </div>
          </div>
        )}

        {/* Hover Upload Overlay */}
        {!isDragActive && !isLoading && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200">
            <div className="text-center">
              <Camera className="h-5 w-5 md:h-6 md:w-6 text-white mx-auto mb-1" />
              <span className="text-xs text-white font-medium">
                {currentImage ? "Change" : "Upload"}
              </span>
            </div>
          </div>
        )}

        {/* Remove Button */}
        {currentImage && !isLoading && !isDragActive && (
          <button
            onClick={handleRemoveImage}
            className="absolute -top-1 -right-1 p-1.5 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-110 shadow-lg"
          >
            <Trash className="h-3 w-3 md:h-4 md:w-4" />
          </button>
        )}

        <input {...getInputProps()} />
      </div>

      {/* Status Text */}
      <div className="text-center">
        {isDragActive ? (
          isDragReject ? (
            <span className="text-red-400 text-xs font-medium">
              Invalid file type
            </span>
          ) : (
            <span className="text-[#B851E0] text-xs font-medium animate-pulse">
              Drop your image here
            </span>
          )
        ) : isUploading ? (
          <span className="text-white/60 text-xs font-medium">
            Uploading...
          </span>
        ) : (
          <span className="text-white/60 text-xs font-medium">
            Drag & drop or click to {currentImage ? "change" : "upload"}
          </span>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => e.target.files && handleFileUpload(e.target.files[0])}
        disabled={isUploading}
      />
    </div>
  );
}