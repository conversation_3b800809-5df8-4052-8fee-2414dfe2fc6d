"use server"

import { getUserById } from "@/lib/db/user"
import { rateLimiter } from "@/lib/rate-limit"
import { deleteCommentById, getCommentById, getCommentReportById, updateCommentReportStatus } from "@/server/comment-reports"
import { getClientIp } from "@/server/geolocation"
import { getUserSession } from "@/server/session"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type DeleteCommentParams = {
  commentId: string
  reportId: string
}

type Callback = {
  success: boolean
  message: string
}

export async function submitDeleteComment({ commentId, reportId }: DeleteCommentParams): Promise<Callback> {
  const ip = await getClientIp()
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later.",
    }
  }

  try {
    if (!commentId || !reportId) {
      return {
        success: false,
        message: "Comment ID and Report ID are required.",
      }
    }

    const userSession = await getUserSession()
    if (!userSession) {
      return {
        success: false,
        message: "User session not found. Please log in again.",
      }
    }

    const userId = userSession.userId
    const user = await getUserById(userId)
    if (!user) {
      return {
        success: false,
        message: "User not found. Please log in again.",
      }
    }

    const commentData = await getCommentById(commentId)
    if (!commentData) {
      return {
        success: false,
        message: "Comment not found.",
      }
    }

    const reportData = await getCommentReportById(reportId)
    if (!reportData) {
      return {
        success: false,
        message: "Report not found.",
      }
    }

    await deleteCommentById(commentId);
    await updateCommentReportStatus(reportId, "CLOSED");
    await createLog(user.id, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_VIDEO_DELETE_COMMENT, LogActions.VIDEO);

    return {
      success: true,
      message: "Comment has been deleted and the report has been resolved.",
    }
  } catch (error) {
    console.error("Error deleting comment:", error)
    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    }
  }
}