"use client";

import { motion, AnimatePresence } from "framer-motion";
import { LogOut, CheckCircle2, <PERSON>ader2, Arrow<PERSON>eft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import Image from "next/image";

interface SignoutClientProps {
  user: {
    name: string;
    email: string;
  };
}

type SignoutStep = "confirm" | "processing" | "success";

export default function SignoutClient({ user }: SignoutClientProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [step, setStep] = useState<SignoutStep>("confirm");

  const handleSignout = async () => {
    setStep("processing");

    try {
      const request = await fetch('/api/auth/logout', { method: 'POST' });
      if (request.status === 200) {
        setStep('success');
        success('Successfully signed out', 'You will be redirected to the login page');

        setTimeout(() => {
          router.push('/login');
        }, 2000);

        return;
      }

      error('Error logging out', 'Error while signing out');
    } catch (err) {
      error("Sign out failed", "An unexpected error occurred. Please try again.");
      setStep("confirm");
      console.error("Sign out error:", err);
    }
  };

  const handleCancel = () => {
    router.push("/dashboard");
  };

  return (
    <div className="min-h-screen bg-black relative flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8 font-['Montserrat']">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Hero Background Pattern */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: "url('/assets/images/webp/hero-bg.webp')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-[#b851e0]/20 via-transparent to-[#eb489b]/20" />
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute w-[600px] h-[600px] rounded-full bg-gradient-to-r from-[#b851e0]/30 to-[#eb489b]/30 blur-3xl -top-48 -left-48"
        />
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
          className="absolute w-[500px] h-[500px] rounded-full bg-gradient-to-r from-[#eb489b]/20 to-[#b851e0]/20 blur-3xl -bottom-48 -right-48"
        />
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-white/10"
            style={{
              width: `${Math.random() * 6 + 2}px`,
              height: `${Math.random() * 6 + 2}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 w-full max-w-lg mx-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-8 gap-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleCancel}
            className="group flex items-center gap-2 px-3 sm:px-4 py-2 rounded-xl bg-white/[0.08] border border-white/20 text-white/70 hover:text-white hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 font-medium backdrop-blur-[16px] text-sm sm:text-base"
          >
            <ArrowLeft className="h-4 w-4 group-hover:-translate-x-0.5 transition-transform duration-200 flex-shrink-0" />
            <span className="hidden sm:inline">Back to Dashboard</span>
            <span className="sm:hidden">Back</span>
          </motion.button>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex items-center flex-shrink-0"
          >
            <Image
              src="/assets/images/svg/footer-logo.svg"
              alt="StreamBliss"
              width={200}
              height={40}
              className="h-6 sm:h-8 w-auto"
              priority
            />
          </motion.div>
        </div>

        {/* Main Card */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative backdrop-blur-[20px] bg-white/[0.08] border border-white/20 rounded-3xl shadow-2xl shadow-black/50 overflow-hidden"
        >
          {/* Card Border Glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#b851e0]/20 to-[#eb489b]/20 rounded-3xl blur-xl" />

          {/* Inner glow */}
          <div className="absolute inset-[1px] bg-gradient-to-b from-white/[0.1] to-transparent rounded-3xl pointer-events-none" />

          <div className="relative p-8 sm:p-10">
            <AnimatePresence mode="wait">
              {step === "confirm" && (
                <motion.div
                  key="confirm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                  className="text-center"
                >
                  {/* Icon */}
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="flex items-center justify-center mb-6"
                  >
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#b851e0] to-[#eb489b] flex items-center justify-center shadow-lg shadow-[#b851e0]/50">
                        <LogOut className="h-10 w-10 text-white" />
                      </div>
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#b851e0] to-[#eb489b] opacity-30 blur-md" />
                    </div>
                  </motion.div>

                  {/* Title & Description */}
                  <motion.h1
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.3 }}
                    className="text-3xl font-bold text-white mb-4"
                  >
                    Sign Out
                  </motion.h1>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 }}
                    className="text-white/70 text-lg mb-8 leading-relaxed max-w-sm mx-auto"
                  >
                    Are you sure you want to sign out of your account?
                  </motion.p>

                  {/* User Info Card */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.5 }}
                    className="relative backdrop-blur-[16px] bg-white/[0.06] border border-white/20 rounded-2xl p-6 mb-8 group hover:bg-white/[0.08] transition-all duration-300"
                  >
                    {/* Subtle inner glow */}
                    <div className="absolute inset-[1px] bg-gradient-to-b from-white/[0.05] to-transparent rounded-2xl pointer-events-none" />

                    <div className="relative flex items-center gap-4">
                      <div className="relative">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#b851e0] to-[#eb489b] flex items-center justify-center text-xl font-bold text-white shadow-lg ring-2 ring-white/20">
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#b851e0] to-[#eb489b] opacity-30 blur-md" />
                      </div>
                      <div className="text-left flex-1 min-w-0">
                        <div className="text-white font-semibold text-lg truncate">
                          {user.name}
                        </div>
                        <div className="text-white/60 text-sm truncate">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Action Buttons */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.6 }}
                    className="space-y-3"
                  >
                    <motion.button
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleSignout}
                      className="relative w-full h-14 bg-gradient-to-r from-[#b851e0] to-[#eb489b] hover:from-[#a641d0] hover:to-[#da3a8b] text-white font-semibold text-lg rounded-2xl transition-all duration-300 shadow-lg shadow-[#b851e0]/30 overflow-hidden group"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <span className="relative flex items-center justify-center gap-2">
                        <LogOut className="h-5 w-5" />
                        Sign Out
                      </span>
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleCancel}
                      className="relative w-full h-14 bg-white/[0.08] border border-white/30 text-white font-semibold text-lg rounded-2xl hover:bg-white/[0.12] hover:border-white/40 transition-all duration-300 backdrop-blur-[16px] overflow-hidden group"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <span className="relative">Cancel</span>
                    </motion.button>
                  </motion.div>
                </motion.div>
              )}

              {step === "processing" && (
                <motion.div
                  key="processing"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                  className="text-center py-8"
                >
                  {/* Loading Icon */}
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                    className="flex items-center justify-center mb-6"
                  >
                    <div className="relative">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "linear",
                        }}
                        className="w-20 h-20 rounded-full bg-gradient-to-br from-[#b851e0] to-[#eb489b] flex items-center justify-center shadow-lg shadow-[#b851e0]/50"
                      >
                        <Loader2 className="h-10 w-10 text-white" />
                      </motion.div>
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#b851e0] to-[#eb489b] opacity-30 blur-md" />
                    </div>
                  </motion.div>

                  {/* Loading Text */}
                  <motion.h2
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 }}
                    className="text-2xl font-bold text-white mb-3"
                  >
                    Signing Out...
                  </motion.h2>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.3 }}
                    className="text-white/70 text-lg leading-relaxed"
                  >
                    Please wait while we securely sign you out
                  </motion.p>
                </motion.div>
              )}

              {step === "success" && (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                  className="text-center py-8"
                >
                  {/* Success Icon */}
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 200,
                      damping: 10,
                      delay: 0.1,
                    }}
                    className="flex items-center justify-center mb-6"
                  >
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg shadow-green-500/50">
                        <CheckCircle2 className="h-10 w-10 text-white" />
                      </div>
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-green-400 to-green-600 opacity-30 blur-md" />
                    </div>
                  </motion.div>

                  {/* Success Text */}
                  <motion.h2
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.3 }}
                    className="text-2xl font-bold text-white mb-3"
                  >
                    Successfully Signed Out
                  </motion.h2>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 }}
                    className="text-white/70 text-lg mb-6 leading-relaxed"
                  >
                    Thank you for using StreamBliss
                  </motion.p>

                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.5 }}
                    className="text-white/50 text-sm"
                  >
                    Redirecting to login page...
                  </motion.p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}