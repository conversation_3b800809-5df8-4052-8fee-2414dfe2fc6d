"use server";

import { prisma } from "@/lib/prisma";

const API_URL = process.env.VIDEO_API_URL || "http://localhost:9999";

export const getImageDataByShortId = async (shortId: string) => {
  return await prisma.image.findFirst({
    where: {
      shortLink: shortId,
    },
    include: { user: true }
  });
}

export const requestImage = async (userId: string, imageId: string) => {
  const imageData = await prisma.image.findUnique({
    where: {
      id: imageId,
    },
  });

  if (imageData == null) return "";

  return `${API_URL}/images/stream/${userId}/${imageData.id}`;
};

export const getImageByUserId = async (userId: string, options?: { take?: number; skip?: number }) => {
  const images = await prisma.image.findMany({
    where: {
      userId: userId,
    },
    select: {
      id: true,
      name: true,
      shortLink: true,
      createdAt: true,
      userId: true,
    },
    take: options?.take,
    skip: options?.skip,
    orderBy: {
      createdAt: "desc",
    },
  });

  return images.map((image) => ({
    ...image,
    url: `${API_URL}/image/${image.userId}/${image.name}`,
  }));
};
