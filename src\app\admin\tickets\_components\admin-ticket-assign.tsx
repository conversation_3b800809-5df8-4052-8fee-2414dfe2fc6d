"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import * as select from "@/components/ui/select";
interface AdminUser {
  id: string;
  name: string | null;
  email: string;
  image?: string | null;
  roleId?: number;
}

interface AdminTicketAssignProps {
  ticketId: string;
  admins: AdminUser[];
  currentAssignee: AdminUser | null;
  onAssign: (assigneeId: string | null) => void;
  disabled?: boolean;
}

export function AdminTicketAssign({
  admins,
  currentAssignee,
  onAssign,
  disabled,
}: AdminTicketAssignProps) {
  const handleValueChange = (value: string) => {
    onAssign(value === "unassigned" ? null : value);
  };

  return (
    <div className="space-y-2">
      <select.Select
        defaultValue={currentAssignee?.id || "unassigned"}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <select.SelectTrigger className="w-full bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40 h-9">
          <select.SelectValue>
            {currentAssignee ? (
              <div className="flex items-center gap-2">
                <Avatar className="h-4 w-4">
                  {currentAssignee.image ? (
                    <AvatarImage
                      src={currentAssignee.image}
                      alt={currentAssignee.name || "Admin"}
                    />
                  ) : (
                    <AvatarFallback className="bg-purple-500/20 text-purple-400 text-xs">
                      {currentAssignee.name?.[0] ||
                        currentAssignee.email?.charAt(0) ||
                        "A"}
                    </AvatarFallback>
                  )}
                </Avatar>
                <span className="text-white text-sm">
                  {currentAssignee.name || currentAssignee.email}
                </span>
              </div>
            ) : (
              <span className="text-gray-400">Unassigned</span>
            )}
          </select.SelectValue>
        </select.SelectTrigger>
        <select.SelectContent className="bg-gray-950 border-gray-800/40">
          <select.SelectItem
            value="unassigned"
            className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
          >
            Unassigned
          </select.SelectItem>
          {admins.map((admin) => (
            <select.SelectItem
              key={admin.id}
              value={admin.id}
              className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
            >
              <div className="flex items-center gap-2">
                <Avatar className="h-4 w-4">
                  {admin.image ? (
                    <AvatarImage
                      src={admin.image}
                      alt={admin.name || "Admin"}
                    />
                  ) : (
                    <AvatarFallback className="bg-purple-500/20 text-purple-400 text-xs">
                      {admin.name?.[0] || admin.email?.charAt(0) || "A"}
                    </AvatarFallback>
                  )}
                </Avatar>
                <span>{admin.name || admin.email}</span>
              </div>
            </select.SelectItem>
          ))}
        </select.SelectContent>
      </select.Select>
    </div>
  );
}