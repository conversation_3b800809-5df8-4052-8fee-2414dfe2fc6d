"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Mail,
  Clock,
  Send,
  Building,
  Loader2,
  MapPin,
  Phone,
  MessageCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import AOS from "aos";
import "aos/dist/aos.css";

const contactInfo = [
  {
    icon: Mail,
    title: "Email",
    content: "<EMAIL>",
    href: "mailto:<EMAIL>",
    description: "Drop us a line anytime",
    gradient: "from-blue-400 to-cyan-400",
  },
  {
    icon: Building,
    title: "Office",
    content: ["Europaring 90", "53757 Sankt Augustin", "Germany"],
    description: "Visit our headquarters",
    gradient: "from-emerald-400 to-teal-400",
  },
  {
    icon: Clock,
    title: "Support Hours",
    content: ["Monday - Friday", "9:00 AM - 6:00 PM CET"],
    description: "We're here to help",
    gradient: "from-orange-400 to-red-400",
  },
];

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Please enter a valid email address."),
  subject: z.string().min(5, "Subject must be at least 5 characters."),
  message: z.string().min(10, "Message must be at least 10 characters."),
});

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success, error } = useEnhancedToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: "", email: "", subject: "", message: "" },
  });

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  async function onSubmit() {
    setIsSubmitting(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      success("Message Sent!", "We'll get back to you as soon as possible.");
      form.reset();
    } catch (err) {
      error("Send Failed", "Something went wrong. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Get in Touch
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Let&apos;s Start a
          <br />
          Conversation
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          We&apos;d love to hear from you. Send us a message and we&apos;ll
          respond as soon as possible.
        </p>
      </motion.div>

      <div className="grid gap-12 lg:gap-16 lg:grid-cols-2">
        {/* Contact Information */}
        <motion.div
          className="space-y-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div data-aos="fade-right" data-aos-duration="600">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Contact Information
            </h2>
            <p className="text-lg text-gray-400 mb-8">
              Choose the best way to reach us
            </p>
          </div>

          {contactInfo.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
              data-aos="fade-right"
              data-aos-duration="800"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div className="flex items-start gap-4">
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${item.gradient} bg-opacity-20 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <item.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-white mb-1">
                      {item.title}
                    </h3>
                    <p className="text-sm text-gray-400 mb-2">
                      {item.description}
                    </p>
                    <div className="text-gray-300">
                      {Array.isArray(item.content) ? (
                        item.content.map((line, i) => (
                          <p key={i} className="text-sm">
                            {line}
                          </p>
                        ))
                      ) : (
                        <a
                          href={item.href}
                          className="text-purple-400 hover:text-purple-300 transition-colors font-medium"
                        >
                          {item.content}
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Contact Form */}
        <motion.div
          className="relative"
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-2xl" />
          <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-10">
            <div className="mb-8" data-aos="fade-left" data-aos-duration="600">
              <h2 className="text-3xl font-bold mb-2 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Send us a message
              </h2>
              <p className="text-gray-400">
                Fill out the form below and we&apos;ll get back to you shortly.
              </p>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white font-medium">
                          Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Your name"
                            {...field}
                            className="backdrop-blur-md bg-white/5 border-white/10 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400 rounded-xl h-12"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white font-medium">
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            className="backdrop-blur-md bg-white/5 border-white/10 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400 rounded-xl h-12"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white font-medium">
                        Subject
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="How can we help?"
                          {...field}
                          className="backdrop-blur-md bg-white/5 border-white/10 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400 rounded-xl h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white font-medium">
                        Message
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell us more..."
                          className="min-h-[150px] backdrop-blur-md bg-white/5 border-white/10 focus:border-purple-400 focus:ring-purple-400/20 text-white placeholder:text-gray-400 rounded-xl resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-3 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-5 w-5" />
                      Send Message
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </div>
        </motion.div>
      </div>

      {/* Additional CTA */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="inline-block p-6 backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl">
          <p className="text-gray-300 text-lg mb-4">
            Prefer to chat? We&apos;re also available on
          </p>
          <div className="flex items-center justify-center gap-4">
            <Button
              asChild
              variant="outline"
              className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10 hover:border-purple-400 transition-all duration-300"
            >
              <a
                href="https://discord.gg/streambliss"
                target="_blank"
                rel="noopener noreferrer"
              >
                <MessageCircle className="mr-2 h-4 w-4" />
                Discord
              </a>
            </Button>
            <Button
              variant="outline"
              className="border-pink-500/30 text-pink-400 hover:bg-pink-500/10 hover:border-pink-400 transition-all duration-300"
            >
              <MessageCircle className="mr-2 h-4 w-4" />
              Twitter
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}