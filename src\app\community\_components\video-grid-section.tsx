"use client"

import { VideoMasonryGrid } from "./VideoMasonryGrid"
import type { Video } from "@/types/video"

interface VideoGridSectionProps {
  videos: Video[]
}

export function VideoGridSection({ videos }: VideoGridSectionProps) {
  return (
    <section className="mt-8 mb-4">
      <h2 className="text-2xl font-bold text-purple-400 mb-6">
        Latest Videos
      </h2>
      <VideoMasonryGrid 
        videos={videos} 
        onPlayVideo={(video) => {
        }}
      />
    </section>
  )
} 