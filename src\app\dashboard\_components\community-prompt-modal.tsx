"use client";

import { useEffect, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Users, X } from "lucide-react";
interface CommunityPromptModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (publish: boolean) => void;
}

export function CommunityPromptModal({
  open,
  onOpenChange,
  onConfirm,
}: CommunityPromptModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);

  const handleCancel = useCallback(() => {
    onOpenChange(false);
    onConfirm(false);
  }, [onOpenChange, onConfirm]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleCancel();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleCancel();
      }
    };

    if (open) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open, onOpenChange, onConfirm, handleCancel]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="w-full max-w-[480px] bg-[#110018] border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-white text-xl font-semibold">
            Publish to Community?
          </h2>
          <button
            onClick={handleCancel}
            className="text-white/60 hover:text-white transition-colors p-1"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="flex flex-col items-center text-center space-y-6">
            {/* Icon */}
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>

            {/* Description */}
            <div className="space-y-3">
              <p className="text-white text-base leading-relaxed">
                Your video will be submitted for review and once approved, it
                will be visible on the community page.
              </p>
              <p className="text-white/60 text-sm">
                This helps other users discover your content and builds the
                community.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                onClick={() => {
                  onOpenChange(false);
                  onConfirm(true);
                }}
                className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg py-3 transition-all duration-200"
              >
                Yes, Publish to Community
              </Button>

              <Button
                onClick={handleCancel}
                variant="outline"
                className="flex-1 border-white/20 text-white hover:bg-white/5 hover:border-white/30 rounded-lg py-3 transition-all duration-200"
              >
                Keep Private
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}