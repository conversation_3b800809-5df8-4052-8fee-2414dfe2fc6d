"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { OTPInput } from "@/components/ui/otp-input";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Mail, RotateCcw, Shield } from "lucide-react";
import { motion } from "framer-motion";
import AOS from "aos";
import "aos/dist/aos.css";
import gsap from "gsap";
import Heading from "@/components/common/Heading";
import Description from "@/components/common/Description";
import Icons from "@/components/common/Icons";
import {useRouter} from "next/navigation";
import compareEllips from "../../../../../public/assets/images/webp/compare-left-img.webp";

type EmailVerificationProps = {
  email: string;
};

export default function VerifyMailClient({ email }: EmailVerificationProps) {
  const [isVerifying, setIsVerifying] = useState(false);
  const router = useRouter();
  const [isResending, setIsResending] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [floatingElements, setFloatingElements] = useState<
    Array<{
      width: number;
      height: number;
      left: number;
      top: number;
      duration: number;
      delay: number;
    }>
  >([]);
  const { success, error } = useEnhancedToast();
  const leftBadgeRef = useRef(null);
  const rightBadgeRef = useRef(null);
  const leftContainerRef = useRef(null);
  const rightContainerRef = useRef(null);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });

    const elements = Array.from({ length: 20 }, () => ({
      width: Math.random() * 4 + 2,
      height: Math.random() * 4 + 2,
      left: Math.random() * 100,
      top: Math.random() * 100,
      duration: Math.random() * 10 + 15,
      delay: Math.random() * 5,
    }));
    setFloatingElements(elements);
  }, []);

  const handleBadgeMouseMove = (e: any, badgeRef: any, containerRef: any) => {
    if (!badgeRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const badge = badgeRef.current;

    const containerRect = container.getBoundingClientRect();
    const centerX = containerRect.left + containerRect.width / 2;
    const centerY = containerRect.top + containerRect.height / 2;

    const x = (e.clientX - centerX) * 0.3;
    const y = (e.clientY - centerY) * 0.3;

    const maxMove = 40;
    const limitedX = Math.max(-maxMove, Math.min(maxMove, x));
    const limitedY = Math.max(-maxMove, Math.min(maxMove, y));

    gsap.to(badge, {
      x: limitedX,
      y: limitedY,
      duration: 0.3,
      ease: "power2.out",
    });
  };

  const handleBadgeMouseLeave = (badgeRef: any) => {
    if (!badgeRef.current) return;

    gsap.to(badgeRef.current, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
    });
  };

  const handleVerify = async (otp: string) => {
    setIsVerifying(true);
    try {
      const request = await fetch("/api/auth/verify/email", {
        method: "POST",
        body: JSON.stringify({
          email,
          token: otp
        })
      })

      const data = await request.json();
      if (data.status === 200) {
        router.push("/dashboard");
      }
    } catch (err) {
      error("Verification Error", "Error sending verification request");
    }
  };

  const resendVerification = async () => {
    setIsResending(true);

    try {
      const request = await fetch("/api/auth/verify/send-email", {
        method: "POST",
        body: JSON.stringify({
          email,
        })
      })

      const data = await request.json();
      if (data.status === 200) {
        success("Verification Sent", "Successfully resent verification token");
      } else {
        error("Send Failed", "Failed to resend verification token");
      }
    } catch (err) {
      error("Send Error", "Error resending verification token");
    } finally {
      setIsResending(false);
    }
  };

  const maskedEmail = email.replace(/(.{2})(.*)(@.*)/, "$1***$3");

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-dark-purple via-custom-black to-light-purple">
      {/* Background Ellipse */}
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-events-none top-[-5%] max-lg:hidden z-[0] opacity-60"
        src={compareEllips}
        alt="background ellipse"
      />

      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {floatingElements.map((element, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-custom-purple to-custom-pink opacity-20"
            style={{
              width: `${element.width}px`,
              height: `${element.height}px`,
              left: `${element.left}%`,
              top: `${element.top}%`,
              animation: `float ${element.duration}s linear infinite`,
              animationDelay: `${element.delay}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-8">
        <div className="max-w-6xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            {/* Left Side - Branding & Features */}
            <div className="relative">
              {/* Floating Badges */}
              <div
                ref={leftContainerRef}
                className="absolute max-lg:hidden top-[-10%] right-[10%] z-10"
                data-aos="fade-right"
                data-aos-duration={1050}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, leftBadgeRef, leftContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(leftBadgeRef)}
              >
                <div
                  ref={leftBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[226px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-2.5 cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="secureStorage" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute top-[-40%] end-[-6%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Secure Email
                  </p>
                </div>
              </div>

              <div
                ref={rightContainerRef}
                className="absolute max-lg:hidden top-[-8%] left-[-5%] z-10"
                data-aos="fade-left"
                data-aos-duration={1300}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, rightBadgeRef, rightContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(rightBadgeRef)}
              >
                <div
                  ref={rightBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[264px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-[9px] cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="effort" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute rotate-[-90deg] top-[-38%] start-[-5%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Quick Access
                  </p>
                </div>
              </div>

              {/* Main Content */}
              <div data-aos="zoom-in" data-aos-duration={500}>
                <Link href="/" className="flex items-center mb-8 lg:mb-12">
                  <Image
                    src="/assets/images/svg/footer-logo.svg"
                    alt="StreamBliss"
                    width={180}
                    height={40}
                    className="h-10 lg:h-12 w-auto"
                  />
                </Link>

                <Heading
                  variant="6xl"
                  className="!text-left !mb-6 max-w-[500px] !font-bold"
                >
                  Verify Your Email Address
                </Heading>

                <Description className="!text-left max-w-[480px] mb-8 !opacity-80">
                  We&apos;ve sent a secure 6-digit verification code to your
                  email address. Enter the code below to verify your account and
                  complete your registration.
                </Description>

                {/* Feature highlights */}
                <div className="space-y-4 max-lg:hidden">
                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={200}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="secureStorage" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Secure email verification
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={400}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="effort" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Instant account access
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={600}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="easySharing" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Access to your dashboard
                    </span>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Right Side - Email Verification Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full max-w-md mx-auto lg:max-w-none"
            >
              <div className="relative">
                <div className="backdrop-blur-md bg-white/5 rounded-2xl border border-white/10 p-8 shadow-2xl relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  <div className="relative z-10">
                    <div className="mb-8 text-center lg:text-left">
                      <div className="flex items-center gap-3 mb-4 justify-center lg:justify-start">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                          <Mail className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h1 className="text-2xl lg:text-3xl font-bold text-white">
                            Verify Your Email
                          </h1>
                        </div>
                      </div>
                      <p className="text-white/70 mb-4">
                        We&apos;ve sent a 6-digit verification code to
                      </p>
                      <div className="inline-flex items-center px-4 py-2 bg-white/5 border border-white/10 rounded-xl">
                        <span className="text-custom-purple font-medium">
                          {maskedEmail}
                        </span>
                      </div>
                    </div>

                    {/* OTP Input */}
                    <div className="space-y-6">
                      <div className="text-center">
                        <label className="text-white font-medium text-sm block mb-4">
                          Enter verification code
                        </label>
                        <div className="flex justify-center mb-4">
                          <OTPInput
                            length={6}
                            onComplete={handleVerify}
                            value={otpValue}
                            onChange={setOtpValue}
                            disabled={isVerifying}
                          />
                        </div>

                        {/* Manual Verify Button */}
                        {otpValue.length === 6 && !isVerifying && (
                          <button
                            onClick={() => handleVerify(otpValue)}
                            className="w-full h-12 bg-gradient-to-r from-custom-purple to-custom-pink hover:from-custom-purple/80 hover:to-custom-pink/80 text-white font-semibold rounded-xl transition-all duration-300"
                          >
                            Verify Code
                          </button>
                        )}
                      </div>

                      {/* Verifying State */}
                      {isVerifying && (
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            gap: "12px",
                            padding: "16px",
                            backgroundColor: "rgba(147, 51, 234, 0.1)",
                            border: "1px solid rgba(147, 51, 234, 0.2)",
                            borderRadius: "12px",
                            marginBottom: "24px",
                          }}
                        >
                          <div
                            style={{
                              width: "24px",
                              height: "24px",
                              border: "3px solid #ffffff",
                              borderTop: "3px solid transparent",
                              borderRadius: "50%",
                              animation: "spin 1s linear infinite",
                            }}
                          ></div>
                          <span style={{ color: "#ffffff", fontWeight: "500" }}>
                            Verifying your code...
                          </span>
                        </div>
                      )}

                      <style
                        dangerouslySetInnerHTML={{
                          __html: `
                          @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                          }
                        `,
                        }}
                      />

                      {/* Resend Button */}
                      <div className="text-center">
                        <p className="text-white/60 text-sm mb-4">
                          Didn&apos;t receive the code?
                        </p>
                        <button
                          onClick={resendVerification}
                          disabled={isResending}
                          className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-custom-purple/20 to-custom-pink/20 hover:from-custom-purple/30 hover:to-custom-pink/30 border border-custom-purple/30 hover:border-custom-purple/50 rounded-xl text-custom-purple font-medium transition-all duration-300 backdrop-blur-sm disabled:opacity-50"
                        >
                          {isResending ? (
                            <>
                              <div className="w-4 h-4 border-2 border-custom-purple/30 border-t-custom-purple rounded-full animate-spin" />
                              Resending...
                            </>
                          ) : (
                            <>
                              <RotateCcw className="w-4 h-4" />
                              Resend Code
                            </>
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Security Notice */}
                    <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                      <div className="flex items-start gap-3">
                        <Shield className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="text-blue-400 font-medium text-sm mb-1">
                            Security Notice
                          </h4>
                          <p className="text-white/70 text-xs leading-relaxed">
                            This code will expire in 15 minutes. Never share
                            your verification code with anyone.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Additional links */}
                    <div className="mt-8 text-center space-y-3">
                      <p className="text-sm text-white/60">
                        Wrong email address?{" "}
                        <Link
                          href="/register"
                          className="text-custom-purple hover:text-custom-pink transition-colors font-medium"
                        >
                          Use a different email
                        </Link>
                      </p>

                      <div className="flex justify-center space-x-6 text-xs text-white/50">
                        <Link
                          href="/privacy"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Privacy
                        </Link>
                        <Link
                          href="/terms"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Terms
                        </Link>
                        <Link
                          href="/help"
                          className="hover:text-custom-purple transition-colors"
                        >
                          Help
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-custom-purple to-custom-pink rounded-full opacity-20 blur-xl" />
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-custom-pink to-custom-purple rounded-full opacity-15 blur-xl" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating animations */}
      <style>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          25% {
            transform: translateY(-20px) rotate(90deg);
          }
          50% {
            transform: translateY(-10px) rotate(180deg);
          }
          75% {
            transform: translateY(-30px) rotate(270deg);
          }
        }
      `}</style>
    </div>
  );
}