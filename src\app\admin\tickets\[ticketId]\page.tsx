import { getAdminUser } from "@/server/session"
import { prisma } from "@/lib/prisma"
import { notFound, redirect } from "next/navigation"
import { Shell } from "@/components/shell"
import AdminTicketDetailClient from "./client"

interface AdminUser {
  id: string
  name: string | null
  email: string
  image?: string | null
  roleId?: number
}

async function getTicketManagers(): Promise<AdminUser[]> {
  const admins = await prisma.user.findMany({
    where: {
      OR: [
        { roleId: 2 },
        {
          role: {
            permissions: {
              some: {
                permission: {
                  name: "TICKET_MANAGEMENT",
                },
              },
            },
          },
        },
      ],
    },
    select: {
      id: true,
      name: true,
      email: true,
      Image: true,
      roleId: true,
    },
  })

  return admins
}

export default async function AdminTicketPage(props: {params: Promise<{ ticketId: string }> }) {
  const params = await props.params;
  const admin = await getAdminUser(["TICKET_MANAGEMENT"])
  if (!admin) {
    redirect("/admin")
  }

  const ticket = await prisma.ticket.findUnique({
    where: {
      id: params.ticketId,
    },
    include: {
      user: true,
      responses: {
        include: {
          user: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      },
      activities: {
        include: {
          user: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      },
    },
  })

  if (!ticket) {
    notFound()
  }

  const ticketManagers = await getTicketManagers()

  const assignedUser = ticket.assignedTo
    ? ticketManagers.find(manager => manager.id === ticket.assignedTo) || null
    : null

  const ticketWithAssignedUser = {
    ...ticket,
    assignedTo: assignedUser
  }

  return (
    <Shell className="max-w-4xl py-6 h-full overflow-y-auto">
      <AdminTicketDetailClient initialTicket={ticketWithAssignedUser} userId={admin.id} ticketManagers={ticketManagers} />
    </Shell>
  )
}