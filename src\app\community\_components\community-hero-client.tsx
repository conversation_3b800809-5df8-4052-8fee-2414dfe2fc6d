"use client";

import { useState, useRef, useEffect } from "react";

import { Button } from "@/components/ui/button";
import { Eye, Clock, User, Upload, Play, Star } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type { Video } from "@/types/video";
import Image from "next/image";

const API_URL = process.env.VIDEO_API_URL || "https://api.streambliss.cloud";

export function CommunityHeroClient({ topVideo }: { topVideo: Video }) {
  const router = useRouter();

  const [hovered, setHovered] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  const [imgError, setImgError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (hovered) {
      const timer = setTimeout(() => {
        setVideoVisible(true);
      }, 50);
      return () => clearTimeout(timer);
    } else {
      setVideoVisible(false);
    }
  }, [hovered]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (videoVisible) {
      video.muted = true;
      video.currentTime = 0;

      video.play().catch((err) => {
        console.log("Simple play error:", err.message);
      });
    } else {
      if (!video.paused) {
        video.pause();
      }
    }
  }, [videoVisible]);

  if (!topVideo) {
    return (
      <div className="relative overflow-hidden bg-gradient-to-br from-slate-950 via-purple-950/30 to-slate-950 py-32">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-spin-slow"></div>
        </div>

        <div className="relative z-10 text-center px-4">
          <div className="mb-6">
            <Star className="w-16 h-16 text-purple-400 mx-auto mb-4 animate-pulse" />
          </div>
          <h1 className="text-6xl md:text-7xl font-bold tracking-tight mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
            Welcome to StreamBliss
          </h1>
          <p className="text-xl md:text-2xl text-slate-300 max-w-3xl mx-auto mb-12 leading-relaxed">
            Discover amazing content from our creative community. Be the first
            to share your story with the world.
          </p>
          <Button
            className="rounded-full bg-gradient-to-r from-purple-600 via-purple-500 to-pink-500 hover:from-purple-700 hover:via-purple-600 hover:to-pink-600 text-white text-lg px-10 py-6 shadow-2xl shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 transform hover:scale-105"
            onClick={() => (window.location.href = "/dashboard")}
          >
            <Upload className="w-6 h-6 mr-3" />
            Start Creating
          </Button>
        </div>
      </div>
    );
  }

  const thumbnail = `${API_URL}/video/thumbnail/${topVideo.userId}/${topVideo.id}`;
  const videoSrc = `${API_URL}/video/stream/${topVideo.userId}/${topVideo.id}`;
  const uploader = topVideo.user?.name || "Unknown";
  const uploaderImage = topVideo.user?.image;
  const formattedDate = formatDistanceToNow(new Date(topVideo.createdAt), {
    addSuffix: true,
  });

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-950 via-purple-950/30 to-slate-950 py-20 md:py-32">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-stretch gap-16 min-h-[420px]">
          {/* Video Preview */}
          <div className="flex-1 flex justify-center h-full">
            <div
              className="relative rounded-2xl overflow-hidden shadow-2xl border border-purple-500/20 w-full max-w-2xl aspect-video bg-slate-900/50 group cursor-pointer h-full"
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              onClick={() => router.push(`/v/${topVideo.shortLink}`)}
            >
              {imgError ? (
                <div className="absolute inset-0 w-full h-full bg-slate-800 flex items-center justify-center">
                  <div className="text-center">
                    <Play className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                    <span className="text-slate-400 text-lg">
                      Video unavailable
                    </span>
                  </div>
                </div>
              ) : (
                <div className="relative w-full h-full">
                  <Image
                    src={thumbnail || "/placeholder.svg"}
                    alt={topVideo.title}
                    fill
                    priority
                    sizes="(max-width: 768px) 100vw, 50vw"
                    className="object-cover transition-all duration-700 group-hover:scale-105"
                    onError={() => setImgError(true)}
                  />
                </div>
              )}

              {videoVisible && (
                <video
                  ref={videoRef}
                  src={videoSrc}
                  poster={thumbnail}
                  className="absolute inset-0 w-full h-full object-cover transition-all duration-700 group-hover:scale-105 z-[1]"
                  muted
                  loop
                  playsInline
                  preload="metadata"
                />
              )}

              {/* Gradient overlays */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-[2]" />
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 via-transparent to-pink-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-[2]" />
              <div
                className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 z-[3] ${hovered ? "opacity-0" : "opacity-100"}`}
              >
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-2xl group-hover:scale-110 transition-all duration-300 backdrop-blur-sm border border-white/20">
                  <Play
                    className="w-10 h-10 text-white ml-1"
                    fill="currentColor"
                  />
                </div>
              </div>

              {/* Stats overlay */}
              <div className="absolute top-4 right-4 z-[3]">
                <div className="bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium flex items-center gap-2 border border-white/10">
                  <Eye className="h-4 w-4" />
                  {formatViews(topVideo.views)}
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 text-center lg:text-left flex flex-col justify-center h-full">
            <div className="mb-4">
              <span className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 px-4 py-2 rounded-full text-sm font-medium border border-purple-500/30">
                <Star className="w-4 h-4" />
                Featured Video
              </span>
            </div>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent leading-tight">
              {topVideo.title}
            </h2>

            <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 mb-8 text-lg">
              <Link
                href={`/community/u/${uploader}`}
                className="flex items-center gap-3 group/avatar hover:opacity-80 transition-all duration-300"
                onClick={(e) => e.stopPropagation()}
              >
                {uploaderImage && !avatarError ? (
                  <div className="relative w-12 h-12">
                    <Image
                      src={uploaderImage || "/placeholder.svg"}
                      alt={uploader}
                      fill
                      className="rounded-full object-cover border-2 border-purple-400/50 shadow-lg group-hover/avatar:border-purple-400 transition-colors"
                      onError={() => setAvatarError(true)}
                    />
                  </div>
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border-2 border-purple-400/50 shadow-lg">
                    <User className="text-xl text-white" />
                  </div>
                )}
                <span className="font-semibold text-white group-hover/avatar:text-purple-200 transition-colors text-xl">
                  {uploader}
                </span>
              </Link>

              <div className="h-2 w-2 rounded-full bg-slate-500" />

              <div className="flex items-center gap-2 text-slate-300">
                <Clock className="h-5 w-5" />
                <span>{formattedDate}</span>
              </div>
            </div>

            {topVideo.description && (
              <p className="text-lg text-slate-300 mb-10 leading-relaxed max-w-2xl">
                {topVideo.description}
              </p>
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                className="rounded-full bg-gradient-to-r from-purple-600 via-purple-500 to-pink-500 hover:from-purple-700 hover:via-purple-600 hover:to-pink-600 text-white text-lg px-8 py-6 shadow-2xl shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 transform hover:scale-105"
                onClick={() => router.push(`/v/${topVideo.shortLink}`)}
              >
                <Play className="w-5 h-5 mr-2" fill="currentColor" />
                Watch Now
              </Button>

              <Button
                variant="outline"
                className="rounded-full border-purple-400/50 text-purple-300 hover:bg-purple-500/10 hover:border-purple-400 text-lg px-8 py-6 transition-all duration-300"
                onClick={() => router.push(`/community/u/${uploader}`)}
              >
                <User className="w-5 h-5 mr-2" />
                View Profile
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}