import { Suspense } from "react";
import { AdminStats } from "./_components/admin-stats";
import { AdminHeader } from "./_components/admin-header";
import { RevenueDashboard } from "./_components/revenue-dashboard";
import { PaymentIssues } from "./_components/payment-issues";
import { getAdminStats } from "@/lib/db/admin";
import { getAdminUser } from "@/server/session";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BarChartIcon as ChartBarIcon, CreditCardIcon } from "lucide-react";
import { getDiskUsage } from "@/server/storage";

export default async function AdminPage() {
  const user = await getAdminUser(["ADMIN_PAGE"]);
  const stats = await getAdminStats();

  const enhancedStats = {
    ...stats,
    reports: stats.reports || {
      total: 0,
      open: 0,
      pending: 0,
      closed: 0,
    },
    storageUsage: await getDiskUsage(),
  };

  const isCEO = user.role.id === 2;

  return (
    <div className="space-y-4">
      {/* Header */}
      <AdminHeader />

      {/* Tabs */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full justify-start overflow-x-auto bg-gray-900/30 border border-gray-800/40 h-10 rounded-lg p-1">
            <TabsTrigger
              value="overview"
              className="flex items-center gap-2 text-sm data-[state=active]:bg-gray-700/60 data-[state=active]:text-white data-[state=active]:shadow-sm text-gray-400 hover:text-gray-300 px-3 py-1.5 rounded-md transition-all duration-200"
            >
              <ChartBarIcon className="h-3.5 w-3.5" />
              <span className="whitespace-nowrap">Overview</span>
            </TabsTrigger>
            {isCEO && (
              <TabsTrigger
                value="revenue"
                className="flex items-center gap-2 text-sm data-[state=active]:bg-gray-700/60 data-[state=active]:text-white data-[state=active]:shadow-sm text-gray-400 hover:text-gray-300 px-3 py-1.5 rounded-md transition-all duration-200"
              >
                <CreditCardIcon className="h-3.5 w-3.5" />
                <span className="whitespace-nowrap">Revenue</span>
              </TabsTrigger>
            )}
          </TabsList>

          <div className="mt-3">
            <TabsContent value="overview" className="space-y-4 m-0">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2 text-purple-400">
                      <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">Loading stats...</span>
                    </div>
                  </div>
                }
              >
                <AdminStats stats={enhancedStats} />
              </Suspense>
            </TabsContent>

            {isCEO && (
              <TabsContent value="revenue" className="space-y-4 m-0">
                {/* CEO Warning Banner */}
                <div className="bg-orange-600/10 border border-orange-500/20 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="flex h-4 w-4 items-center justify-center rounded bg-orange-600/30">
                      <svg
                        className="h-2.5 w-2.5"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 2L1 21h22L12 2zm0 3.5L20.5 19H3.5L12 5.5zM11 10v4h2v-4h-2zm0 6v2h2v-2h-2z" />
                      </svg>
                    </div>
                    <span className="text-orange-300 font-medium">
                      Restricted Access
                    </span>
                    <span className="text-orange-400/70">CEO/Finance only</span>
                  </div>
                </div>

                <Suspense
                  fallback={
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-2 text-purple-400">
                        <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
                        <span className="text-sm">Loading revenue data...</span>
                      </div>
                    </div>
                  }
                >
                  <RevenueDashboard />
                </Suspense>

                <Suspense
                  fallback={
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-2 text-purple-400">
                        <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
                        <span className="text-sm">
                          Loading payment issues...
                        </span>
                      </div>
                    </div>
                  }
                >
                  <PaymentIssues />
                </Suspense>
              </TabsContent>
            )}
          </div>
        </Tabs>
      </div>
    </div>
  );
}