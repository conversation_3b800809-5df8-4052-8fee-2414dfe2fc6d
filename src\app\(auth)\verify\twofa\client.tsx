"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { OTPInput } from "@/components/ui/otp-input";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { motion } from "framer-motion";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import gsap from "gsap";
import Heading from "@/components/common/Heading";
import Description from "@/components/common/Description";
import Icons from "@/components/common/Icons";
import compareEllips from "../../../../../public/assets/images/webp/compare-left-img.webp";

type Verify2FAPageProps = {
  email: string;
};

export default function VerifyTwoFaClient({ email }: Verify2FAPageProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isVerifying, setIsVerifying] = useState(false);
  const leftBadgeRef = useRef(null);
  const rightBadgeRef = useRef(null);
  const leftContainerRef = useRef(null);
  const rightContainerRef = useRef(null);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  const handleBadgeMouseMove = (e: any, badgeRef: any, containerRef: any) => {
    if (!badgeRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const badge = badgeRef.current;

    const containerRect = container.getBoundingClientRect();
    const centerX = containerRect.left + containerRect.width / 2;
    const centerY = containerRect.top + containerRect.height / 2;

    const x = (e.clientX - centerX) * 0.3;
    const y = (e.clientY - centerY) * 0.3;

    const maxMove = 40;
    const limitedX = Math.max(-maxMove, Math.min(maxMove, x));
    const limitedY = Math.max(-maxMove, Math.min(maxMove, y));

    gsap.to(badge, {
      x: limitedX,
      y: limitedY,
      duration: 0.3,
      ease: "power2.out",
    });
  };

  const handleBadgeMouseLeave = (badgeRef: any) => {
    if (!badgeRef.current) return;

    gsap.to(badgeRef.current, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
    });
  };

  const handleVerify = async (otp: string) => {
    setIsVerifying(true);

    try {
      const result = await fetch("/api/auth/verify/twofactor", {
        method: "POST",
        body: JSON.stringify({
          token: otp
        }),
      })

      const data = await result.json();
      if (!result.ok) {
        error("Verification Error", data.message);
        return;
      }

      success("Verification Successful", "Successfully verified!");

      router.push('/dashboard');
    } catch (err) {
      error("Verification Failed", "Invalid OTP. Please try again.");
      console.error("Error verifying OTP:", err);
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-dark-purple via-custom-black to-light-purple">
      {/* Background Ellipse */}
      <Image
        width={379}
        height={379}
        className="absolute left-0 pointer-events-none top-[-5%] max-lg:hidden z-[0] opacity-60"
        src={compareEllips}
        alt="background ellipse"
      />
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-custom-purple to-custom-pink opacity-20"
            style={{
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 15}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-8">
        <div className="max-w-6xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            <div className="relative">
              <div
                ref={leftContainerRef}
                className="absolute max-lg:hidden top-[-10%] right-[10%] z-10"
                data-aos="fade-right"
                data-aos-duration={1050}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, leftBadgeRef, leftContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(leftBadgeRef)}
              >
                <div
                  ref={leftBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[226px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-2.5 cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="secureStorage" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute top-[-40%] end-[-6%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Bank-Level Security
                  </p>
                </div>
              </div>

              <div
                ref={rightContainerRef}
                className="absolute max-lg:hidden top-[-8%] left-[-5%] z-10"
                data-aos="fade-left"
                data-aos-duration={1300}
                onMouseMove={(e) =>
                  handleBadgeMouseMove(e, rightBadgeRef, rightContainerRef)
                }
                onMouseLeave={() => handleBadgeMouseLeave(rightBadgeRef)}
              >
                <div
                  ref={rightBadgeRef}
                  className="flex items-center gap-x-2.5 max-w-[264px] bg-badge w-full border-custom-blur-gray justify-center relative border rounded-full px-[22px] py-[9px] cursor-pointer backdrop-blur-md"
                >
                  <Icons icon="endToEnd" className="w-5 h-2.5" />
                  <Icons
                    icon="badgeArrow"
                    className="absolute rotate-[-90deg] top-[-38%] start-[-5%] w-5 h-5"
                  />
                  <p className="text-sm md:text-base font-semibold text-nowrap leading-100 mb-0 text-light-white">
                    Protected Access
                  </p>
                </div>
              </div>

              {/* Main Content */}
              <div data-aos="zoom-in" data-aos-duration={500}>
                <Link href="/" className="flex items-center mb-8 lg:mb-12">
                  <Image
                    src="/assets/images/svg/footer-logo.svg"
                    alt="StreamBliss"
                    width={180}
                    height={40}
                    className="h-10 lg:h-12 w-auto"
                  />
                </Link>

                <Heading
                  variant="6xl"
                  className="!text-left !mb-6 max-w-[500px] !font-bold"
                >
                  Secure Your Account Access
                </Heading>

                <Description className="!text-left max-w-[480px] mb-8 !opacity-80">
                  Two-factor authentication adds an extra layer of security to
                  keep your creative content safe from unauthorized access.
                </Description>

                {/* Security highlights */}
                <div className="space-y-4 max-lg:hidden">
                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={200}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="secureStorage" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Advanced encryption protection
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={400}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="effort" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Time-based verification codes
                    </span>
                  </motion.div>

                  <motion.div
                    className="flex items-center gap-4"
                    data-aos="fade-up"
                    data-aos-duration={600}
                    data-aos-delay={600}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                      <Icons icon="fastUpload" className="w-4 h-4" />
                    </div>
                    <span className="text-white font-medium">
                      Instant account verification
                    </span>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Right Side - 2FA Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full max-w-md mx-auto lg:max-w-none"
            >
              <div className="relative">
                <div className="backdrop-blur-md bg-white/5 rounded-2xl border border-white/10 p-8 shadow-2xl relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  <div className="relative z-10">
                    {/* Header */}
                    <div className="mb-8 text-center lg:text-left">
                      <div className="flex items-center gap-3 mb-4 justify-center lg:justify-start">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-custom-purple to-custom-pink flex items-center justify-center">
                          <Icons icon="secureStorage" className="w-5 h-5" />
                        </div>
                        <div>
                          <h1 className="text-2xl lg:text-3xl font-bold text-white">
                            Two-Factor Authentication
                          </h1>
                        </div>
                      </div>
                      <p className="text-white/70">
                        Enter the 6-digit code from your authenticator app
                      </p>
                    </div>

                    {/* OTP Input */}
                    <div className="space-y-6">
                      <div className="flex justify-center">
                        <OTPInput
                          length={6}
                          onComplete={handleVerify}
                          disabled={isVerifying}
                        />
                      </div>

                      {isVerifying && (
                        <div className="flex items-center justify-center gap-3 text-white/90 bg-white/5 rounded-lg p-3">
                          <svg
                            className="w-5 h-5 animate-spin text-purple-400"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                          </svg>
                          <span className="text-sm font-medium">
                            Verifying code...
                          </span>
                        </div>
                      )}

                      {/* Back to login */}
                      <div className="text-center pt-4 border-t border-white/10">
                        <Link
                          href="/login"
                          className="text-sm text-white/60 hover:text-white transition-colors"
                        >
                          ← Back to login
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center mt-4">
                  <span
                    onClick={() => router.push("/password-forgot")}
                    className="text-sm text-purple-400 hover:text-pink-400 transition-colors font-medium underline underline-offset-2 cursor-pointer"
                  >
                    Can&apos;t access your authenticator app?
                  </span>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-custom-purple to-custom-pink rounded-full opacity-20 blur-xl" />
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-custom-pink to-custom-purple rounded-full opacity-15 blur-xl" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating animations */}
      <style>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          25% {
            transform: translateY(-20px) rotate(90deg);
          }
          50% {
            transform: translateY(-10px) rotate(180deg);
          }
          75% {
            transform: translateY(-30px) rotate(270deg);
          }
        }
      `}</style>
    </div>
  );
}