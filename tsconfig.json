{
  "compilerOptions": {
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "noEmit": true,
    "incremental": true,
    "module": "esnext",
    "esModuleInterop": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "strictNullChecks": true,
    "baseUrl": ".",
    "paths": {
      "@/components/*": [
        "src/components/*"
      ],
      "@/lib/*": [
        "src/lib/*"
      ],
      "@/server/*": [
        "src/server/*"
      ],
      "@/hooks/*": [
        "src/hooks/*"
      ],
      "@/types/*": [
        "src/types/*"
      ],
    },
    "plugins": [
      {
        "name": "next"
      }
    ],
    "target": "ES2017"
  },
  "include": [
    "next-env.d.ts",
    ".next/types/**/*.ts",
    "**/*.ts",
    "**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}
