"use client";
import React, { useState } from "react";
import Heading from "./common/Heading";
import Description from "./common/Description";
import { PRICING_LIST } from "../../utils/helper";
import CustomButton from "./common/CustomButton";
import Icons from "./common/Icons";
import pricingEllips from "../../public/assets/images/webp/pricing-right-img.webp";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect } from "react";

const Pricing = () => {
  const [active, setActive] = useState(false);
  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div id="pricing" className="pt-35 w-full pb-20 max-lg:py-16 max-md:pt-10 max-md:pb-7 relative mt-[-60px] sm:mt-0">
      <Image
        width={379}
        height={379}
        className="absolute right-0 -top-1/4 -z-[1]"
        src={pricingEllips}
        alt="pricing img"
      />
      <div className="max-w-[1080px] mx-auto w-full max-[1143.98px]:px-4">
        <div className="max-w-[709px] mx-auto">
          <div data-aos="fade-up" data-aos-duration={800}>
            <Heading>Simple, Transparent Pricing</Heading>
          </div>
          <div data-aos="fade-up" data-aos-duration={1000}>
            <Description className="text-center">
              Choose the plan that works best for you
            </Description>
          </div>
          <div
            data-aos="fade-up"
            data-aos-duration={1000}
            className="w-full bg-gradient-to-r from-black via-white to-black mt-5 h-[1px] mb-10.5 max-lg:mb-8 max-md:mb-6"
          ></div>
          <div
            data-aos="fade-up"
            data-aos-duration={1200}
            className="w-full max-w-[370px] max-md:max-w-[300px] mx-auto bg-white/5 border border-white/24 rounded-full h-18.5 max-md:h-14.5 shadow-[0px_0px_6.5px_0px_#D74CB661_inset] relative flex justify-between items-center overflow-hidden"
          >
            <div
              className={`absolute top-1/2 -translate-y-1/2 h-14.5 max-md:h-10.5 bg-gradient-to-b from-custom-purple to-custom-pink rounded-full duration-200 ease-linear  ${
                active
                  ? "translate-x-[92%] max-md:translate-x-[83%] w-47 max-md:w-40 max-sm:w-40"
                  : "translate-x-[4%] w-36.5 max-md:w-30 max-sm:w-28"
              }`}
            ></div>
            <button
              onClick={() => setActive(false)}
              className="py-[14.5px] text-white font-bold leading-160 text-lg z-[1] cursor-pointer max-md:text-base absolute top-1/2 -translate-y-1/2 left-9 max-md:left-7"
            >
              Monthly
            </button>
            <button
              onClick={() => setActive(true)}
              className="py-[14.5px] text-white font-bold leading-160 text-lg absolute z-[1] cursor-pointer flex items-center gap-2 max-md:text-base top-1/2 -translate-y-1/2 right-6 max-md:right-4.5"
            >
              Yearly
              <span className="px-2 py-[1px] text-white text-sm leading-160 font-medium border border-white rounded-full bg-[#D04DBF1F] max-md:px-1.5 max-md:text-xs">
                Save 20%
              </span>
            </button>
          </div>
        </div>
        <div className="flex lg:items-center justify-start pt-12.5 max-lg:pt-10 max-md:pt-8 max-lg:flex-wrap max-lg:gap-4">
          {PRICING_LIST.map((obj, i) => (
            <div
              data-aos="fade-up"
              data-aos-duration={1200}
              key={i}
              className={`border flex flex-col justify-between backdrop-blur-[16px] hover:shadow-[0px_8px_24px_0px] hover:shadow-custom-skyblue duration-300 ease-linear max-md:!p-4 max-lg:w-[48%] max-md:w-full max-lg:max-w-full max-md:max-w-[500px] max-lg:mx-auto max-md:rounded-xl hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20 transition-all ${
                i === 0
                  ? "rounded-tl-[20px] max-lg:rounded-[20px] rounded-bl-[20px] lg:h-[575px] w-full max-w-[364px] border-white/10 p-6 px-[23px] -mr-[0.5px] bg-white/1 max-lg:p-5"
                  : i === 1
                  ? "bg-[#B81EC90A] rounded-[20px] pricing-gradient-border p-[1px] lg:min-h-[608px] w-full max-w-[412px] py-8 px-[45px] max-lg:py-7 max-lg:px-10 max-lg:before:!h-auto max-md:before:!rounded-xl"
                  : "rounded-tr-[20px] max-lg:rounded-[20px] rounded-br-[20px] lg:h-[575px] w-full max-w-[364px] border-white/10 p-6 px-[23px] -ml-[0.5px] bg-white/1 max-lg:p-5"
              }`}
            >
              <div className="mb-5">
                <div className="flex w-full items-center gap-2.5">
                  <p className="text-white text-2xl font-semibold leading-100 max-md:text-xl">
                    {obj.planType}
                  </p>
                  {i === 1 && (
                    <p className="bg-gradient-to-b from-custom-purple to-custom-pink p-[1px] rounded-full h-6 flex justify-center items-center w-full max-w-[105px]">
                      <span className="w-full bg-gradient-to-b from-[#1E0B21] to-[#220A1C] text-center text-xs text-white font-semibold leading-100 h-full rounded-full  flex justify-center items-center">
                        Most Popular
                      </span>
                    </p>
                  )}
                </div>
                <p className="text-white font-semibold pt-2.5 text-custom-4xl max-lg:text-custom-3xl leading-120">
                  {active && i === 1
                    ? "$100"
                    : active && i === 2
                    ? "$250"
                    : obj.planFee}
                  <span className="font-normal text-custom-3xl max-lg:text-2xl">
                    {active ? "/year" : "/month"}
                  </span>
                </p>

                {i === 1 || i === 2 ? (
                  <div
                    className={`duration-200 ease-linear ${
                      active ? "opacity-100 max-h-8" : "opacity-0 max-h-0"
                    }`}
                  >
                    <p className="text-transparent bg-gradient-to-bl from-custom-purple to-custom-pink bg-clip-text pt-2 font-semibold text-xl">
                      {i === 1 ? "Save $20 (17%)" : "Save $50 (17%)"}
                      <span className="font-normal text-xs leading-100 text-white opacity-70 block pt-0.5">
                        Compared to monthly billing
                      </span>
                    </p>
                  </div>
                ) : (
                  ""
                )}

                <p className="pt-9 text-white font-bold text-lg leading-100 pb-5.5 max-lg:pt-7 max-lg:text-base max-lg:pb-3.5">
                  {obj.planUse}
                </p>
                <div className="flex flex-col gap-4.5 max-lg:gap-3">
                  {obj.list.map((itm, idx) => (
                    <div
                      data-aos="fade-up"
                      data-aos-duration={idx * 300 + 500}
                      key={idx}
                      className="flex gap-2.5 items-center"
                    >
                      <Icons icon="rightTickIcon" />
                      <p className="text-white opacity-70 font-normal text-base leading-160 max-md:text-sm">
                        {itm}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <a href="/register">
                <CustomButton variant={i === 1 ? "gradiant" : "default"}>
                  Get Started
                </CustomButton>
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Pricing;