"use server";

import { prisma } from "@/lib/prisma";
import type { Video } from "@/types/video";
import { unstable_cache } from "next/cache";

const API_URL = process.env.VIDEO_API_URL || "https://api.streambliss.cloud";

function calculateVideoScore(video: any): number {
  const now = new Date();
  const createdAt = new Date(video.createdAt);
  const hoursSinceCreated =
    (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

  const viewScore = Math.log10((video.views || 0) + 1) * 100;
  const recencyBonus = Math.max(
    0,
    500 * Math.exp(-hoursSinceCreated / (24 * 7)),
  );

  const durationSeconds = video.duration || 0;
  let durationBonus = 0;
  if (durationSeconds >= 30 && durationSeconds <= 300) {
    durationBonus = 50;
  } else if (durationSeconds > 300 && durationSeconds <= 600) {
    durationBonus = 25;
  }

  const communityBonus = video.approvedForCommunity ? 100 : 0;

  return viewScore + recencyBonus + durationBonus + communityBonus;
}

async function fetchVideosFromDb(): Promise<Video[]> {
  try {
    const videos = await prisma.video.findMany({
      where: {
        showCommunity: true,
        isPrivate: false,
        approvedForCommunity: true,
      },
      orderBy: [{ views: "desc" }, { createdAt: "desc" }],
      take: 24,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const uniqueVideos = Array.from(
      new Map(videos.map((v) => [v.id, v])).values(),
    );

    // Apply improved scoring algorithm
    const scoredVideos = uniqueVideos.map((video) => ({
      ...video,
      _score: calculateVideoScore(video),
    }));

    // Sort by calculated score
    scoredVideos.sort((a, b) => b._score - a._score);

    const mappedVideos: Video[] = scoredVideos.map((video) => {
      const user = video.user
        ? {
            id: video.user.id,
            name: video.user.name || "Unknown",
            image: `${API_URL}/profile/${video.user.id}`,
          }
        : undefined;
      return {
        ...video,
        thumbnailUrl:
          video.thumbnailUrl ||
          `${API_URL}/video/thumbnail/${video.userId}/${video.id}`,
        user,
      };
    });

    return mappedVideos;
  } catch (error) {
    console.error("Error fetching videos:", error);
    return [];
  }
}

export const getVideos = unstable_cache(
  async () => {
    return fetchVideosFromDb();
  },
  ["community-videos-v2"],
  {
    revalidate: 30,
    tags: ["community-videos"],
  },
);

async function fetchTrendingVideos(): Promise<Video[]> {
  try {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const videos = await prisma.video.findMany({
      where: {
        showCommunity: true,
        isPrivate: false,
        approvedForCommunity: true,
        createdAt: { gte: oneDayAgo },
      },
      orderBy: [{ views: "desc" }, { createdAt: "desc" }],
      take: 12,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const mappedVideos: Video[] = videos.map((video) => {
      const user = video.user
        ? {
            id: video.user.id,
            name: video.user.name || "Unknown",
            image: `${API_URL}/profile/${video.user.id}`,
          }
        : undefined;
      return {
        ...video,
        thumbnailUrl:
          video.thumbnailUrl ||
          `${API_URL}/video/thumbnail/${video.userId}/${video.id}`,
        user,
      };
    });

    return mappedVideos;
  } catch (error) {
    console.error("Error fetching trending videos:", error);
    return [];
  }
}

export const getTrendingVideos = unstable_cache(
  async () => {
    return fetchTrendingVideos();
  },
  ["trending-videos"],
  {
    revalidate: 15,
    tags: ["community-videos", "trending"],
  },
);

// New function for paginated loading of more videos
async function fetchMoreVideos(
  skip: number = 0,
  take: number = 12,
): Promise<Video[]> {
  try {
    const videos = await prisma.video.findMany({
      where: {
        showCommunity: true,
        isPrivate: false,
        approvedForCommunity: true,
      },
      orderBy: [{ views: "desc" }, { createdAt: "desc" }],
      skip,
      take,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const mappedVideos: Video[] = videos.map((video) => {
      const user = video.user
        ? {
            id: video.user.id,
            name: video.user.name || "Unknown",
            image: `${API_URL}/profile/${video.user.id}`,
          }
        : undefined;
      return {
        ...video,
        thumbnailUrl:
          video.thumbnailUrl ||
          `${API_URL}/video/thumbnail/${video.userId}/${video.id}`,
        user,
      };
    });

    return mappedVideos;
  } catch (error) {
    console.error("Error fetching more videos:", error);
    return [];
  }
}

export const getMoreVideos = unstable_cache(
  async (skip: number, take: number) => {
    return fetchMoreVideos(skip, take);
  },
  ["more-videos"],
  {
    revalidate: 60,
    tags: ["community-videos"],
  },
);

async function fetchTopVideoFromDb() {
  try {
    const video = await prisma.video.findFirst({
      where: {
        showCommunity: true,
        isPrivate: false,
      },
      orderBy: { views: "desc" },
      include: {
        user: { select: { id: true, name: true } },
      },
    });
    if (video && video.user) {
      const user = {
        id: video.user.id,
        name: video.user.name || "Unknown",
        image: `${API_URL}/profile/${video.user.id}`,
      };
      return {
        ...video,
        thumbnailUrl:
          video.thumbnailUrl ||
          `${API_URL}/video/thumbnail/${video.userId}/${video.id}`,
        user,
      };
    }
    if (!video) return null;
    return {
      ...video,
      thumbnailUrl:
        video.thumbnailUrl ||
        `${API_URL}/video/thumbnail/${video.userId}/${video.id}`,
    };
  } catch (error) {
    console.error("Error fetching top video:", error);
    return null;
  }
}

export const getTopVideo = unstable_cache(
  async () => {
    return fetchTopVideoFromDb();
  },
  ["community-top-video"],
  { revalidate: 60 },
);