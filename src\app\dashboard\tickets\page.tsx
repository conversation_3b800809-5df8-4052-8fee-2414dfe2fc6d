import { getUser } from "@/server/session";
import { prisma } from "@/lib/prisma";
import DashboardTicketsClient from "./client";
import { redirect } from "next/navigation";
import { getUserNotifications } from "@/server/notifications";
import { getUserById } from "@/lib/db/user";
import { hasPermission } from "@/server/admin";
import { getUserProfilePicture } from "@/server/profile";

export default async function TicketsPage() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }

  const dbUser = await getUserById(user.id);
  const hasAccessToAdmin = dbUser
    ? await hasPermission(dbUser.roleId, ["ADMIN_PAGE"])
    : false;
  const notifications = await getUserNotifications(user.id);
  const userProfileImage = await getUserProfilePicture(user.id);

  return (
    <DashboardTicketsClient
      userName={user.name || ""}
      userId={user.id}
      userImage={userProfileImage}
      notifications={notifications}
      hasAccessToAdmin={hasAccessToAdmin}
    />
  );
}