"use client";

import { useEffect, useState } from "react";
import { $Enums } from "@prisma/client";
import { needsTwoFactorAuthAsAdmin } from "@/server/admin";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";
import { DashboardWrapper } from "./_components/dashboard-wrapper";
import { DashboardHeader } from "./_components/dashboard-header";
import { NewMediaGrid } from "./_components/new-media-grid";
import { SortOption } from "./_components/sort-popup";
import { InteractiveTutorial } from "./_components/interactive-tutorial";
import { completeTutorial } from "./_actions/complete-tutorial";
import {useSocket} from "@/components/socket-context";

const API_URL = "https://dev-api.streambliss.cloud";
const ITEMS_PER_PAGE = 12;

type DashboardClientProps = {
  userName: string;
  userId: string;
  userImage?: string | null;
  videos: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    url: string;
    thumbnailUrl: string | null;
    shortLink: string;
    views: number;
    userId: string;
    duration?: number;
    isPrivate?: boolean;
    commentsDisabled?: boolean;
    musicDisabled?: boolean;
    showCommunity?: boolean;
    approvedForCommunity?: boolean;
  }[];
  authToken: string;
  notifications: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  images?: {
    url: string;
    name: string;
    title?: string;
    createdAt: Date;
    shortLink: string;
    id: string;
  }[];
  hasAccessToAdmin?: boolean;
  userSubscription: string;
  hasSeenTutorial?: boolean;
};

export default function DashboardClient({
  userName,
  userId,
  userImage,
  videos,
  authToken,
  notifications,
  images = [],
  hasAccessToAdmin,
  userSubscription,
  hasSeenTutorial = false,
}: DashboardClientProps) {
  const [firstRender, setFirstRender] = useState(false);
  const [videoSubmit, setVideoSubmit] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [isPrivate, setIsPrivate] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [imageCount, setImageCount] = useState(images.length);
  const [sortBy, setSortBy] = useState<SortOption>("date");
  const [avatarError, setAvatarError] = useState(false);
  const [showTutorial, setShowTutorial] = useState(!hasSeenTutorial);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();
  const { info } = useEnhancedToast();
  const {isConnected, sendEvent} = useSocket();

  useEffect(() => {
    setShowTutorial(!hasSeenTutorial);
  }, [hasSeenTutorial]);

  useEffect(() => {
    if (firstRender) {
      return;
    }

    const checkTwoFactorAuth = async () => {
      const twoFaCheck = await needsTwoFactorAuthAsAdmin(userId);
      if (twoFaCheck) {
        info("Admin Access Required", "Please enable Two Factor Authentication to access this page.");
        router.push("/dashboard/settings");
      }
    };

    checkTwoFactorAuth();
    setFirstRender(true);
  }, [userId, firstRender, router, info]);

  useEffect(() => {
    console.log("Connected: " + isConnected);
    if (isConnected) {
      console.log(JSON.stringify({
        type: "video-progress",
        action: 'subscribe',
        userId: userId
      }))
      sendEvent(JSON.stringify({
        type: "video-progress",
        action: 'subscribe',
        userId: userId
      }));
    }
  }, [isConnected, sendEvent, userId]);

  const handleImageUpload = (newCount: number) => {
    setImageCount(newCount);
  };

  const handleTutorialComplete = () => {
    setShowTutorial(false);
    router.refresh();
  };

  const handleTutorialClose = async () => {
    setShowTutorial(false);
    try {
      await completeTutorial(userId);
    } catch (error) {
      console.error("Failed to save tutorial completion:", error);
    }
  };

  const allMedia = [
    ...videos.map((video) => ({
      id: video.id,
      title: video.title,
      thumbnailUrl: `${API_URL}/videos/thumbnail/${video.id}`,
      type: "video" as const,
      createdAt: video.createdAt,
      views: video.views,
      likes: Math.floor(Math.random() * 50) + 10,
      duration: video.duration,
      tags: [
        ...(video.musicDisabled ? [] : ["Music"]),
        video.isPrivate ? "Private" : "Public",
        ...(video.showCommunity ? ["Community"] : []),
      ],
      url: video.url,
      shortLink: video.shortLink,
      isPrivate: video.isPrivate,
      commentsDisabled: video.commentsDisabled,
      musicDisabled: video.musicDisabled,
      showCommunity: video.showCommunity,
      approvedForCommunity: video.approvedForCommunity,
    })),
    ...images.map((image) => ({
      id: image.id,
      title: image.title || image.name,
      thumbnailUrl: image.url,
      type: "image" as const,
      createdAt: image.createdAt,
      views: 0,
      likes: Math.floor(Math.random() * 30) + 5,
      tags: ["Public"],
      url: image.url,
      shortLink: image.shortLink,
      isPrivate: false,
    })),
  ];

  const sortedMedia = [...allMedia].sort((a, b) => {
    switch (sortBy) {
      case "date":
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      case "duration":
        if (a.type === "video" && b.type === "image") return -1;
        if (a.type === "image" && b.type === "video") return 1;
        if (a.type === "video" && b.type === "video") {
          const aDuration = a.duration || 0;
          const bDuration = b.duration || 0;
          return bDuration - aDuration;
        }
        return 0;
      case "views":
        return b.views - a.views;
      case "name":
        return a.title.localeCompare(b.title);
      default:
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
    }
  });

  const filteredMedia = sortedMedia.filter((item) => {
    if (
      searchQuery &&
      !item.title.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    if (activeFilter === "videos" && item.type !== "video") {
      return false;
    }
    if (activeFilter === "images" && item.type !== "image") {
      return false;
    }

    if (isPrivate && !item.isPrivate) {
      return false;
    }

    return true;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredMedia.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedMedia = filteredMedia.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeFilter, isPrivate, searchQuery, sortBy]);

  return (
    <>
      <DashboardWrapper
        userName={userName}
        userImage={userImage}
        notifications={notifications}
        hasAccessToAdmin={hasAccessToAdmin}
      >
        <DashboardHeader
          userName={userName}
          userImage={userImage}
          notifications={notifications}
          hasAccessToAdmin={hasAccessToAdmin}
          title="Your Recent Files"
          description="Manage and organize your Video and images."
          showFilters={true}
          activeFilter={activeFilter}
          setActiveFilter={setActiveFilter}
          isPrivate={isPrivate}
          setIsPrivate={setIsPrivate}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          sortBy={sortBy}
          setSortBy={setSortBy}
          onUpload={() => setShowUploadDialog(true)}
        />

        {/* Media Grid */}
        <NewMediaGrid
          media={paginatedMedia}
          userId={userId}
          authToken={authToken}
          userSubscription={userSubscription}
          videos={videos}
          images={images}
          videoSubmit={videoSubmit}
          setVideoSubmit={setVideoSubmit}
          onImageUpload={handleImageUpload}
          showUploadDialog={showUploadDialog}
          setShowUploadDialog={setShowUploadDialog}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          totalItems={filteredMedia.length}
        />
      </DashboardWrapper>

      {/* Interactive Tutorial */}
      <InteractiveTutorial
        isOpen={showTutorial}
        onClose={handleTutorialClose}
        onComplete={handleTutorialComplete}
        userId={userId}
      />
    </>
  );
}