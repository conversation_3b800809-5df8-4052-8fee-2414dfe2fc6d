import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function DELETE(request: NextRequest) {
    const userSession = await getUserSession();
    if (!userSession || !userSession.accessToken) {
        return NextResponse.json({status: HttpStatusCode.Forbidden, message: "No valid user-session."});
    }

    const response = await fetch(process.env.VIDEO_API_URL + "/images/profile-picture", {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        }
    });

    const data = await response.json();
    if (!response.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, success: true});
}