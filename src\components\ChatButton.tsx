"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle } from "lucide-react"
import { motion } from "framer-motion"

export function ChatButton() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 }}
      className="fixed bottom-2 right-2 sm:bottom-4 sm:right-4 z-50 max-w-[12vw] sm:max-w-none"
    >
      <Button
        variant="outline"
        size="icon"
        className="w-10 h-10 sm:w-12 sm:h-12 max-w-[48px] max-h-[48px] rounded-full bg-background/95 border-primary/20 hover:border-primary/40"
        aria-label="Open chat"
      >
        <MessageCircle className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
      </Button>
    </motion.div>
  )
} 