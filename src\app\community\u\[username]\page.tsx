import { notFound } from "next/navigation"
import Footer from "@/components/Footer"
import { getUserByName, getUserVideos } from "../../_actions/get-user-videos"
import { UserProfileClient } from "./client"
import { CommunityHeaderWrapper } from "../../_components/community-header-wrapper"
import type { Metadata, ResolvingMetadata } from "next"

type GenerateMetadataProps = {
  params: Promise<{ username: string }>
}

export async function generateMetadata(
  props: GenerateMetadataProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const params = await props.params
  const username = params.username
  const user = await getUserByName(username)
  
  if (!user) {
    return {
      title: "User Not Found",
      description: "The requested user profile could not be found."
    }
  }
  
  return {
    title: `${user.name}'s Videos | StreamBliss Community`,
    description: `Watch videos shared by ${user.name} on StreamBliss Community.`,
    openGraph: {
      title: `${user.name}'s Videos | StreamBliss Community`,
      description: `Watch videos shared by ${user.name} on StreamBliss Community.`,
      type: "profile",
      url: `/community/u/${username}`,
      images: [
        {
          url: user.image,
          width: 400,
          height: 400,
          alt: user.name || "User profile"
        }
      ]
    },
    twitter: {
      card: "summary",
      title: `${user.name}'s Videos | StreamBliss Community`,
      description: `Watch videos shared by ${user.name} on StreamBliss Community.`,
      images: [user.image]
    }
  }
}

export default async function UserProfilePage(props: { params: Promise<{ username: string }> }) {
  // Await the params to get the username
  const params = await props.params
  const username = params.username
  const user = await getUserByName(username)
  
  if (!user) {
    notFound()
  }
  
  const videos = await getUserVideos(username)
  
  return (
    <div className="min-h-screen bg-black text-white">
      <CommunityHeaderWrapper />
      <UserProfileClient user={user} videos={videos} />
      <Footer />
    </div>
  )
}