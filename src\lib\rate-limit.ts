const rateLimitMap = new Map<string, {count: number, lastRequest: number}>();

const RATE_LIMIT = 5;
const TIME_WINDOW = 60 * 1000;

export function rateLimiter(ip: string): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);

  if (!record) {
    rateLimitMap.set(ip, {count: 1, lastRequest: now});
    return false;
  }

  if (now - record.lastRequest > TIME_WINDOW) {
    rateLimitMap.set(ip, {count: 1, lastRequest: now});
    return false;
  }

  if (record.count >= RATE_LIMIT) {
    return true;
  }

  record.count += 1;
  rateLimitMap.set(ip, record);
  return false;
}

const rateLimitMapViews = new Map<string, { lastRequest: number }>();
const TIME_WINDOW_VIEWS = 60 * 60 * 1000;

export function rateLimiterViews(ip: string, videoId: string): boolean {
  const now = Date.now();
  const key = `${ip}_${videoId}`;

  const record = rateLimitMapViews.get(key);
  if (!record) {
    rateLimitMapViews.set(key, { lastRequest: now });
    return false;
  }

  if (now - record.lastRequest < TIME_WINDOW_VIEWS) {
    return true;
  }

  rateLimitMapViews.set(key, { lastRequest: now });
  return false;
}