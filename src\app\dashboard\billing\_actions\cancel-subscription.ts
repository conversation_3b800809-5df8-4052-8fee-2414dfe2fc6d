"use server"

import { getBillingUserById, getUserById } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { stripe } from "@/lib/stripe";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
}

export default async function cancelSubscription(): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  const userSession = await getUserSession();
  if (!userSession.userId) {
    return {
      success: false,
      message: "User Session not found",
    }
  }

  const user = await getUserById(userSession.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found",
    }
  }

  const billingUser = await getBillingUserById(user.id);
  if (!billingUser || !billingUser.stripeAccountId) {
    return {
      success: false,
      message: "Billing user not found",
    }
  }

  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: billingUser.stripeAccountId,
      status: 'active',
      limit: 1,
    });

    if (subscriptions.data.length === 0) {
      return {
        success: false,
        message: "No active subscription found",
      }
    }

    // Cancel the active subscription
    const subscription = subscriptions.data[0];
    const response = await stripe.subscriptions.cancel(subscription.id);

    if (response.status !== "canceled") {
      return {
        success: false,
        message: "Failed to cancel subscription",
      }
    }
  } catch (error) {
    console.error("Error canceling subscription:", error);
    return {
      success: false,
      message: "Failed to cancel subscription. Please try again.",
    }
  }

  await prisma.user.update({
    where: { id: user.id },
    data: {
      package: "FREE"
    },
  });

  await createLog(user.id, LogConstants.CANCELLED_SUBSCRIPTION, LogActions.ACCOUNT);

  return {
    success: true,
    message: "Subscription cancelled successfully",
  }
}