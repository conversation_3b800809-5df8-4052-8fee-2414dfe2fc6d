"use server";

import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { createNotification } from "@/server/notifications";
import { getReportCase, getReportData } from "@/server/reports";
import { getUserSession } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type ReportData = {
  reportId: string;
  status: string;
};

export async function updateReportStatus({ reportId, status }: ReportData): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!reportId) {
    return { success: false, message: "Invalid report ID" };
  }

  if (!status) {
    return { success: false, message: "Invalid status" };
  }

  if (status != "PENDING" && status != "CLOSED") {
    return { success: false, message: "Invalid status" };
  }

  const user = await getUserSession();
  if (!user) {
    return { success: false, message: "User not found" };
  }

  const reportData = await getReportData(reportId);
  if (!reportData) {
    return { success: false, message: "Report not found" };
  }

  const reportCaseData = await getReportCase(reportData.id);
  if (!reportCaseData) {
    return { success: false, message: "Report case not found" };
  }

  if (reportCaseData.adminUserId !== user.userId && reportCaseData.status !== "OPEN") {
    return { success: false, message: "You are not authorized to update this report" };
  }

  await prisma.reportCase.update({
    where: {
      id: reportCaseData.id,
    },
    data: {
      status: status,
      adminUserId: user.userId,
    },
  });

  await createNotification(reportData.userId, "Report status updated", "REPORT")
  await createLog(user.userId, LogConstants.ADMIN_ACTION_PREFIX+LogConstants.ADMIN_REPORTS_UPDATED, LogActions.REPORT);

  return { success: true, message: "Report status updated" };
}
