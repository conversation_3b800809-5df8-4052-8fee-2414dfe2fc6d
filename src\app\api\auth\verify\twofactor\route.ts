import {NextRequest, NextResponse} from "next/server";
import {getUserSession} from "@/server/session";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function POST(request: NextRequest) {
    const {token} = await request.json();
    if (!token){
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No token provided."});
    }

    const userSession = await getUserSession();
    if (!userSession ||!userSession.accessToken || !userSession.userId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "Invalid user session."})
    }

    const verifyResponse = await fetch(process.env.VIDEO_API_URL + "/two-factor/verify", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${userSession.accessToken}`,
            "x-api-key": process.env.API_SERVER_KEY!
        },
        body: JSON.stringify({
            purpose: 'login',
            token
        })
    });

    const data = await verifyResponse.json();
    if (!verifyResponse.ok){
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    const session = await getUserSession();
    session.userId = data.user.id;
    session.accessToken = data.accessToken;
    await session.save();


    return NextResponse.json({status: HttpStatusCode.Ok, data: data});
}