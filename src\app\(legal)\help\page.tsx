"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import {
  Search,
  Upload,
  Share2,
  HelpCircle,
  MessageCircle,
  Mail,
  UserPlus,
  Play,
  Settings,
  Shield,
  ArrowRight,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import AOS from "aos";
import "aos/dist/aos.css";

const faqs = [
  {
    id: 1,
    question: "How do I upload videos?",
    answer:
      'You can upload videos by clicking the "Upload" button in your dashboard. We support most video formats and files up to 10GB. The upload process is simple: drag and drop your file or click to browse, add a title and description, and click upload.',
    icon: Upload,
    category: "Upload",
    gradient: "from-blue-400 to-cyan-500",
  },
  {
    id: 2,
    question: "What video formats are supported?",
    answer:
      "We support MP4, MOV, AVI, MKV, and most other common video formats. Videos are automatically transcoded for optimal playback across all devices. Maximum file size is 10GB for free users and 50GB for premium users.",
    icon: Play,
    category: "Technical",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    id: 3,
    question: "How do I share my videos?",
    answer:
      "Each video has a unique sharing link that you can copy and share anywhere. You can also embed videos on your website using our responsive embed code. Privacy settings allow you to control who can view your content.",
    icon: Share2,
    category: "Sharing",
    gradient: "from-purple-400 to-pink-500",
  },
  {
    id: 4,
    question: "How secure are my uploads?",
    answer:
      "All uploads are encrypted during transfer and storage. We use industry-standard security measures including SSL encryption, secure servers, and regular security audits to protect your content.",
    icon: Shield,
    category: "Security",
    gradient: "from-orange-400 to-red-500",
  },
  {
    id: 5,
    question: "Can I customize my profile?",
    answer:
      "Yes! You can customize your profile with a profile picture, bio, social media links, and custom branding. Premium users get access to additional customization options and themes.",
    icon: Settings,
    category: "Account",
    gradient: "from-indigo-400 to-purple-500",
  },
];

const steps = [
  {
    title: "Create an Account",
    description:
      "Sign up for a free account to start uploading and sharing your content.",
    icon: UserPlus,
    gradient: "from-green-400 to-emerald-500",
  },
  {
    title: "Upload Your First Video",
    description:
      "Click the upload button and select a video file from your device.",
    icon: Upload,
    gradient: "from-blue-400 to-indigo-500",
  },
  {
    title: "Share Your Content",
    description:
      "Use the sharing options to distribute your video across the web.",
    icon: Share2,
    gradient: "from-purple-400 to-pink-500",
  },
];

const categories = [
  "All",
  "Upload",
  "Technical",
  "Sharing",
  "Security",
  "Account",
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  let filteredFaqs = faqs;

  if (selectedCategory !== "All") {
    filteredFaqs = faqs.filter((faq) => faq.category === selectedCategory);
  }

  if (searchQuery.trim()) {
    filteredFaqs = filteredFaqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Help Center
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          How Can We
          <br />
          Help You?
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
          Find answers to common questions, learn how to use our platform, and
          get the support you need.
        </p>

        {/* Search Bar */}
        <div className="relative max-w-2xl mx-auto">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl" />
          <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-2">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-gray-400" />
              <Input
                className="h-14 w-full rounded-xl border-0 bg-transparent pl-14 pr-4 text-lg text-white placeholder:text-gray-400 focus:ring-2 focus:ring-purple-500/50"
                placeholder="Search help articles, guides, and FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Category Filter */}
      <motion.div
        className="flex flex-wrap justify-center gap-3"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => {
              setSelectedCategory(category);
              setExpandedFaq(null);
            }}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
              selectedCategory === category
                ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25"
                : "bg-white/5 border border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
            }`}
          >
            {category}
          </button>
        ))}
      </motion.div>

      {/* FAQ Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Quick answers to the most common questions about StreamBliss
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-4">
          {filteredFaqs.length > 0 ? (
            filteredFaqs.map((faq, index) => {
              const IconComponent = faq.icon;
              return (
                <div
                  key={faq.id}
                  className="group relative"
                  data-aos="fade-up"
                  data-aos-duration="600"
                  data-aos-delay={index * 50}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl overflow-hidden transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                    <button
                      onClick={() =>
                        setExpandedFaq(expandedFaq === faq.id ? null : faq.id)
                      }
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-300"
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${faq.gradient} bg-opacity-20`}
                        >
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-white group-hover:text-gray-100 transition-colors">
                            {faq.question}
                          </h3>
                          <span className="text-sm text-purple-400 font-medium">
                            {faq.category}
                          </span>
                        </div>
                      </div>
                      {expandedFaq === faq.id ? (
                        <ChevronUp className="h-6 w-6 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-gray-400" />
                      )}
                    </button>

                    {expandedFaq === faq.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-white/10"
                      >
                        <div className="p-6 pt-4">
                          <p className="text-gray-300 leading-relaxed pl-16">
                            {faq.answer}
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-center py-12">
              <HelpCircle className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">
                No results found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search terms or category filter
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Getting Started Guide */}
      <motion.div
        className="space-y-12"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center">
          <div className="inline-block mb-4">
            <div className="px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20">
              <span className="text-sm font-medium text-purple-400">
                Quick Start
              </span>
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Getting Started in 3 Steps
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            New to StreamBliss? Follow these simple steps to get up and running
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          {/* Connection Line */}
          <div className="hidden md:block absolute top-20 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500/20 via-pink-500/40 to-purple-500/20" />

          <div className="grid gap-8 md:grid-cols-3">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                className="group relative text-center"
                data-aos="fade-up"
                data-aos-duration="600"
                data-aos-delay={index * 100}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-8 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                  {/* Step Number */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>

                  <div
                    className={`mx-auto flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-r ${step.gradient} bg-opacity-20 mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <step.icon className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-gray-100 transition-colors">
                    {step.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Contact Support Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Still Need Help?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Our support team is here to assist you with any questions or
              issues you may have.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button
                asChild
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
              >
                <Link href="/contact" className="inline-flex items-center">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Contact Support
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button
                variant="outline"
                asChild
                className="border-gray-600 text-gray-300 hover:bg-white/10 hover:border-gray-500 transition-all duration-300"
              >
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center"
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Email Us
                </a>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}