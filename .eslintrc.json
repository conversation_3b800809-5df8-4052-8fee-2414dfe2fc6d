{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "next/core-web-vitals", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"react/prop-types": "off", "no-undef": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react/react-in-jsx-scope": "off", "no-unused-vars": "off", "no-extra-semi": "off", "no-case-declarations": "off", "prefer-const": "warn"}, "overrides": [{"files": ["src/generated/**/*"], "rules": {"@typescript-eslint/no-require-imports": "off"}}], "ignorePatterns": ["src/generated/**"]}