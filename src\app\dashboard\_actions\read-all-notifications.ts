"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { updateNotifications } from "@/server/notifications";
import { getUserSession } from "@/server/session";

type Callback = {
  success: boolean;
  message?: string;
};

export default async function submitReadAllNotifications(): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  const userSession = await getUserSession();
  if (!userSession) {
    return {
      success: false,
      message: "User session not found",
    };
  };

  const user = await getUserById(userSession.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found",
    };
  }

  await updateNotifications(user.id, { read: true });

  return {
    success: true,
  }
};
