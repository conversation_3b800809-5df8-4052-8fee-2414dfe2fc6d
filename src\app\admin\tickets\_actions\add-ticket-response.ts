"use server"

import { prisma } from "@/lib/prisma"
import { getUserSession } from "@/server/session"
import { revalidatePath } from "next/cache"
import { createNotification } from "@/server/notifications"
import {LogActions, NotificationType} from "@prisma/client"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

export default async function submitTicketResponse({
  ticketId,
  content,
}: {
  ticketId: string
  content: string
}) {
  try {
    const session = await getUserSession()
    if (!session?.userId) {
      return {
        success: false,
        message: "Not authenticated",
      }
    }

    const ticket = await prisma.ticket.findUnique({
      where: { id: ticketId },
      select: { userId: true },
    })

    if (!ticket) {
      return {
        success: false,
        message: "Ticket not found",
      }
    }

    const data = await prisma.$transaction(async (tx) => {
      const newResponse = await tx.ticketResponse.create({
        data: {
          content,
          ticketId,
          userId: session.userId,
        },
      })

      await tx.ticketActivity.create({
        data: {
          id: newResponse.id,
          ticketId,
          userId: session.userId,
          action: "RESPONDED",

        },
      })

      return newResponse
    })

    if (session.userId !== ticket.userId) {
      await createNotification(ticket.userId, "New response on your support ticket", NotificationType.INFO)
    }

    revalidatePath(`/admin/tickets/${ticketId}`)
    await createLog(session.userId, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_TICKETS_RESPOND, LogActions.TICKET);

    return {
      success: true,
      message: "Response added successfully",
      messageId: data.id
    }
  } catch (error) {
    console.error("[ADD_TICKET_RESPONSE]", error)
    return {
      success: false,
      message: "Something went wrong",
    }
  }
}