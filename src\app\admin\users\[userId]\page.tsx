import { notFound, redirect } from "next/navigation";
import { getUserById } from "@/lib/db/user";
import { getAdminUser, getUserSession } from "@/server/session";
import { getRoles } from "@/server/role";
import { getBanReasons } from "@/server/ban-reasons";
import { prisma } from "@/lib/prisma";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { UserActions } from "../_components/user-actions";
import { getLoginHistoryLogs } from "@/server/login-history";
import { getImageByUserId } from "@/server/image";
import { loadUserStripeData } from "@/server/subscription";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import UserDetailsContent from "./_components/user-details-content";
import UserImagesContent from "./_components/user-images-content";
import UserVideosContent from "./_components/user-videos-content";
import UserSubscriptionContent from "./_components/user-subscription-content";
import UserLogsContent from "./_components/user-logs-content";
import { getUserLogs } from "@/server/logs";

export default async function UserDetailPage(props) {
  const params = await props.params;
  const searchParams = (await props.searchParams) || {};
  const admin = await getAdminUser(["USER_PAGE"]);
  if (!admin) {
    redirect("/admin");
  }

  const userSession = await getUserSession();
  if (!userSession) {
    redirect("/admin");
  }

  const activeUser = await getUserById(userSession.userId);
  if (!activeUser) {
    redirect("/admin");
  }

  const user = await getUserById(params.userId);
  if (!user) {
    notFound();
  }

  const roles = await getRoles();
  const banReasons = await getBanReasons();

  const [totalVideos, totalImages] = await Promise.all([
    prisma.video.count({
      where: { userId: user.id },
    }),
    prisma.image.count({
      where: { userId: user.id },
    }),
  ]);

  const VIDEOS_PER_PAGE = 10;
  const IMAGES_PER_PAGE = 10;
  const videoPage = Math.max(1, Number(searchParams.videoPage) || 1);
  const imagePage = Math.max(1, Number(searchParams.imagePage) || 1);

  const videos = await prisma.video.findMany({
    where: {
      userId: user.id,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: VIDEOS_PER_PAGE,
    skip: (videoPage - 1) * VIDEOS_PER_PAGE,
  });

  const loginHistory = await getLoginHistoryLogs(user.id);
  const images = await getImageByUserId(user.id, {
    take: IMAGES_PER_PAGE,
    skip: (imagePage - 1) * IMAGES_PER_PAGE,
  });

  const permissions = activeUser.role.permissions || [];
  const hasPermission = (permission) => {
    return permissions.some(
      (perm) =>
        perm.permission.name === permission || perm.permission.name === "ALL",
    );
  };

  const { planName, subscriptionData, invoiceHistory } =
    await loadUserStripeData(user.stripeAccountId);
  const userLogs = await getUserLogs(user.id);

  return (
    <div className="space-y-6">
      {/* Back Navigation */}
      <div className="flex items-center gap-2">
        <Link
          href="/admin/users"
          className="flex items-center text-sm text-gray-400 hover:text-purple-400 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Users
        </Link>
      </div>

      {/* Page Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div>
            <h1 className="text-xl font-bold text-white">User Details</h1>
            <p className="text-sm text-gray-400 mt-1">
              Detailed information for {user.name || user.email}
            </p>
          </div>
          <UserActions user={user} roles={roles} banReasons={banReasons} />
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="bg-gray-900/30 border border-gray-800/40 rounded-xl p-1 w-full justify-start overflow-x-auto">
          <TabsTrigger
            value="details"
            className="data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
          >
            Details
          </TabsTrigger>
          {hasPermission("ADMIN_SEE_IMAGE_HISTORY") && (
            <TabsTrigger
              value="images"
              className="data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              Images
            </TabsTrigger>
          )}
          {hasPermission("ADMIN_SEE_VIDEO_HISTORY") && (
            <TabsTrigger
              value="videos"
              className="data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              Videos
            </TabsTrigger>
          )}
          {hasPermission("ADMIN_SEE_USER_SUBSCRIPTION") && (
            <TabsTrigger
              value="subscription"
              className="data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              Subscription
            </TabsTrigger>
          )}
          {hasPermission("ADMIN_SEE_USER_LOGS") && (
            <TabsTrigger
              value="logs"
              className="data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              Logs
            </TabsTrigger>
          )}
        </TabsList>

        <div className="mt-6">
          <TabsContent value="details" className="mt-0">
            <UserDetailsContent
              user={user!}
              loginHistory={loginHistory}
              canSeeLoginHistory={hasPermission("ADMIN_SEE_LOGIN_HISTORY")}
            />
          </TabsContent>
          {hasPermission("ADMIN_SEE_IMAGE_HISTORY") && (
            <TabsContent value="images" className="mt-0">
              <UserImagesContent
                images={images}
                totalImages={totalImages}
                imagePage={imagePage}
                ITEMS_PER_PAGE={IMAGES_PER_PAGE}
              />
            </TabsContent>
          )}
          {hasPermission("ADMIN_SEE_VIDEO_HISTORY") && (
            <TabsContent value="videos" className="mt-0">
              <UserVideosContent
                videos={videos}
                user={user}
                videoPage={videoPage}
                totalVideos={totalVideos}
                ITEMS_PER_PAGE={VIDEOS_PER_PAGE}
              />
            </TabsContent>
          )}
          {hasPermission("ADMIN_SEE_USER_SUBSCRIPTION") && (
            <TabsContent value="subscription" className="mt-0">
              <UserSubscriptionContent
                user={user}
                subscriptionData={subscriptionData}
                invoiceHistory={invoiceHistory}
                planName={planName}
              />
            </TabsContent>
          )}
          {hasPermission("ADMIN_SEE_USER_LOGS") && (
            <TabsContent value="logs" className="mt-0">
              <UserLogsContent logs={userLogs} />
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
}