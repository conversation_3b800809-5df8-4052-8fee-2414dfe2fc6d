import { getVideoDataByShortLink } from "@/server/video";
import { getUserSession } from "@/server/session";
import { getUserById } from "@/lib/db/user";
import { getUserProfilePicture } from "@/server/profile";
import { getUserNotifications } from "@/server/notifications";
import { hasPermission } from "@/server/admin";
import VideoAnalyticsClient from "./client";
import { notFound } from "next/navigation";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function VideoAnalyticsPage({
  params: paramsPromise,
}: PageProps) {
  const params = await paramsPromise;
  const shortLink = params.id;
  const session = await getUserSession();

  if (!session?.userId) {
    notFound();
    return null;
  }

  const video = await getVideoDataByShortLink(shortLink);
  if (!video) {
    notFound();
    return null;
  }

  if (video.userId !== session.userId) {
    notFound();
    return null;
  }

  const user = await getUserById(session.userId);
  if (!user) {
    notFound();
    return null;
  }

  const userProfileImage = await getUserProfilePicture(session.userId);
  const allNotifications = await getUserNotifications(session.userId);
  const hasAccessToAdmin = await hasPermission(user.roleId, ["ADMIN"]);

  return (
    <VideoAnalyticsClient
      video={video}
      userId={session.userId}
      userName={user.name || user.email.split("@")[0]}
      userImage={userProfileImage}
      notifications={allNotifications}
      hasAccessToAdmin={hasAccessToAdmin}
      userSubscription={user.package || ""}
    />
  );
}