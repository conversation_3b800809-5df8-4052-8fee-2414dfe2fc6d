"use client";

export function AdminHeader() {
  return (
    <div className="bg-gray-900/40 border border-gray-700/50 rounded-lg p-4 hover:bg-gray-900/50 hover:border-gray-600/60 transition-all duration-200">
      <div className="flex items-center justify-between">
        <div>
          <h1
            className="text-xl font-bold tracking-tight mb-1"
            style={{
              background:
                "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              color: "transparent",
            }}
          >
            Admin Dashboard
          </h1>
          <p className="text-sm text-gray-300">
            Manage users and monitor platform statistics
          </p>
        </div>

        {/* Status indicator */}
        <div className="hidden md:flex items-center gap-2 px-3 py-1.5 rounded-full bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30">
          <div className="w-1.5 h-1.5 rounded-full bg-emerald-400 animate-pulse" />
          <span className="text-xs font-medium text-emerald-400">Online</span>
        </div>
      </div>
    </div>
  );
}