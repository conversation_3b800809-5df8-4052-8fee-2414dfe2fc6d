import type React from "react"
import type { Metada<PERSON> } from "next"
import { getVideoDataByShortLink } from "src/server/video"

interface LayoutProps {
  children: React.ReactNode
  params: Promise<{ id: string }>
}

export async function generateMetadata(props: LayoutProps): Promise<Metadata> {
  const params = await props.params;
  const videoData = await getVideoDataByShortLink(params.id)

  if (!videoData) {
    return {
      title: "Video Not Found",
    }
  }

  return {
    title: videoData.title,
    openGraph: {
      title: videoData.title,
      images: videoData.thumbnailUrl ? [videoData.thumbnailUrl] : [],
    },
    twitter: {
      card: "summary_large_image",
      title: videoData.title,
      images: videoData.thumbnailUrl ? [videoData.thumbnailUrl] : [],
    },
  }
}

export default function ShortLinkLayout({ children }: LayoutProps) {
  return children
}