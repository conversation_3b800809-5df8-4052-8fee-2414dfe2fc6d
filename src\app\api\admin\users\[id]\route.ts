import { NextResponse } from "next/server"
import { getAdminUser, getUser } from "@/server/session"
import { updateUser, deleteUser } from "@/lib/db/admin"

export async function PATCH(req: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getAdminUser(["ADMIN_PAGE"]);
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const data = await req.json()
    const updatedUser = await updateUser(params.id, data)

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("User update error:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(req: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getAdminUser(["ADMIN_PAGE"]);
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    await deleteUser(params.id)
    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("User delete error:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}