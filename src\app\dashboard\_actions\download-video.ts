"use server";

import { decryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { getUserSettings } from "@/server/user-settings";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Props = {
  videoId: string;
};

type Callback = {
  success: boolean;
  message: string;
};

export default async function submitDownloadVideo({ videoId }: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later.",
    };
  }

  if (!videoId) {
    return {
      success: false,
      message: "Video ID is required.",
    };
  }

  const userSession = await getUserSession();
  if (!userSession) {
    return {
      success: false,
      message: "You must be logged in to download videos.",
    };
  }

  const user = await getUserById(userSession.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found.",
    };
  }

  const userSettings = await getUserSettings(userSession.userId);
  if (!userSettings) {
    return {
      success: false,
      message: "User settings not found.",
    };
  }

  const token = decryptData(userSettings.secretToken, userSettings.secretTokenIv);
  if (!token) {
    return {
      success: false,
      message: "Invalid token.",
    };
  }

  const downloadUrl = `${process.env.VIDEO_API_URL}/video/${user.id}/${videoId}/download?token=${encodeURIComponent(token)}`;
  await createLog(user.id, LogConstants.DOWNLOADED_VIDEO, LogActions.VIDEO);

  return {
    success: true,
    message: downloadUrl,
  };
}
