"use client";

import { motion } from "framer-motion";
import { Scale } from "lucide-react";
import { useEffect } from "react";
import {
  Building2,
  Mail,
  MapPin,
  Shield,
  Phone,
  Globe,
  FileText,
  ArrowRight,
} from "lucide-react";
import AOS from "aos";
import "aos/dist/aos.css";

const companyInfo = [
  {
    icon: Building2,
    title: "Company Details",
    content: "Nocta Solutions",
    description: "Official registered business name",
    gradient: "from-blue-400 to-cyan-500",
  },
  {
    icon: MapPin,
    title: "Business Address",
    content: ["Europaring 90", "53757 Sankt Augustin", "Germany"],
    description: "Official business location",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    icon: Mail,
    title: "Contact Information",
    content: "<EMAIL>",
    href: "mailto:<EMAIL>",
    description: "General inquiries and support",
    gradient: "from-purple-400 to-pink-500",
  },
  {
    icon: Globe,
    title: "Website",
    content: "www.streambliss.cloud",
    href: "https://streambliss.cloud",
    description: "Official website",
    gradient: "from-orange-400 to-red-500",
  },
];

const legalSections = [
  {
    icon: FileText,
    title: "Legal Responsibility",
    content:
      "The contents of our pages have been created with the utmost care. However, we cannot guarantee the contents' accuracy, completeness, or topicality. According to statutory provisions, we are furthermore responsible for our own content on these web pages.",
    gradient: "from-indigo-400 to-purple-500",
  },
  {
    icon: Shield,
    title: "Disclaimer",
    content:
      "Responsibility for the content of external links (to web pages of third parties) lies solely with the operators of the linked pages. No violations were evident to us at the time of linking. Should any legal infringement become known to us, we will remove the respective link immediately.",
    gradient: "from-amber-400 to-orange-500",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function ImpressumPage() {
  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Legal Information
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Impressum
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Legal information and company details according to § 5 TMG (German
          Telemedia Act).
        </p>
      </motion.div>

      {/* Legal Notice */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl" />
        <div className="relative backdrop-blur-xl bg-blue-500/5 border border-blue-500/20 rounded-2xl p-6 md:p-8">
          <div className="flex items-start gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex-shrink-0">
              <Shield className="h-6 w-6 text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold mb-2 text-blue-200">
                Legal Notice
              </h2>
              <p className="text-blue-100/90 leading-relaxed">
                This page contains the legally required information about our
                company and services in accordance with German law (§ 5 TMG).
                This information is mandatory for all commercial websites
                operating in Germany.
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Company Information */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Company Information
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Essential business details and contact information
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {companyInfo.map((info, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 md:p-8 h-full transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div className="flex items-start gap-4">
                  <div
                    className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${info.gradient} bg-opacity-20 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <info.icon className="h-7 w-7 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gray-100 transition-colors">
                      {info.title}
                    </h3>
                    <p className="text-sm text-gray-400 mb-3">
                      {info.description}
                    </p>
                    <div className="text-gray-300">
                      {Array.isArray(info.content) ? (
                        info.content.map((line, i) => (
                          <p key={i} className="text-lg font-medium">
                            {line}
                          </p>
                        ))
                      ) : info.href ? (
                        <a
                          href={info.href}
                          className="text-lg font-medium text-purple-400 hover:text-purple-300 transition-colors inline-flex items-center"
                          target={
                            info.href.startsWith("http") ? "_blank" : undefined
                          }
                          rel={
                            info.href.startsWith("http")
                              ? "noopener noreferrer"
                              : undefined
                          }
                        >
                          {info.content}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </a>
                      ) : (
                        <p className="text-lg font-medium">{info.content}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Legal Sections */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Legal Information
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Important legal disclaimers and responsibility statements
          </p>
        </div>

        <div className="space-y-6">
          {legalSections.map((section, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 md:p-8 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div className="flex items-start gap-6">
                  <div
                    className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${section.gradient} bg-opacity-20 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <section.icon className="h-7 w-7 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-gray-100 transition-colors">
                      {section.title}
                    </h3>
                    <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors">
                      {section.content}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Dispute Resolution */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-start gap-6">
              <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-emerald-500/20 to-teal-500/20 flex-shrink-0">
                <Scale className="h-8 w-8 text-emerald-400" />
              </div>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Dispute Resolution
                </h2>
                <div className="space-y-4 text-gray-300 leading-relaxed">
                  <p className="text-lg">
                    The European Commission provides a platform for online
                    dispute resolution (OS) which is accessible at{" "}
                    <a
                      href="https://ec.europa.eu/consumers/odr/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-emerald-400 hover:text-emerald-300 transition-colors font-medium inline-flex items-center"
                    >
                      https://ec.europa.eu/consumers/odr/
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </a>
                  </p>
                  <p className="text-lg">
                    We are not obliged and not willing to participate in dispute
                    resolution proceedings before a consumer arbitration board.
                    However, we are committed to resolving any issues directly
                    with our users in a fair and timely manner.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Contact Section */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Questions About This Information?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed">
            If you have any questions about the legal information provided on
            this page, please don&apos;t hesitate to contact us.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25"
            >
              <Mail className="mr-2 h-5 w-5" />
              Contact Legal Team
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
            <span className="text-gray-400 font-medium">
              <EMAIL>
            </span>
          </div>
        </div>
      </motion.div>
    </div>
  );
}