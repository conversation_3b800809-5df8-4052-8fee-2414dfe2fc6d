import {getUserSession} from "@/server/session";
import {NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";

export async function GET() {
    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status:  HttpStatusCode.Forbidden, message: "No valid user session found."});
    }

    const getTicketsResponse = await fetch(process.env.VIDEO_API_URL + "/tickets/user", {
        method: "GET",
        headers: {
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        }
    });

    const data = await getTicketsResponse.json();
    if (!getTicketsResponse.ok) {
        return NextResponse.json({status: HttpStatusCode.InternalServerError, message: data.message});
    }

    return NextResponse.json({status: HttpStatusCode.Ok, data: data});
}