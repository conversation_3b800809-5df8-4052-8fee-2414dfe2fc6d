"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { Flag, AlertTriangle } from "lucide-react";

const reportReasons = [
  { id: "inappropriate", label: "Inappropriate content", icon: "🚫" },
  { id: "copyright", label: "Copyright violation", icon: "©️" },
  { id: "spam", label: "Spam or misleading", icon: "⚠️" },
  { id: "other", label: "Other", icon: "❓" },
];

interface ReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  imageId: string;
}

export function ReportDialog({
  open,
  onOpenChange,
  imageId,
}: ReportDialogProps) {
  const [reason, setReason] = useState("");
  const [details, setDetails] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success, error } = useEnhancedToast();

  const handleSubmit = async () => {
    if (!reason) {
      error("Reason Required", "Please select a reason");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/report/submit", {
        method: "POST",
        body: JSON.stringify({
          id: imageId,
          reason,
          details,
          type: 'image'
        }),
      })

      const data = await response.json();
      if (!(data.status >= 200 && data.status < 300)) {
        error("Report Failed", data.message);
        return;
      }

      success("Report Submitted", "Report submitted successfully");
      onOpenChange(false);
      setReason("");
      setDetails("");
    } catch (err) {
      error("Submit Error", "Failed to submit report");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg bg-gradient-to-br from-gray-900 via-gray-900 to-purple-900/20 border border-purple-500/20 text-white shadow-2xl">
        <DialogHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30">
              <Flag className="h-5 w-5 text-red-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-white">
                Report Content
              </DialogTitle>
              <p className="text-sm text-gray-400 mt-1">
                Help us maintain a safe community
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-4">
            <Label className="text-base font-medium text-white">
              Why are you reporting this image?
            </Label>
            <RadioGroup value={reason} onValueChange={setReason}>
              <div className="grid gap-3">
                {reportReasons.map((reportReason) => (
                  <div
                    key={reportReason.id}
                    className="flex items-center space-x-3 group"
                  >
                    <RadioGroupItem
                      value={reportReason.id}
                      id={reportReason.id}
                      className="border-gray-600 text-purple-400 focus:ring-purple-400"
                    />
                    <Label
                      htmlFor={reportReason.id}
                      className="flex-1 p-4 rounded-lg border border-gray-700 hover:border-purple-500/50 hover:bg-purple-500/5 transition-all cursor-pointer group-hover:bg-purple-500/5"
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-xl">{reportReason.icon}</span>
                        <span className="text-white font-medium">
                          {reportReason.label}
                        </span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-3">
            <Label
              htmlFor="details"
              className="text-base font-medium text-white"
            >
              Additional details (optional)
            </Label>
            <Textarea
              id="details"
              placeholder="Provide more context about why you're reporting this image..."
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              className="min-h-[100px] bg-gray-800/50 border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-500/50 focus:ring-purple-500/20 resize-none"
              maxLength={500}
            />
            <div className="text-xs text-gray-500 text-right">
              {details.length}/500
            </div>
          </div>

          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-200">
                <p className="font-medium mb-1">Please report responsibly</p>
                <p className="text-yellow-300">
                  False reports may result in action being taken against your
                  account. Only report content that violates our community
                  guidelines.
                </p>
              </div>
            </div>
          </div>

          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 cursor-pointer"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!reason || isSubmitting}
              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Submitting...
                </div>
              ) : (
                "Submit Report"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}