"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getClientIp } from "@/server/geolocation";
import { createNotification } from "@/server/notifications";
import { getRoleById, updateUserRole } from "@/server/role";
import { getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type Data = {
  userId: string;
  adminId: number;
}

export default async function submitMakeAdmin({ userId, adminId }: Data): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  const _user = await getUser();
  if (!_user) {
    return { success: false, message: "User not found" };
  }

  if (!userId) {
    return { success: false, message: "Invalid user ID" };
  }

  const user = await getUserById(userId);
  if (!user) {
    return { success: false, message: "User not found" };
  }

  const role = await getRoleById(adminId);
  if (!role) {
    return { success: false, message: "Role not found" };
  }

  const userRole = await getUserById(_user.id);
  if (!userRole) {
    return { success: false, message: "User Role not found" };
  }

  const userRoleName = await getRoleById(userRole.roleId);
  if (!userRoleName) {
    return { success: false, message: "User Role Name not found" };
  }

  const canPerform = await hasPermission(userRole.roleId, ["USER_ROLE_UPDATE"]);
  if (!canPerform) {
    return { success: false, message: "You do not have permission to change the users roles" };
  }

  if (role.level >= userRoleName.level) {
    return { success: false, message: "You cannot change the role of a user to a higher level than your own" };
  }

  await updateUserRole(userId, adminId);
  await createNotification(userId, "You have been given a new role", "INFO");
  await createLog(_user.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_CHANGED_ROLE, LogActions.ACCOUNT);

  return {
    success: true,
    message: "You have changed the users roles",
  }
}
