"use server"

import { prisma } from "@/lib/prisma"
import { getUserSession } from "@/server/session"
import { revalidatePath } from "next/cache"
import { createNotification } from "@/server/notifications"
import {LogActions, NotificationType} from "@prisma/client"
import { getClientIp } from "@/server/geolocation"
import { rateLimiter } from "@/lib/rate-limit"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

export default async function submitAssignTicket({
  ticketId,
  assignedTo,
}: {
  ticketId: string
  assignedTo: string | null
}) {
  try {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
      return {
        success: false,
        message: "You have exceeded the rate limit. Please try again later."
      }
    }

    const session = await getUserSession()
    if (!session?.userId) {
      return {
        success: false,
        message: "Not authenticated",
      }
    }

    const user = await prisma.user.findUnique({
      where: { id: session.userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    })

    const userPermissions = user?.role.permissions.map((p) => p.permission.name) || []
    const hasAccess =
      userPermissions.includes("ALL") || userPermissions.includes("TICKET_MANAGEMENT") || user?.roleId === 2 // Admin role

    if (!hasAccess) {
      return {
        success: false,
        message: "You don't have permission to assign tickets",
      }
    }

    const ticket = await prisma.ticket.findUnique({
      where: { id: ticketId },
      select: { assignedTo: true, userId: true },
    })

    if (!ticket) {
      return {
        success: false,
        message: "Ticket not found",
      }
    }

    await prisma.ticket.update({
      where: {
        id: ticketId,
      },
      data: {
        assignedTo,
        ...(assignedTo && { status: "IN_PROGRESS" }),
      },
    })

    await prisma.ticketActivity.create({
      data: {
        ticketId,
        userId: session.userId,
        action: assignedTo ? (assignedTo === session.userId ? "SELF_ASSIGNED" : "ASSIGNED") : "UNASSIGNED",
      },
    })

    if (assignedTo && ticket.userId !== session.userId) {
      await createNotification(ticket.userId, "Your ticket has been assigned to a support agent", NotificationType.REPORT)
    }

    if (assignedTo && assignedTo !== session.userId) {
      await createNotification(assignedTo, "A ticket has been assigned to you", NotificationType.REPORT)
    }

    revalidatePath(`/admin/tickets/${ticketId}`)
    revalidatePath("/admin/tickets")
    await createLog(user!.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_TICKETS_ASSIGN, LogActions.TICKET);

    return {
      success: true,
      message: assignedTo
        ? assignedTo === session.userId
          ? "Ticket self-assigned successfully"
          : "Ticket assigned successfully"
        : "Ticket unassigned successfully",
    }
  } catch (error) {
    console.error("[ASSIGN_TICKET]", error)
    return {
      success: false,
      message: "Something went wrong",
    }
  }
}