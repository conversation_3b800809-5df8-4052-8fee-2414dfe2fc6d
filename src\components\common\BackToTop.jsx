"use client";
import React, { useEffect, useState } from "react";
import Icons from "./Icons";
import NoSSR from "./NoSSR";

const BackToTopContent = () => {
  const [visible, setVisible] = useState(false);

  const toggleVisible = () => {
    const scrolled = document.documentElement.scrollTop;
    if (scrolled > 500) {
      setVisible(true);
    } else if (scrolled <= 500) {
      setVisible(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    window.addEventListener("scroll", toggleVisible);
    return () => {
      window.removeEventListener("scroll", toggleVisible);
    };
  }, []);
  return (
    <button
      className="group animate-bounce hover:animate-none flex justify-center md:w-11 w-9 h-9 md:h-11 bg-back-to-top group rounded-full items-center p-2 fixed lg:right-10 sm:right-5 right-4 md:bottom-12 sm:bottom-8 bottom-6 z-30 transition-all ease-out cursor-pointer hover:scale-110 duration-300 hover:shadow-xl hover:shadow-purple-500/25 magnetic-hover"
      onClick={scrollToTop}
      style={{
        display: visible ? "flex " : "none",
      }}
    >
      <Icons className="max-sm:size-5" icon="upArrow" />
    </button>
  );
};

const BackToTop = () => {
  return (
    <NoSSR>
      <BackToTopContent />
    </NoSSR>
  );
};

export default BackToTop;