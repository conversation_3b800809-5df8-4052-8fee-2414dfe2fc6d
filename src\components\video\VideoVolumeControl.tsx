"use client";

import React, { useState, useRef, useCallback } from "react";

export interface VideoVolumeControlProps {
  volume: number;
  isMuted: boolean;
  onVolumeChange: (volume: number) => void;
  orientation?: "horizontal" | "vertical";
  className?: string;
}

export const VideoVolumeControl: React.FC<VideoVolumeControlProps> = ({
  volume,
  isMuted,
  onVolumeChange,
  orientation = "horizontal",
  className = "",
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const displayVolume = isMuted ? 0 : volume;
  const percentage = displayVolume * 100;

  const getVolumeFromPosition = useCallback((clientX: number, clientY: number): number => {
    if (!sliderRef.current) return volume;
    
    const rect = sliderRef.current.getBoundingClientRect();
    let position: number;
    
    if (orientation === "horizontal") {
      position = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    } else {
      position = Math.max(0, Math.min(1, 1 - (clientY - rect.top) / rect.height));
    }
    
    return position;
  }, [orientation, volume]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    const newVolume = getVolumeFromPosition(e.clientX, e.clientY);
    onVolumeChange(newVolume);
  }, [getVolumeFromPosition, onVolumeChange]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      const newVolume = getVolumeFromPosition(e.clientX, e.clientY);
      onVolumeChange(newVolume);
    }
  }, [isDragging, getVolumeFromPosition, onVolumeChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Global mouse handlers for dragging
  React.useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => setIsDragging(false);
      const handleGlobalMouseMove = (e: MouseEvent) => {
        if (sliderRef.current) {
          const newVolume = getVolumeFromPosition(e.clientX, e.clientY);
          onVolumeChange(newVolume);
        }
      };

      document.addEventListener("mouseup", handleGlobalMouseUp);
      document.addEventListener("mousemove", handleGlobalMouseMove);

      return () => {
        document.removeEventListener("mouseup", handleGlobalMouseUp);
        document.removeEventListener("mousemove", handleGlobalMouseMove);
      };
    }
  }, [isDragging, getVolumeFromPosition, onVolumeChange]);

  if (orientation === "vertical") {
    return (
      <div
        className={`relative ${className}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div
          className={`transition-all duration-200 ${
            isHovered || isDragging ? "opacity-100 scale-100" : "opacity-0 scale-95"
          }`}
        >
          <div
            ref={sliderRef}
            className="w-1 h-20 bg-white/20 rounded-full cursor-pointer relative"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            {/* Volume Fill */}
            <div
              className="absolute bottom-0 w-full bg-gradient-to-t from-purple-500 to-pink-500 rounded-full transition-all duration-150"
              style={{ height: `${percentage}%` }}
            />
            
            {/* Volume Handle */}
            <div
              className={`absolute left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white rounded-full shadow-lg transition-all duration-150 ${
                isDragging ? "scale-125" : "scale-100"
              }`}
              style={{ bottom: `calc(${percentage}% - 6px)` }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <div
        ref={sliderRef}
        className="h-1 bg-white/20 rounded-full cursor-pointer relative w-full"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        {/* Volume Fill */}
        <div
          className="absolute left-0 top-0 h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-150"
          style={{ width: `${percentage}%` }}
        />

        {/* Volume Handle */}
        <div
          className={`absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-lg transition-all duration-150 ${
            isDragging ? "scale-125" : "scale-100"
          }`}
          style={{ left: `calc(${percentage}% - 6px)` }}
        />
      </div>
    </div>
  );
};

export default VideoVolumeControl;