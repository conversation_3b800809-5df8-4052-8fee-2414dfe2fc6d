import { prisma } from "@/lib/prisma";
import { $Enums } from "@prisma/client";
import { format, formatDistance } from "date-fns";

export const getMaintenance = async () => {
  const key = "MAINTENACE_ENABLED";

  const systemConfig = await prisma.systemSettings.findFirst({
    where: {
      key
    }
  });
  if (!systemConfig) {
    return false;
  }

  return systemConfig.value === "true";
};

export const setMaintenance = async (value: boolean) => {
  const key = "MAINTENACE_ENABLED";
  await prisma.systemSettings.upsert({
    where: {
      key
    },
    update: {
      value: value.toString()
    },
    create: {
      key,
      value: value.toString()
    }
  });
}

export const getMaintenanceDuration = async () => {
  const key = "MAINTENACE_DURATION";

  const systemConfig = await prisma.systemSettings.findFirst({
    where: {
      key
    }
  });
  if (!systemConfig) {
    return 0;
  }

  return parseInt(systemConfig.value);
};

export const setMaintenanceDuration = async (value: number) => {
  const key = "MAINTENACE_DURATION";
  await prisma.systemSettings.upsert({
    where: {
      key
    },
    update: {
      value: value.toString()
    },
    create: {
      key,
      value: value.toString()
    }
  });
}

export const getMaintenanceStart = async () => {
  const key = "MAINTENACE_START";
  const systemConfig = await prisma.systemSettings.findFirst({
    where: {
      key
    }
  });
  if (!systemConfig) {
    return new Date();
  }

  return new Date(systemConfig.value);
}

export const setMaintenanceStart = async (value: Date) => {
  const key = "MAINTENACE_START";
  await prisma.systemSettings.upsert({
    where: {
      key
    },
    update: {
      value: value.toISOString()
    },
    create: {
      key,
      value: value.toISOString()
    }
  });
}

export const getMaintenanceEnd = async () => {
  const key = "MAINTENACE_END";
  const systemConfig = await prisma.systemSettings.findFirst({
    where: {
      key
    }
  });
  if (!systemConfig) {
    return new Date();
  }

  return new Date(systemConfig.value);
}

export const setMaintenanceEnd = async (value: Date) => {
  const key = "MAINTENACE_END";
  await prisma.systemSettings.upsert({
    where: {
      key
    },
    update: {
      value: value.toISOString()
    },
    create: {
      key,
      value: value.toISOString()
    }
  });
}

type MaintenanceData = {
  enabled: boolean;
  duration: string;
  updates: { timestamp: string, message: string, type: $Enums.MaintenanceTypes }[];
};

export const getMaintenanceData = async (): Promise<MaintenanceData> => {
  const enabled = await getMaintenance();
  const duration = await getMaintenanceDuration();
  const startDate = await getMaintenanceStart();
  const databaseUpdates = await getMaintenanceUpdates();
  databaseUpdates.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  const updates = databaseUpdates.map((update) => {
    return {
      timestamp: format(update.createdAt, "hh:mm a"),
      message: update.title,
      type: update.type
    }
  });

  const formattedDuration = formatDistance(0, duration * 1000, { includeSeconds: true });

  return {
    enabled,
    duration: formattedDuration,
    updates
  }
};

export const addMaintenanceUpdate = async (message: string, type: $Enums.MaintenanceTypes) => {
  return await prisma.maintenanceUpdates.create({
    data: {
      title: message,
      type: type
    }
  })
}

export const getMaintenanceUpdates = async () => {
  return await prisma.maintenanceUpdates.findMany({
    orderBy: {
      createdAt: "desc"
    }
  })
}

export const clearMaintenanceUpdates = async () => {
  return await prisma.maintenanceUpdates.deleteMany({});
}
