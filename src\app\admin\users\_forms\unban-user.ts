"use server";

import { getUserById, isUserBanned } from "@/lib/db/user";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from "@/lib/rate-limit";
import { hasPermission } from "@/server/admin";
import { getBanReason } from "@/server/ban-reasons";
import { getClientIp } from "@/server/geolocation";
import { getAdminUser, getUser } from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
  success: boolean;
  message: string;
};

type Params = {
  userId: string;
}

export default async function submitUnBanUser({ userId }: Params): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!userId) {
    return { success: false, message: "User ID is required" };
  }

  const userSession = await getUser();
  if (!userSession) {
    return { success: false, message: "No user session found" };
  }

  const adminUser = await getUserById(userSession.id);
  if (!adminUser) {
    return { success: false, message: "No admin user found" };
  }

  const user = await hasPermission(adminUser.roleId, ["ADMIN_UNBAN"]);
  if (!user) {
    return {
      success: false,
      message: "You are not authorized to unban users",
    }
  }

  const target = await getUserById(userId);
  if (!target) {
    return { success: false, message: "User not found" };
  }

  if (!isUserBanned(target.id)) {
    return { success: false, message: "User is not banned" };
  }

  const banData = await getBanReason(target.id);
  if (!banData) {
    return { success: false, message: "Ban reason not found" };
  }

  await prisma.user.update({
    where: {
      id: target.id
    },
    data: {
      banReason: {
        delete: {
          id: banData.id
        }
      }
    }
  })

  await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_UNBAN, LogActions.ACCOUNT);

  return { success: true, message: "Unban user success" };
}
