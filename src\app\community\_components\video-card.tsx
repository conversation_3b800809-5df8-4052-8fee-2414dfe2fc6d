"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import type { Video } from "@/types/video";
import { VideoModal } from "./video-modal";
import { Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

const API_URL = process.env.VIDEO_API_URL || "https://api.streambliss.cloud";

interface VideoCardProps {
  video: Video;
  hideUploader?: boolean;
  size?: "normal" | "large" | "wide" | "tall";
  isWide?: boolean;
  quality?: string;
  onClick?: (video: Video) => void;
}

export function VideoCard({
  video,
  hideUploader = false,
  size = "normal",
  isWide = false,
  quality = "",
  onClick,
}: VideoCardProps) {
  const [open, setOpen] = useState(false);
  const [hovered, setHovered] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  const [imgError, setImgError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const router = useRouter();

  const thumbnail = `${API_URL}/video/thumbnail/${video.userId}/${video.id}`;
  const videoSrc = `${API_URL}/video/stream/${video.userId}/${video.id}`;
  const uploader = video.user?.name || "Unknown";
  const uploaderImage = video.user?.image;
  const displayTitle =
    video.title && video.title.length > 0 ? video.title : "Clip by " + uploader;

  useEffect(() => {
    if (hovered) {
      const timer = setTimeout(() => {
        setVideoVisible(true);
      }, 50);
      return () => clearTimeout(timer);
    } else {
      setVideoVisible(false);
    }
  }, [hovered]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (videoVisible) {
      video.currentTime = 0;
      video.muted = false;
      video.volume = 0.3;

      video.play().catch((err) => {
        console.log("Video play with sound failed, trying muted:", err.message);
        video.muted = true;
        video.play().catch((mutedErr) => {
          console.log("Muted video play also failed:", mutedErr.message);
        });
      });
    } else {
      if (!video.paused) {
        video.pause();
      }
    }
  }, [videoVisible]);

  const handleCardClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest(".user-info-clickable")) {
      return;
    }
    router.push(`/v/${video.shortLink}`);
  };

  const containerClasses = `
    group relative w-full h-full cursor-pointer overflow-hidden bg-zinc-900 rounded-xl border border-white/10 transition-all duration-300 hover:border-purple-500/30 hover:shadow-xl hover:shadow-purple-500/20
  `.trim();

  const getAspectRatio = () => {
    switch (size) {
      case "wide":
        return "aspect-[16/9]";
      case "tall":
        return "aspect-[9/16]";
      case "large":
        return "aspect-[16/12]";
      default:
        return "aspect-video";
    }
  };

  const formattedDuration = (duration: number) => {
    if (!duration) return "00:00";
    const totalSeconds = Math.floor(duration);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    if (hours > 0) {
      return `${hours}:${minutes < 10 ? "0" : ""}${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
    }

    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  return (
    <div
      className={containerClasses}
      style={{ height: "100%" }}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={handleCardClick}
    >
      <div className={`relative w-full h-full ${getAspectRatio()}`}>
        {imgError ? (
          <div className="absolute inset-0 w-full h-full bg-zinc-800 flex items-center justify-center">
            <span className="text-zinc-400 text-sm">Thumbnail unavailable</span>
          </div>
        ) : (
          <Image
            src={thumbnail || "/placeholder.svg"}
            alt={displayTitle}
            fill
            sizes={size === "large" ? "50vw" : "33vw"}
            className="object-cover"
            priority={size === "large"}
            loading={size === "large" ? "eager" : "lazy"}
            onError={() => setImgError(true)}
          />
        )}

        {videoVisible && (
          <video
            ref={videoRef}
            src={videoSrc}
            className="absolute inset-0 w-full h-full object-cover z-[1]"
            muted
            loop
            playsInline
            preload="metadata"
          />
        )}
      </div>

      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent group-hover:from-black/95 group-hover:via-black/40 transition-all duration-300 pointer-events-none z-[2]"></div>
      <div className="absolute bottom-2 right-2 bg-black/70 text-white px-1.5 py-0.5 rounded-sm text-xs font-medium z-[3]">
        {formattedDuration(video.duration!)}
      </div>

      <div className="absolute bottom-0 left-0 w-full p-3 pointer-events-none z-[3]">
        <h2
          className={`text-white font-bold leading-tight break-words drop-shadow-md ${
            size === "large" ? "text-xl" : "text-base"
          }`}
          style={{ textShadow: "0px 1px 2px rgba(0, 0, 0, 0.9)" }}
        >
          {displayTitle.length > 50
            ? displayTitle.substring(0, 47) + "..."
            : displayTitle}
        </h2>
      </div>

      <div className="absolute left-0 top-0 w-full flex items-center justify-between p-2 bg-gradient-to-b from-black/60 via-black/30 to-transparent pointer-events-none z-[3]">
        {!hideUploader && (
          <Link
            href={`/community/u/${uploader}`}
            className="user-info-clickable flex items-center gap-1.5 pointer-events-auto hover:opacity-80 transition-opacity"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {uploaderImage && !avatarError ? (
              <div className="relative w-6 h-6">
                <Image
                  src={uploaderImage || "/placeholder.svg"}
                  alt={uploader}
                  fill
                  className="rounded-full object-cover border border-white/30"
                  onError={() => setAvatarError(true)}
                />
              </div>
            ) : (
              <div className="w-6 h-6 rounded-full bg-primary/70 flex items-center justify-center border border-white/30">
                <span className="text-[10px] text-white font-semibold">
                  {uploader.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <span className="text-white text-xs font-medium truncate max-w-[100px]">
              {uploader}
            </span>
          </Link>
        )}
        <div className="flex items-center gap-1 text-white text-xs font-medium">
          <Eye className="w-3.5 h-3.5" />
          <span>
            {video.views >= 1000
              ? `${(video.views / 1000).toFixed(video.views % 1000 === 0 ? 0 : 1)}K`
              : video.views}
          </span>
        </div>
      </div>

      <div
        className={`absolute inset-0 flex items-center justify-center pointer-events-none z-[3] transition-opacity duration-200 ${hovered ? "opacity-0" : "opacity-100"}`}
      >
        <div
          className={`w-12 h-12 rounded-full bg-primary/80 flex items-center justify-center shadow-lg transform scale-0 group-hover:scale-100 transition-transform duration-200`}
        >
          <svg
            className="w-6 h-6 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M8 5v14l11-7z" />
          </svg>
        </div>
      </div>

      {open && (
        <VideoModal
          isOpen={open}
          onClose={() => setOpen(false)}
          video={video}
        />
      )}
    </div>
  );
}