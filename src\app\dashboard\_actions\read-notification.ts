"use server";

import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getNotificationById, updateNotification } from "@/server/notifications";
import { getUserSession } from "@/server/session";

type Callback = {
  success: boolean;
  message?: string;
};

type Props = {
  notificationId: string;
};

export default async function submitReadNotification({ notificationId }: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  if (!notificationId) {
    return {
      success: false,
      message: "Notification ID is required",
    };
  }

  const userSession = await getUserSession();
  if (!userSession) {
    return {
      success: false,
      message: "User session not found",
    };
  };

  const user = await getUserById(userSession.userId);
  if (!user) {
    return {
      success: false,
      message: "User not found",
    };
  }

  const notificationData = await getNotificationById(notificationId);
  if (!notificationData) {
    return {
      success: false,
      message: "Notification not found",
    };
  }

  await updateNotification(notificationId, { read: true });

  return {
    success: true,
  }
};
