import { notFound } from "next/navigation";
import {getUser, getUserSession} from "@/server/session";
import { prisma } from "@/lib/prisma";
import DashboardTicketDetailClient from "./client";
import { getUserProfilePicture } from "@/server/profile";
import { getUserById } from "@/lib/db/user";
import { hasPermission } from "@/server/admin";

export default async function TicketPage(props: {
  params: Promise<{ ticketId: string }>;
}) {
  const params = await props.params;
  const user = await getUser();
  if (!user) {
    notFound();
  }

  const userSession = await getUserSession();
  if (!userSession) {
    notFound();
  }

  const ticketResponse = await fetch(process.env.VIDEO_API_URL + "/tickets/by-id/" + params.ticketId, {
    method: "GET",
    headers: {
      "Authorization": "Bearer " + userSession.accessToken,
      "x-api-key": process.env.API_SERVER_KEY!,
    }
  });

  if (!ticketResponse) {
    notFound();
  }

  const ticket = await ticketResponse.json();
  if (ticket.id === null) {
    notFound();
  }

  const [allNotifications, dbUser] = await Promise.all([
    prisma.notifications.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 20,
    }),
    getUserById(user.id),
  ]);

  const hasAccessToAdmin = dbUser
    ? await hasPermission(dbUser.roleId, ["ADMIN_PAGE"])
    : false;
  const userProfileImage = await getUserProfilePicture(user.id);

  if (!ticket) {
    notFound();
  }

  const userAvatar = await getUserProfilePicture(ticket.user.id);
  const responseUserAvatars = await Promise.all(
    ticket.responses.map((r) => getUserProfilePicture(r.user.id)),
  );
  const activityUserAvatars = await Promise.all(
    ticket.activities.map((a) => getUserProfilePicture(a.user.id)),
  );

  const transformedTicket = {
    ...ticket,
    user: {
      ...ticket.user,
      image: userAvatar,
    },
    responses: ticket.responses.map((response, index) => ({
      ...response,
      user: {
        ...response.user,
        image: responseUserAvatars[index],
      },
    })),
    activities: ticket.activities.map((activity, index) => ({
      ...activity,
      user: {
        ...activity.user,
        image: activityUserAvatars[index],
      },
    })),
  };

  const assignedTo = ticket.assignedTo
    ? await prisma.user.findUnique({
        where: { id: ticket.assignedTo },
        select: {
          id: true,
          name: true,
          email: true,
          roleId: true,
        },
      })
    : null;

  const assignedToAvatar = assignedTo
    ? await getUserProfilePicture(assignedTo.id)
    : null;

  const transformedAssignedTo = assignedTo
    ? {
        ...assignedTo,
        image: assignedToAvatar,
      }
    : null;

  return (
    <DashboardTicketDetailClient
      userId={user.id}
      userName={user.name || user.email.split("@")[0]}
      userImage={userProfileImage}
      notifications={allNotifications}
      hasAccessToAdmin={hasAccessToAdmin}
      ticket={{ ...transformedTicket, assignedTo: transformedAssignedTo }}
    />
  );
}