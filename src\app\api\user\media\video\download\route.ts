import {NextRequest, NextResponse} from "next/server";
import {HttpStatusCode} from "@/types/httpStatusCodes";
import {getUserSession} from "@/server/session";

export async function POST(request: NextRequest) {
    const {videoId} = await request.json();
    if (!videoId) {
        return NextResponse.json({status: HttpStatusCode.UnprocessableEntity, message: "No Video ID provided."});
    }

    const userSession = await getUserSession();
    if (!userSession) {
        return NextResponse.json({status: HttpStatusCode.Conflict, message: "No user session found."});
    }

    const videoDownloadRequest = await fetch(process.env.VIDEO_API_URL + "/videos/download/" + videoId, {
        method: "GET",
        headers: {
            "Authorization": "Bearer " + userSession.accessToken,
            "x-api-key": process.env.API_SERVER_KEY!
        }
    });

    if (!videoDownloadRequest.ok || !videoDownloadRequest.body) {
        const errorText = await videoDownloadRequest.text();
        return new NextResponse(errorText, { status: 500 });
    }

    const headers = new Headers();
    headers.set('Content-Type', 'video/mp4');
    headers.set('Content-Disposition', `attachment; filename="${videoId}.mp4"`);

    return new NextResponse(videoDownloadRequest.body, {
        status: 200,
        headers,
    });
}