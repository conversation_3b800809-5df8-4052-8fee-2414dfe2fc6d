"use server"

import { getUser } from "@/server/session"
import { prisma } from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import {LogActions, NotificationType} from "@prisma/client"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

export default async function closeTicket(ticketId: string) {
  try {
    const user = await getUser()
    if (!user) {
      return { success: false, message: "Unauthorized" }
    }

    const ticket = await prisma.ticket.update({
      where: {
        id: ticketId,
        userId: user.id,
      },
      data: {
        status: "CLOSED",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    await prisma.ticketActivity.create({
      data: {
        ticketId,
        userId: user.id,
        action: "STATUS_CHANGED_TO_CLOSED",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    await prisma.notifications.create({
      data: {
        userId: ticket.userId,
        type: "INFO",
        data: `Your ticket #${ticket.id.slice(0, 8)} has been closed.`,
      },
    })
    await createLog(user.id, LogConstants.CLOSED_SUPPORT_TICKET, LogActions.TICKET);

    revalidatePath(`/dashboard/tickets/${ticketId}`)
    return { success: true, message: "Ticket closed successfully" }
  } catch (error) {
    console.error("Error closing ticket:", error)
    return { 
      success: false, 
      message: error instanceof Error ? error.message : "Failed to close ticket" 
    }
  }
}