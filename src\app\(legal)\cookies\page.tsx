"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Setting<PERSON>,
  Check,
  X,
  ArrowRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import AOS from "aos";
import "aos/dist/aos.css";

const cookieTypes = [
  {
    id: "essential",
    icon: Shield,
    title: "Essential Cookies",
    description:
      "These cookies are necessary for the website to function and cannot be switched off in our systems. They ensure core functionality like security, authentication, and basic site operations.",
    required: true,
    gradient: "from-emerald-400 to-teal-500",
    examples: ["Session management", "Security tokens", "Load balancing"],
  },
  {
    id: "performance",
    icon: BarChart,
    title: "Performance Cookies",
    description:
      "These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us understand user behavior and optimize our services.",
    required: false,
    gradient: "from-blue-400 to-indigo-500",
    examples: ["Analytics", "Performance metrics", "Error tracking"],
  },
  {
    id: "functional",
    icon: Settings,
    title: "Functional Cookies",
    description:
      "These cookies enable the website to provide enhanced functionality and personalization. They remember your preferences and settings to improve your experience.",
    required: false,
    gradient: "from-purple-400 to-pink-500",
    examples: ["User preferences", "Language settings", "Theme choices"],
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export default function CookiesPage() {
  const [cookiePreferences, setCookiePreferences] = useState({
    performance: true,
    functional: true,
  });
  const [saved, setSaved] = useState(false);

  useEffect(() => {
    AOS.init({
      easing: "ease-in-out",
      once: true,
      offset: 100,
    });
  }, []);

  const handleToggle = (type: "performance" | "functional") => {
    setCookiePreferences((prev) => ({ ...prev, [type]: !prev[type] }));
    setSaved(false);
  };

  const savePreferences = () => {
    console.log("Saving preferences:", cookiePreferences);
    setSaved(true);
    setTimeout(() => setSaved(false), 2000);
  };

  const acceptAll = () => {
    setCookiePreferences({ performance: true, functional: true });
    savePreferences();
  };

  const rejectAll = () => {
    setCookiePreferences({ performance: false, functional: false });
    savePreferences();
  };

  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="inline-block mb-6">
          <div className="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 backdrop-blur-sm">
            <span className="text-sm font-medium bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Privacy & Cookies
            </span>
          </div>
        </div>

        <h1
          className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6"
          style={{
            background:
              "linear-gradient(135deg, #b851e0 0%, #eb489b 50%, #a855f7 100%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            color: "transparent",
          }}
        >
          Cookie Policy
        </h1>

        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Manage your cookie preferences and understand how we use cookies to
          improve your experience.
        </p>
      </motion.div>

      {/* What are cookies */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="flex items-start gap-6">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-amber-500/20 to-orange-500/20 flex-shrink-0">
              <Cookie className="h-8 w-8 text-amber-400" />
            </div>
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                What are cookies?
              </h2>
              <p className="text-lg text-gray-300 leading-relaxed">
                Cookies are small text files stored on your device that help
                websites remember information about your visit. They can be used
                for various purposes, such as saving your preferences, enabling
                essential site functionality, and gathering analytics to improve
                our services. We are committed to protecting your privacy and
                being transparent about how we use cookies.
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Cookie Types */}
      <motion.div
        className="space-y-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Cookie Types & Settings
          </h2>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Choose which types of cookies you&apos;d like to allow
          </p>
        </div>

        <div className="space-y-6">
          {cookieTypes.map((type, index) => (
            <motion.div
              key={type.id}
              variants={itemVariants}
              className="group relative"
              data-aos="fade-up"
              data-aos-duration="600"
              data-aos-delay={index * 100}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 md:p-8 transition-all duration-500 group-hover:border-white/20 group-hover:bg-white/10">
                <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                  <div className="flex items-start gap-4 flex-1">
                    <div
                      className={`flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-r ${type.gradient} bg-opacity-20 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                    >
                      <type.icon className="h-7 w-7 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl md:text-2xl font-bold text-white mb-2 group-hover:text-gray-100 transition-colors">
                        {type.title}
                      </h3>
                      <p className="text-gray-300 mb-4 leading-relaxed group-hover:text-gray-200 transition-colors">
                        {type.description}
                      </p>

                      {/* Examples */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-400">
                          Examples:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {type.examples.map((example, i) => (
                            <span
                              key={i}
                              className="px-3 py-1 text-xs rounded-full bg-white/10 text-gray-300 border border-white/20"
                            >
                              {example}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between lg:justify-end gap-4 lg:min-w-[200px]">
                    <div className="text-right">
                      <p className="text-sm font-medium text-white mb-1">
                        {type.required
                          ? "Always On"
                          : cookiePreferences[
                                type.id as "performance" | "functional"
                              ]
                            ? "Allowed"
                            : "Blocked"}
                      </p>
                      <p className="text-xs text-gray-400">
                        {type.required
                          ? "Required for site function"
                          : "Optional"}
                      </p>
                    </div>
                    <Switch
                      id={type.id}
                      checked={
                        type.required ||
                        cookiePreferences[
                          type.id as "performance" | "functional"
                        ]
                      }
                      onCheckedChange={() =>
                        handleToggle(type.id as "performance" | "functional")
                      }
                      disabled={type.required}
                      aria-label={`Toggle ${type.title}`}
                      className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        className="space-y-6"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Button
            onClick={savePreferences}
            className={`w-full sm:w-auto px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl ${
              saved
                ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-green-500/25"
                : "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-purple-500/25"
            }`}
          >
            {saved ? (
              <>
                <Check className="mr-2 h-5 w-5" />
                Preferences Saved!
              </>
            ) : (
              "Save Preferences"
            )}
          </Button>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={acceptAll}
              className="border-emerald-500/30 text-emerald-400 hover:bg-emerald-500/10 hover:border-emerald-400 transition-all duration-300"
            >
              <Check className="mr-2 h-4 w-4" />
              Accept All
            </Button>
            <Button
              variant="outline"
              onClick={rejectAll}
              className="border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-400 transition-all duration-300"
            >
              <X className="mr-2 h-4 w-4" />
              Reject All
            </Button>
          </div>
        </div>

        {/* Status Message */}
        {saved && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-500/20 border border-green-500/30 text-green-400">
              <Check className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">
                Your preferences have been saved successfully!
              </span>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Additional Information */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl" />
        <div className="relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Questions About Our Cookies?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              For any questions about our use of cookies or to exercise your
              data rights, please don&apos;t hesitate to contact our privacy
              team.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25"
              >
                Contact Privacy Team
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
              <span className="text-gray-400 font-medium">
                <EMAIL>
              </span>
            </div>

            <div className="mt-8 pt-8 border-t border-white/10">
              <p className="text-sm text-gray-400 leading-relaxed">
                You can change your cookie preferences at any time by returning
                to this page. Note that disabling certain cookies may impact
                your experience on our website.
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}