import { getVerificationEmail } from "src/server/session";
import VerifyMailClient from "./client";
import Link from "next/link";

export default async function VerifyMail() {
  const email = await getVerificationEmail();

  if (!email) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Email Not Found
          </h1>
          <p className="text-white/70 mb-6">
            No email address was provided for verification.
          </p>
          <Link
            href="/login"
            className="text-custom-purple hover:text-custom-pink"
          >
            Back to Login
          </Link>
        </div>
      </div>
    );
  }

  return <VerifyMailClient email={email} />;
}