generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String           @id @default(cuid())
  email              String           @unique
  hashedPassword     String
  name               String
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  package            Package          @default(FREE)
  verified           Boolean          @default(false)
  roleId             Int              @default(1)
  stripeAccountId    String?
  instagram          String?
  twitch             String?
  twitter            String?
  website            String?
  youtube            String?
  tokenVersion       Int              @default(0)
  subscriptionEndsAt DateTime         @default(now())
  hasSeenTutorial    Boolean          @default(false)
  accounts           Account[]
  Image              Image[]
  Ticket             Ticket[]
  TicketActivity     TicketActivity[]
  TicketResponse     TicketResponse[]
  role               Role             @relation(fields: [roleId], references: [id])
  banReason          UserBanReason[]
  UserProviders      UserProviders[]
  UserSettings       UserSettings[]
  videos             Video[]

  @@index([roleId], map: "User_roleId_fkey")
}

model UserSettings {
  id               String   @id @default(cuid())
  userId           String
  twoFactorEnabled Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @default(now())
  secretToken      String   @default("") @db.LongText
  secretTokenIv    String   @default("") @db.LongText
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "UserSettings_userId_fkey")
}

model UserTwoFactor {
  id              String   @id @default(cuid())
  userId          String   @unique
  twofa_secret    String
  twofa_secret_iv String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now())
}

model UserProviders {
  id            String   @id @default(cuid())
  userId        String
  provider      String
  accessToken   String   @db.LongText
  tokenType     String   @db.LongText
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())
  accessTokenIv String   @db.LongText
  providerId    String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "UserProviders_userId_fkey")
}

model UserBanReason {
  id        Int      @id @default(autoincrement())
  userId    String
  reason    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "UserBanReason_userId_fkey")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "Account_userId_fkey")
}

model Video {
  id                   String   @id @default(cuid())
  title                String
  url                  String
  thumbnailUrl         String?
  shortLink            String   @unique
  views                Int      @default(0)
  userId               String
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  commentsDisabled     Boolean  @default(false)
  isPrivate            Boolean  @default(false)
  musicDisabled        Boolean  @default(false)
  showCommunity        Boolean  @default(false)
  markedForDelete      Boolean  @default(false)
  duration             Int      @default(0)
  approvedForCommunity Boolean  @default(false)
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "Video_userId_fkey")
}

model VideoLikes {
  id        String   @id @default(cuid())
  videoId   String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VideoDislikes {
  id        String   @id @default(cuid())
  videoId   String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Subscribe {
  id           String   @id @default(cuid())
  userId       String
  channelId    String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  notification Boolean  @default(true)
}

model Image {
  id        String   @id @default(cuid())
  name      String
  shortLink String   @unique
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "Image_userId_fkey")
}

model EmailVerificationRequest {
  id          Int      @id @default(autoincrement())
  userId      String
  token       String
  requestedAt DateTime @default(now())
  expired     Boolean  @default(false)
}

model PasswordReset {
  id          Int      @id @default(autoincrement())
  userId      String
  token       String
  requestedAt DateTime @default(now())
  expired     Boolean  @default(false)
}

model Role {
  id          Int              @id @default(autoincrement())
  name        String           @unique
  level       Int
  permissions RolePermission[]
  users       User[]
}

model Permission {
  id    Int              @id @default(autoincrement())
  name  String           @unique
  roles RolePermission[]
}

model RolePermission {
  roleId       Int
  permissionId Int
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@index([permissionId], map: "RolePermission_permissionId_fkey")
}

model Reports {
  id               String       @id @default(cuid())
  userId           String
  reportedUserId   String
  reportReason     String
  details          String?
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  reportObjectId   String
  reportObjectType String
  reportCase       ReportCase[]
}

model ReportCase {
  id          String       @id @default(cuid())
  adminUserId String?
  reportId    String
  status      ReportStatus
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  report      Reports      @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@index([reportId], map: "ReportCase_reportId_fkey")
}

model Ticket {
  id         String           @id @default(cuid())
  title      String
  content    String           @db.Text
  status     TicketStatus     @default(OPEN)
  priority   TicketPriority   @default(NORMAL)
  userId     String
  assignedTo String?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  category   Ticket_category  @default(GENERAL)
  user       User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  activities TicketActivity[]
  responses  TicketResponse[]

  @@index([userId], map: "Ticket_userId_fkey")
}

model TicketResponse {
  id        String   @id @default(cuid())
  content   String   @db.Text
  ticketId  String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ticket    Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([ticketId], map: "TicketResponse_ticketId_fkey")
  @@index([userId], map: "TicketResponse_userId_fkey")
}

model TicketActivity {
  id        String   @id @default(cuid())
  ticketId  String
  userId    String
  action    String
  createdAt DateTime @default(now())
  ticket    Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([ticketId], map: "TicketActivity_ticketId_fkey")
  @@index([userId], map: "TicketActivity_userId_fkey")
}

model SystemSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Comments {
  id        String   @id @default(cuid())
  videoId   String
  userId    String
  comment   String
  parentId  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CommentLikes {
  id        String   @id @default(cuid())
  commentId String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notifications {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  data      String
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

model BanReasons {
  id        Int      @id @default(autoincrement())
  reason    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model LoginHistory {
  id        String   @id @default(cuid())
  userId    String
  email     String
  ip        String
  location  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  success   Boolean
}

model Maintenance {
  id        String   @id @default(cuid())
  path      String   @unique
  enabled   Boolean
  updatedAt DateTime @updatedAt
}

model MaintenanceUpdates {
  id        String           @id @default(cuid())
  title     String
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  type      MaintenanceTypes
}

model CommentReports {
  id                 String       @id @default(cuid())
  commentId          String
  userId             String
  reportedVideoId    String
  reportedUserId     String
  reason             String
  details            String?
  status             ReportStatus
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  commentText        String
  reportedUserEmail  String
  reportedUserName   String
  reportedVideoTitle String
  reportedVideoUrl   String
}

model Logs {
  id          String     @id @default(cuid())
  user        String
  info        String
  action      LogActions
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  furtherInfo String
}

enum MaintenanceTypes {
  INFO
  SUCCESS
  ERROR
}

enum ReportStatus {
  OPEN
  PENDING
  CLOSED
}

enum Package {
  FREE
  PRO
  CREATOR
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  CLOSED
  RESOLVED
}

enum TicketPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationType {
  COMMENT
  LIKE
  FOLLOW
  REPORT
  INFO
  WARNING
  ERROR
  SUBSCRIBE
}

enum LogActions {
  ACCOUNT
  VIDEO
  IMAGE
  TICKET
  REPORT
  COUPON
}

enum Ticket_category {
  GENERAL
  TECHNICAL
  BILLING
  SUGGESTION
  OTHER
  BUG
}
