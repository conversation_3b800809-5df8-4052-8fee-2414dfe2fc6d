"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send } from "lucide-react"
import { useEnhancedToast } from "@/components/ui/enhanced-toast"
import { useRouter } from "next/navigation"
import submitTicketResponse from "../_actions/add-ticket-response"
import { useSocket } from "@/components/socket-context"

export function TicketResponseForm({ userId, ticketId }: { userId: string, ticketId: string }) {
  const { success, error } = useEnhancedToast();
  const [content, setContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { sendEvent } = useSocket();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) {
      error("Response Required", "Please enter a response");
      return
    }

    setIsSubmitting(true)
    try {
      const result = await submitTicketResponse({
        ticketId,
        content,
      });

      if (result.success) {
        setContent("")
        success("Response Sent", "Your response has been added to the ticket");

        sendEvent(JSON.stringify({
          type: 'ticket:chat',
          ticketId: ticketId,
          messageData: {
            sender: userId,
            message: content,
            messageId: result.messageId
          }
        }));
      } else {
        throw new Error(result.message)
      }
    } catch (err) {
      error("Send Failed", "Failed to send your response. Please try again.");
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <Textarea
        placeholder="Type your response..."
        value={content}
        onChange={(e) => setContent(e.target.value)}
        className="min-h-[120px] resize-none bg-[#1A1A1A] border-[#333] focus-visible:ring-purple-500"
        disabled={isSubmitting}
        onKeyDown={(e) => handleKeyDown(e as React.KeyboardEvent<HTMLTextAreaElement>)}
      />
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
        >
          <Send className="mr-2 h-4 w-4" />
          {isSubmitting ? "Sending..." : "Send Response"}
        </Button>
      </div>
    </form>
  )
}