"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { TicketCard } from "./ticket-card"

interface TicketsClientProps {
  openTickets: any[]
  inProgressTickets: any[]
  resolvedTickets: any[]
  closedTickets: any[]
}

export function TicketsClient({ openTickets, inProgressTickets, resolvedTickets, closedTickets }: TicketsClientProps) {
  return (
    <Tabs defaultValue="open">
      <TabsList className="mb-4">
        <TabsTrigger value="open">Open ({openTickets.length})</TabsTrigger>
        <TabsTrigger value="in-progress">In Progress ({inProgressTickets.length})</TabsTrigger>
        <TabsTrigger value="resolved">Resolved ({resolvedTickets.length})</TabsTrigger>
        <TabsTrigger value="closed">Closed ({closedTickets.length})</TabsTrigger>
      </TabsList>
      <TabsContent value="open" className="space-y-4">
        {openTickets.length === 0 ? (
          <div className="text-center py-12 border rounded-lg bg-card">
            <p className="text-muted-foreground">No open tickets</p>
          </div>
        ) : (
          openTickets.map((ticket) => <TicketCard key={ticket.id} ticket={ticket} isAdmin />)
        )}
      </TabsContent>
      <TabsContent value="in-progress" className="space-y-4">
        {inProgressTickets.length === 0 ? (
          <div className="text-center py-12 border rounded-lg bg-card">
            <p className="text-muted-foreground">No tickets in progress</p>
          </div>
        ) : (
          inProgressTickets.map((ticket) => <TicketCard key={ticket.id} ticket={ticket} isAdmin />)
        )}
      </TabsContent>
      <TabsContent value="resolved" className="space-y-4">
        {resolvedTickets.length === 0 ? (
          <div className="text-center py-12 border rounded-lg bg-card">
            <p className="text-muted-foreground">No resolved tickets</p>
          </div>
        ) : (
          resolvedTickets.map((ticket) => <TicketCard key={ticket.id} ticket={ticket} isAdmin />)
        )}
      </TabsContent>
      <TabsContent value="closed" className="space-y-4">
        {closedTickets.length === 0 ? (
          <div className="text-center py-12 border rounded-lg bg-card">
            <p className="text-muted-foreground">No closed tickets</p>
          </div>
        ) : (
          closedTickets.map((ticket) => <TicketCard key={ticket.id} ticket={ticket} isAdmin />)
        )}
      </TabsContent>
    </Tabs>
  )
}