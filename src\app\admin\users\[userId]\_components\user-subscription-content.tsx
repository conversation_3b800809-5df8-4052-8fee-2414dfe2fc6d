"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import <PERSON><PERSON> from "stripe";
import { User } from "@/lib/db/user";

type Props = {
  user: User;
  subscriptionData: Stripe.Subscription | null;
  invoiceHistory: Stripe.Invoice[];
  planName: string;
};

export default function UserSubscriptionContent({
  user,
  subscriptionData,
  invoiceHistory = [],
  planName,
}: Props) {
  if (!subscriptionData) {
    return (
      <div className="flex justify-center">
        <div className="max-w-md w-full bg-gray-950/50 border border-gray-800/40 rounded-xl p-6 text-center">
          <h3 className="text-lg font-semibold text-white mb-2">
            No Active Subscription
          </h3>
          <p className="text-gray-400">
            The user currently doesn&apos;t have any running subscriptions.
          </p>
        </div>
      </div>
    );
  }

  const currentPeriodEnd = subscriptionData.current_period_end
    ? format(new Date(subscriptionData.current_period_end * 1000), "PPP")
    : "N/A";
  const isActive = subscriptionData.status === "active";

  return (
    <div className="space-y-6">
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Subscription Details
        </h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Status:</span>
            <span
              className={
                isActive
                  ? "text-green-400 font-medium"
                  : "text-red-400 font-medium"
              }
            >
              {isActive ? "Active" : "Inactive"}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Plan:</span>
            <span className="text-white font-medium">{planName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Current Period Ends:</span>
            <span className="text-white font-medium">{currentPeriodEnd}</span>
          </div>
        </div>
      </div>

      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Purchase History
        </h3>
        <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-800/40 hover:bg-gray-800/20">
                <TableHead className="text-gray-300 font-semibold">
                  Date
                </TableHead>
                <TableHead className="text-gray-300 font-semibold">
                  Amount
                </TableHead>
                <TableHead className="text-gray-300 font-semibold">
                  Status
                </TableHead>
                <TableHead className="text-gray-300 font-semibold">
                  Invoice
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoiceHistory.length === 0 ? (
                <TableRow className="border-gray-800/40">
                  <TableCell
                    colSpan={4}
                    className="text-center text-gray-400 py-8"
                  >
                    No invoices found.
                  </TableCell>
                </TableRow>
              ) : (
                invoiceHistory.map((invoice) => (
                  <TableRow
                    key={invoice.id}
                    className="border-gray-800/40 hover:bg-gray-800/20"
                  >
                    <TableCell className="text-gray-300">
                      {format(new Date(invoice.created * 1000), "PPP")}
                    </TableCell>
                    <TableCell className="text-white font-medium">
                      ${(invoice.amount_paid / 100).toFixed(2)}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          invoice.status === "paid"
                            ? "bg-green-500/20 text-green-400"
                            : "bg-red-500/20 text-red-400"
                        }`}
                      >
                        {invoice.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      <a
                        href={invoice.hosted_invoice_url ?? "#"}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300 hover:underline transition-colors"
                      >
                        View
                      </a>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}