"use server";

import {rateLimiter} from "@/lib/rate-limit";
import {getClientIp} from "@/server/geolocation";
import {approveVideoForCommunity, getVideoDataById} from "@/server/video";
import {getUserSession} from "@/server/session";
import {getUserById} from "@/lib/db/user";
import {hasPermission} from "@/server/admin";
import {createNotification} from "@/server/notifications";
import {LogActions, NotificationType} from "@prisma/client";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";

type Callback = {
    success: boolean;
    message: string;
    data?: {
        id: string
    }
}

type Props = {
    videoId: string;
};

export default async function submitApproveVideo({videoId}: Props): Promise<Callback> {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
        return {
            success: false,
            message: "You have exceeded the rate limit. Please try again later.",
        }
    }

    try {
        const video = await getVideoDataById(videoId);
        if (!video) {
            return {
                success: false,
                message: "Video not found.",
            }
        }

        const owner = await getUserById(video.userId);
        if (!owner) {
            return{
                success: false,
                message: "Video owner not found.",
            }
        }

        const adminUserSession = await getUserSession();
        if (!adminUserSession) {
            return {
                success: false,
                message: "Not authenticated",
            };
        }

        const adminUserId = adminUserSession.userId;
        const adminUser = await getUserById(adminUserId);
        if (!adminUser) {
            return {
                success: false,
                message: "Admin user not found. Please log in again.",
            };
        }

        if (!await hasPermission(adminUser.roleId, ["ADMIN_COMMUNITY_APPROVE"])){
            return {
                success: false,
                message: "You do not have permission to approve videos.",
            }
        }

        if (video.showCommunity && video.approvedForCommunity) {
            return {
                success: false,
                message: "Video is already approved for community.",
            }
        }

        await approveVideoForCommunity(video.id);
        await createNotification(owner.id, "Your video has been accepted for the community.", NotificationType.INFO);
        await createLog(adminUser.id, LogConstants.ADMIN_VIDEO_COMMUNITY_ACCEPT, LogActions.VIDEO, "Accepted video: " + video.id);

        return {
            success: true,
            message: "Video approved successfully.",
            data: {
                id: video.id
            }
        }
    } catch (error) {
        console.error(error);
        return {
            success: false,
            message: "An unexpected error occurred. Please try again.",
        }
    }
}
