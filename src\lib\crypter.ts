import { <PERSON><PERSON><PERSON> } from "buffer"
import crypto from "crypto"

const ALGORITHM = "aes-256-cbc"

const ENCRYPTION_KEY = Buffer.from("RT9aMHwwTjZ6NFNgYStHoy80SGhoW1xNdTNkX2FINHE=", "base64")

if (ENCRYPTION_KEY.length !== 32) {
  throw new Error("ENCRYPTION_KEY must be 32 bytes (256 bits) long")
}

export function encryptData(data: string) {
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv)

  const encryptedBuffer = Buffer.from(cipher.update(data, "utf8", "base64") + cipher.final("base64"), "base64")

  return {
    encryptedData: encryptedBuffer.toString("base64"),
    iv: iv.toString("base64"),
  }
}

export function decryptData(encryptedData: string, iv: string) {
  const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, Buffer.from(iv, "base64"))

  const decrypted = decipher.update(encryptedData, "base64", "utf8") + decipher.final("utf8")

  return decrypted
}

export interface EncryptedResult {
  encryptedData: string
  iv: string
}