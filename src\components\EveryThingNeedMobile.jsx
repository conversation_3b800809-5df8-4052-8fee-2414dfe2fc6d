import React, { useLayoutEffect } from "react";
import { FEATURES_LIST } from "../../utils/helper";
import Icons from "./common/Icons";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

const EveryThingNeedMobile = () => {
  useLayoutEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    const ctx = gsap.context(() => {
      gsap.matchMedia().add("(max-width: 639px)", () => {
        const cards = gsap.utils.toArray(".card-item");
        const isBelowSm = window.innerHeight < 600;

        cards.forEach((card, index) => {
          gsap.set(card, {
            y: index === 0 ? 0 : 700,
            zIndex: index,
            scale: index === 0 ? 1 : 0.9,
          });
        });

        const tl = gsap.timeline({
          scrollTrigger: {
            trigger: ".cards_parent",
            start: "top top",
            end: `+=${cards.length * 100}%`,
            pin: true,
            scrub: 1,
          },
        });
        const scaleStep = 0.05;
        cards.forEach((card, index) => {
          if (index > 0) {
            tl.to(
              cards[index - 1],
              {
                scale: 0.9,
                duration: 0.3,
                ease: "power2.out",
              },
              "+=0.1"
            );

            tl.to(
              card,
              {
                y: isBelowSm ? 0 : 100 + index * -8,
                duration: 0.3,
                ease: "power2.out",
              },
              "<"
            );
          }
        });
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      ctx.revert();
    };
  }, []);

  return (
    <div className="relative w-full h-[70vh] overflow-hidden cards_parent">
      <div className="cards_scroll">
        {FEATURES_LIST.map((feature, idx) => (
          <div
            key={idx}
            id={`card-${idx + 1}`}
            className={`card-${idx + 1} ${
              idx % 2 === 0 ? "bg-[#1F0C1D]" : "bg-[#110018]"
            } 
                        ${
                          idx === 0
                            ? "relative mt-[100px] mt-0-h-sm"
                            : "absolute top-0 left-0 w-full"
                        } 
                        p-6 rounded-2xl shadow-lg min-h-[295px] flex justify-between flex-col 
                        border border-[#3e2e3d] card-item`}
          >
            <div>
              <div className="min-w-[60px] h-[60px] inline-grid justify-center items-center upload-box rounded-lg relative group-hover:shadow-[0px_0px_17.23px_0px_#B851E066] duration-300 bg-[linear-gradient(321.29deg,_#FFFFFF33_22.72%,_#FFFFFF00_74.04%)]">
                <Icons icon={feature.icon} />
              </div>
              <h3 className="font-semibold mb-2 text-xl md:text-[32px] leading-130 pt-4 text-white">
                {feature.title}
              </h3>
            </div>
            <p className="text-white opacity-80 text-base">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EveryThingNeedMobile;
