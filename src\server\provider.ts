"use server";

import { getUserById } from "@/lib/db/user";
import {prisma} from "@/lib/prisma";

export const updateProvider = async (userId: string, provider: string, accessToken: string, acecssTokenIv: string, tokenType: string) => {
  const data = await prisma.userProviders.findFirst({
    where: {userId: userId}
  });
  if (data == null) return;

  await prisma.userProviders.update({
    where: {
      id: data.id
    },
    data: {
      accessToken: accessToken,
      accessTokenIv: acecssTokenIv,
      tokenType: tokenType,
      provider: provider
    }
  });
};

export const isProviderLinked = async (provider: string, providerId: string) => {
  const data = await prisma.userProviders.findFirst({
    where: {provider: provider, providerId: providerId}
  });

  return !!data;
}

export const linkProvider = async (userId: string, provider: string, providerId: string, accessToken: string, accessTokenIv: string, tokenType: string) => {
  const user = await getUserById(userId);
  if (!user) return;

  await prisma.userProviders.create({
    data: {
      provider: provider,
      providerId: providerId,
      userId: user.id,
      accessToken: accessToken,
      accessTokenIv: accessTokenIv,
      tokenType: tokenType
    }
  })
};
