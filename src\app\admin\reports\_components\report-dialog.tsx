"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Flag,
  Shield,
  Trash2,
  UserX,
  AlertTriangle,
  Clock,
  Check,
  X,
  ExternalLink,
  Video,
  Image as ImageIcon,
} from "lucide-react";
import { updateReportStatus } from "../_actions/update-status";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useRouter } from "next/navigation";
import submitRemoveVideo from "../_actions/remove-video";
import submitBanUser from "../_actions/ban-user";
import { format } from "date-fns";
import { useState } from "react";

interface ReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  report: {
    id: string;
    reportedUser: string;
    reportedUserId: string;
    reportReason: string;
    createdAt: string;
    status: string;
    moderator: string;
    shortLink: string;
    videoId: string;
    details: string | null;
    contentType: "video" | "image";
  };
}

export function ReportDialog({ isOpen, onClose, report }: ReportDialogProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleRemoveContent = async () => {
    setIsProcessing(true);
    try {
      const response = await submitRemoveVideo({
        videoId: report.videoId,
        removalReason: report.reportReason,
      });

      if (response.success) {
        success("Content Removed", response.message);
      } else {
        error("Remove Failed", response.message);
      }

      if (response.success) {
        onClose();
        router.refresh();
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBanUser = async () => {
    setIsProcessing(true);
    try {
      const response = await submitBanUser({
        userId: report.reportedUserId,
        reason: report.reportReason,
      });

      if (response.success) {
        success("User Banned", response.message);
      } else {
        error("Ban Failed", response.message);
      }

      if (response.success) {
        onClose();
        router.refresh();
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUpdateStatus = async (status: string) => {
    setIsProcessing(true);
    try {
      const response = await updateReportStatus({
        reportId: report.id,
        status,
      });

      if (response.success) {
        success("Status Updated", response.message);
      } else {
        error("Update Failed", response.message);
      }

      if (response.success) {
        onClose();
        router.refresh();
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const getReasonLabel = (reason: string) => {
    switch (reason) {
      case "inappropriate":
        return "Inappropriate content";
      case "copyright":
        return "Copyright violation";
      case "spam":
        return "Spam or misleading";
      case "other":
        return "Other";
      default:
        return reason;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "OPEN":
        return (
          <Badge className="bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse mr-2" />
            Open
          </Badge>
        );
      case "PENDING":
        return (
          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 hover:bg-yellow-500/30">
            <Clock className="w-3 h-3 mr-2" />
            Pending
          </Badge>
        );
      case "CLOSED":
        return (
          <Badge className="bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30">
            <Check className="w-3 h-3 mr-2" />
            Closed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 border border-gray-700/50 text-white shadow-2xl max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-red-500/20 to-orange-500/20 border border-red-500/30">
              <Shield className="h-4 w-4 text-red-400" />
            </div>
            <div>
              <DialogTitle className="text-lg font-bold text-white">
                Report Review
              </DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-6">
          {/* Content Info */}
          <div className="bg-gray-800/40 border border-gray-700/40 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={`flex h-6 w-6 items-center justify-center rounded ${
                    report.contentType === "video"
                      ? "bg-blue-500/20 text-blue-400"
                      : "bg-pink-500/20 text-pink-400"
                  }`}
                >
                  {report.contentType === "video" ? (
                    <Video className="h-3 w-3" />
                  ) : (
                    <ImageIcon className="h-3 w-3" />
                  )}
                </div>
                <div>
                  <span className="text-sm font-medium text-white capitalize">
                    {report.contentType}
                  </span>
                  <p className="text-xs text-gray-400">{report.shortLink}</p>
                </div>
              </div>
              {report.shortLink !== "DELETED" ? (
                <a
                  href={`/${report.contentType === "video" ? "v" : "i"}/${report.shortLink}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
                >
                  View
                  <ExternalLink className="h-3 w-3" />
                </a>
              ) : (
                <span className="text-xs text-red-400">Deleted</span>
              )}
            </div>
          </div>

          {/* Report Information */}
          <div className="space-y-4">
            {/* Report Details */}
            <div className="bg-gray-800/40 border border-gray-700/40 rounded-lg p-3">
              <h3 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                <Flag className="h-4 w-4 text-red-400" />
                Report Details
              </h3>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">Reason:</span>
                  <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/30 text-xs">
                    {getReasonLabel(report.reportReason)}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">Status:</span>
                  <div>{getStatusBadge(report.status)}</div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">User:</span>
                  <span className="text-white text-sm font-medium">
                    {report.reportedUser}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-400">Date:</span>
                  <span className="text-white text-sm">
                    {format(new Date(report.createdAt), "MMM dd, yyyy")}
                  </span>
                </div>
              </div>
            </div>

            {/* Additional Details */}
            {report.details && report.details.length > 0 && (
              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-xs font-medium text-yellow-300 mb-1">
                      Additional Details
                    </h4>
                    <div className="text-xs text-yellow-100 bg-yellow-500/10 rounded p-2 whitespace-pre-wrap">
                      {report.details}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-2">
              {report.status === "OPEN" && (
                <Button
                  size="sm"
                  className="w-full bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
                  onClick={() => handleUpdateStatus("PENDING")}
                  disabled={isProcessing}
                >
                  <Check className="w-3 h-3 mr-1" />
                  Accept Report
                </Button>
              )}

              {report.status === "PENDING" && (
                <Button
                  size="sm"
                  className="w-full bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30"
                  onClick={() => handleUpdateStatus("CLOSED")}
                  disabled={isProcessing}
                >
                  <X className="w-3 h-3 mr-1" />
                  Close Report
                </Button>
              )}

              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  className="bg-red-500/20 border-red-500/30 hover:bg-red-500/30"
                  onClick={handleRemoveContent}
                  disabled={isProcessing || report.shortLink === "DELETED"}
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Remove
                </Button>

                <Button
                  variant="destructive"
                  size="sm"
                  className="bg-red-600/20 border-red-600/30 hover:bg-red-600/30"
                  onClick={handleBanUser}
                  disabled={isProcessing}
                >
                  <UserX className="w-3 h-3 mr-1" />
                  Ban User
                </Button>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="w-full bg-gray-700/30 border-gray-600 text-gray-300 hover:bg-gray-600"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}