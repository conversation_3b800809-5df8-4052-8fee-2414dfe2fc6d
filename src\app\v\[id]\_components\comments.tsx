"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDistanceToNow } from "date-fns"
import { ThumbsUp, MessageSquare, Flag, MoreVertical, TrashIcon } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { submitVideoComment } from "../_actions/send-comment"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import type { Comment } from "@/server/comment"
import { submitLikeComment } from "../_actions/like-comment"
import submitRemoveComment from "../_actions/remove-comment"
import { ReportCommentDialog } from "./report-comment-dialog"
import Link from "next/link"

interface CommentsProps {
  videoId: string
  commentsDisabled: boolean
  currentUser?: {
    id: string
    name: string
    email: string
    image?: string
  } | null
  comments: Comment[]
  isAdmin: boolean
  getUserProfileUrl?: (userId: string) => string
  className?: string
}

const API_URL = "https://api.streambliss.cloud"

export function Comments({
  videoId,
  commentsDisabled,
  currentUser,
  comments,
  isAdmin,
  getUserProfileUrl = (username: string) => `/community/u/${username}`,
  className = "",
}: CommentsProps) {
  const router = useRouter()
  const [comment, setComment] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState("")
  const [expandedReplies, setExpandedReplies] = useState<Record<string, boolean>>({
    "2": true,
  })
  const [reportingCommentId, setReportingCommentId] = useState<string | null>(null)

  const handleSubmitComment = async () => {
    if (!comment.trim()) return
    if (currentUser == null) {
      toast({
        variant: "destructive",
        description: "You must log in to comment",
      })
      return
    }

    const response = await submitVideoComment({ senderId: currentUser.id, videoId, text: comment })
    toast({
      variant: response.success ? "default" : "destructive",
      description: response.message,
    })

    if (response.success) {
      router.refresh()
      setComment("")
    }
  }

  const handleSubmitReply = async (commentId: string) => {
    if (!replyContent.trim()) return
    if (currentUser == null) {
      toast({
        variant: "destructive",
        description: "You must log in to comment",
      })
      return
    }

    const response = await submitVideoComment({
      senderId: currentUser.id,
      videoId,
      text: replyContent,
      commentId: commentId,
    })
    toast({
      variant: response.success ? "default" : "destructive",
      description: response.message,
    })

    if (response.success) {
      router.refresh()
    }

    setReplyingTo(null)
    setReplyContent("")
    setExpandedReplies({
      ...expandedReplies,
      [commentId]: true,
    })
  }

  const toggleLike = async (commentId: string) => {
    if (!currentUser) {
      toast({
        variant: "destructive",
        description: "You must log in to like comments",
      })
      return
    }

    const response = await submitLikeComment({ commentId, userId: currentUser.id })
    if (response.success) {
      router.refresh()
    }

    if (!response.success) {
      toast({
        variant: "destructive",
        description: response.message,
      })
    }
  }

  const toggleReplies = (commentId: string) => {
    setExpandedReplies({
      ...expandedReplies,
      [commentId]: !expandedReplies[commentId],
    })
  }

  const handleRemoveComment = async (commentId: string) => {
    const resposne = await submitRemoveComment({ commentId })
    toast({
      variant: resposne.success ? "default" : "destructive",
      description: resposne.message,
    })

    if (resposne.success) {
      router.refresh()
    }
  }

  const openReportDialog = (commentId: string) => {
    if (!currentUser) {
      toast({
        variant: "destructive",
        description: "You must log in to report comments",
      })
      return
    }
    setReportingCommentId(commentId)
  }

  if (commentsDisabled) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 text-white/30 mx-auto mb-3" />
          <p className="text-white/60 text-sm">Comments have been disabled for this video</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {currentUser && (
        <div className="p-6 rounded-2xl bg-gradient-to-r from-white/5 to-white/[0.02] border border-white/10 backdrop-blur-sm">
          <div className="flex gap-4">
            <Link href={getUserProfileUrl(currentUser.name)}>
              <Avatar className="h-12 w-12 ring-2 ring-purple-500/30 cursor-pointer hover:ring-purple-400 transition-all duration-300 hover:scale-105">
                {currentUser.image && (
                  <AvatarImage src={currentUser.image || "/placeholder.svg"} alt={currentUser.name} />
                )}
                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white font-bold">
                  {currentUser.name?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </Link>
            <div className="flex-1 space-y-3">
              <Textarea
                placeholder="Add a comment..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="min-h-[80px] resize-none bg-white/5 border-white/10 text-white placeholder:text-white/50 focus:border-purple-500/50 focus:ring-purple-500/20"
              />
              <div className="flex justify-end">
                <Button 
                  onClick={handleSubmitComment}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium px-6"
                >
                  Comment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {comments.length === 0 ? (
        <div className="text-center py-8">
          <MessageSquare className="h-12 w-12 text-white/30 mx-auto mb-3" />
          <p className="text-white/60 text-sm">Be the first to comment!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {comments.map((comment) => (
            <div key={comment.id} className="p-6 rounded-2xl bg-gradient-to-r from-white/5 to-white/[0.02] border border-white/10 backdrop-blur-sm">
              <div className="flex gap-4">
                <Link href={getUserProfileUrl(comment.user.name)}>
                  <Avatar className="h-10 w-10 ring-2 ring-purple-500/30 cursor-pointer hover:ring-purple-400 transition-all duration-300 hover:scale-105">
                    {comment.user.id && (
                      <AvatarImage
                        src={API_URL + "/profile/" + comment.user.id || "/placeholder.svg"}
                        alt={comment.user.name}
                      />
                    )}
                    <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white font-bold">
                      {comment.user.name[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Link
                        href={getUserProfileUrl(comment.user.name)}
                        className="font-semibold text-white hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-purple-400 hover:to-pink-400 transition-all duration-300"
                      >
                        {comment.user.name}
                      </Link>
                      <span className="text-xs text-white/50 bg-white/5 px-2 py-1 rounded-full">
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                      </span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-white/60 hover:text-white hover:bg-white/10">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">More options</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-gray-900 border-gray-800">
                        <DropdownMenuItem onClick={() => openReportDialog(comment.id)} className="text-white hover:bg-gray-800">
                          <Flag className="mr-2 h-4 w-4" /> Report
                        </DropdownMenuItem>
                        {isAdmin && (
                          <DropdownMenuItem className="text-red-400 hover:bg-red-900/20" onClick={() => handleRemoveComment(comment.id)}>
                            <TrashIcon className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <p className="text-sm text-white/90 leading-relaxed">{comment.text}</p>
                  <div className="flex items-center gap-4 pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-white/60 hover:text-white hover:bg-white/10 transition-all duration-300"
                      onClick={() => toggleLike(comment.id)}
                    >
                      <ThumbsUp className={`mr-2 h-4 w-4 ${comment.isLiked ? "fill-purple-400 text-purple-400" : ""}`} />
                      <span className="text-xs">{comment.likes > 0 ? comment.likes : "Like"}</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-white/60 hover:text-white hover:bg-white/10 transition-all duration-300"
                      onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      <span className="text-xs">Reply</span>
                    </Button>
                  </div>
                </div>
              </div>

              {/* Reply form */}
              {replyingTo === comment.id && (
                <div className="mt-4 ml-14 p-4 rounded-xl bg-white/5 border border-white/10">
                  <div className="flex gap-3">
                    <Link href={currentUser ? getUserProfileUrl(currentUser.name) : "#"}>
                      <Avatar className="h-8 w-8 ring-1 ring-purple-500/30 cursor-pointer hover:ring-purple-400 transition-all">
                        {currentUser?.image && (
                          <AvatarImage src={currentUser.image || "/placeholder.svg"} alt={currentUser.name || ""} />
                        )}
                        <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white text-sm font-bold">
                          {currentUser?.name?.[0]?.toUpperCase() || "A"}
                        </AvatarFallback>
                      </Avatar>
                    </Link>
                    <div className="flex-1 space-y-2">
                      <Textarea
                        placeholder={`Replying to ${comment.user.name}...`}
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        className="min-h-[60px] resize-none text-sm bg-white/5 border-white/10 text-white placeholder:text-white/50 focus:border-purple-500/50"
                      />
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" onClick={() => setReplyingTo(null)} className="border-white/20 text-white/80 hover:bg-white/10">
                          Cancel
                        </Button>
                        <Button size="sm" onClick={() => handleSubmitReply(comment.id)} className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                          Reply
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Replies */}
              {comment.children && comment.children.length > 0 && (
                <div className="mt-4 ml-14 space-y-4">
                  <Button
                    variant="link"
                    size="sm"
                    className="h-8 px-0 text-xs text-purple-400 hover:text-purple-300"
                    onClick={() => toggleReplies(comment.id)}
                  >
                    {expandedReplies[comment.id]
                      ? `Hide ${comment.children.length} ${comment.children.length === 1 ? "reply" : "replies"}`
                      : `View ${comment.children.length} ${comment.children.length === 1 ? "reply" : "replies"}`}
                  </Button>

                  {expandedReplies[comment.id] &&
                    comment.children.map((reply) => (
                      <div key={reply.id} className="p-4 rounded-xl bg-white/5 border border-white/10 mt-2">
                        <div className="flex gap-3">
                          <Link href={getUserProfileUrl(reply.user.name)}>
                            <Avatar className="h-8 w-8 ring-1 ring-purple-500/30 cursor-pointer hover:ring-purple-400 transition-all">
                              {reply.user.id && (
                                <AvatarImage
                                  src={API_URL + "/profile/" + reply.user.id || "/placeholder.svg"}
                                  alt={reply.user.name}
                                />
                              )}
                              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white text-sm font-bold">
                                {reply.user.name[0].toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                          </Link>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center gap-2">
                              <Link
                                href={getUserProfileUrl(reply.user.name)}
                                className="font-semibold text-white hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-purple-400 hover:to-pink-400 transition-all duration-300 text-sm"
                              >
                                {reply.user.name}
                              </Link>
                              <span className="text-xs text-white/50 bg-white/5 px-2 py-1 rounded-full">
                                {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                            <p className="text-sm text-white/90 leading-relaxed">{reply.text}</p>
                            <div className="flex items-center gap-4 pt-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 px-3 text-white/60 hover:text-white hover:bg-white/10 transition-all duration-300"
                                onClick={() => toggleLike(reply.id)}
                              >
                                <ThumbsUp className={`mr-2 h-4 w-4 ${reply.isLiked ? "fill-purple-400 text-purple-400" : ""}`} />
                                <span className="text-xs">{reply.likes > 0 ? reply.likes : "Like"}</span>
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8 text-white/60 hover:text-white hover:bg-white/10">
                                    <MoreVertical className="h-4 w-4" />
                                    <span className="sr-only">More options</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="bg-gray-900 border-gray-800">
                                  <DropdownMenuItem onClick={() => openReportDialog(reply.id)} className="text-white hover:bg-gray-800">
                                    <Flag className="mr-2 h-4 w-4" /> Report
                                  </DropdownMenuItem>
                                  {isAdmin && (
                                    <DropdownMenuItem className="text-red-400 hover:bg-red-900/20" onClick={() => handleRemoveComment(reply.id)}>
                                      <TrashIcon className="mr-2 h-4 w-4" /> Delete
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      <ReportCommentDialog
        open={!!reportingCommentId}
        onOpenChange={() => setReportingCommentId(null)}
        commentId={reportingCommentId ?? ""}
      />
    </div>
  );
}