/* Base Styles - Fonts, Global Utilities, and Core Styles */

/* Font Face Declarations */
@font-face {
  font-family: Montserrat;
  src: url(/assets/fonts/Montserrat-Thin.ttf);
  font-weight: 300;
}

@font-face {
  font-family: Montserrat;
  src: url(/assets/fonts/Montserrat-Regular.ttf);
  font-weight: 400;
}

@font-face {
  font-family: Montserrat;
  src: url(/assets/fonts/Montserrat-Medium.ttf);
  font-weight: 500;
}

@font-face {
  font-family: Montserrat;
  src: url(/assets/fonts/Montserrat-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: Montserrat;
  src: url(/assets/fonts/Montserrat-Bold.ttf);
  font-weight: 700;
}

/* Global cursor pointer for interactive elements */
button,
[role="button"],
[type="button"],
[type="submit"],
[type="reset"],
.cursor-pointer,
a[href],
select,
input[type="checkbox"],
input[type="radio"],
label[for],
[tabindex]:not([tabindex="-1"]),
.clickable {
  cursor: pointer !important;
}

/* Ensure disabled elements don't have pointer cursor */
button:disabled,
[role="button"]:disabled,
[type="button"]:disabled,
[type="submit"]:disabled,
[type="reset"]:disabled,
[aria-disabled="true"] {
  cursor: not-allowed !important;
}

/* Custom spacing for chat messages */
.space-y-15 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 3.75rem; /* 60px */
}

.mt-15 {
  margin-top: 3.75rem; /* 60px */
}

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 4px;
  height: 0.5px;
}

::-webkit-scrollbar-track {
  width: 4px;
  background: #140016;
  border-radius: 8px;
  height: 0.5px;
}

::-webkit-scrollbar-thumb {
  width: 4px;
  background: linear-gradient(#b851e0, #eb489b);
  border-radius: 8px;
  height: 0.5px;
}

/* Dashboard specific utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced font smoothing */
button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Enhanced link hover effects */
a:not(.no-hover-effect) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a:not(.no-hover-effect):hover {
  transform: translateY(-1px);
}
