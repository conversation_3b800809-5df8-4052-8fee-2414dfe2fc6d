"use client";

import {useState, useEffect, useRef} from "react";
import Image from "next/image";
import {shouldDisableOptimization} from "@/lib/image-utils";
import {
    MoreVertical,
    Heart,
    Music,
    Globe,
    Users,
    Eye,
    Lock,
    Settings,
    Link2,
    BarChart3,
    Download,
    Trash2,
    CheckCircle,
    UploadCloud,
    Copy,
    ChevronLeft,
    ChevronRight,
} from "lucide-react";
import {motion, AnimatePresence} from "framer-motion";
import type {ProcessingVideo} from "../../../types/video";
import {useSocket} from "@/components/socket-context";

import {UnifiedUploader} from "./unified-uploader";
import {VideoModal} from "./video-modal";
import {ImageModal} from "./image-modal";
import {VideoSettingsDialog} from "./video-settings-dialog";
import {CommunityPromptModal} from "./community-prompt-modal";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import {
    Dropdown<PERSON><PERSON><PERSON>,
    Dropdown<PERSON>enuContent,
    Dropdown<PERSON>enuI<PERSON>,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {Button} from "@/components/ui/button";
import submitEditVideo from "../_actions/edit-video";
import submitVideoRemove from "../_actions/remove-video";
import submitDeleteImage from "../_actions/delete-image";
import submitDownloadVideo from "../_actions/download-video";
import {useRouter} from "next/navigation";
import Link from "next/link";

const API_URL = "https://dev-api.streambliss.cloud";

// Duration formatting function
const formattedDuration = (duration: number) => {
    if (!duration) return "00:00";
    const totalSeconds = Math.floor(duration);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
        return `${hours}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    } else {
        return `${minutes}:${String(seconds).padStart(2, "0")}`;
    }
};

type MediaItem = {
    id: string;
    title: string;
    thumbnailUrl: string | null;
    type: "video" | "image";
    createdAt: Date;
    views: number;
    likes: number;
    tags: string[];
    url: string;
    shortLink: string;
    duration?: number;
    isPrivate?: boolean;
    commentsDisabled?: boolean;
    musicDisabled?: boolean;
    showCommunity?: boolean;
    approvedForCommunity?: boolean;
};

type NewMediaGridProps = {
    media: MediaItem[];
    userId: string;
    authToken: string;
    userSubscription: string;
    videos: any[];
    images: any[];
    videoSubmit: boolean;
    setVideoSubmit: (value: boolean) => void;
    onImageUpload: (count: number) => void;
    showUploadDialog: boolean;
    setShowUploadDialog: (value: boolean) => void;
    currentPage?: number;
    totalPages?: number;
    onPageChange?: (page: number) => void;
    totalItems?: number;
};

export function NewMediaGrid({
                                 media,
                                 userId,
                                 authToken,
                                 userSubscription,
                                 videos,
                                 images,
                                 videoSubmit,
                                 setVideoSubmit,
                                 onImageUpload,
                                 showUploadDialog,
                                 setShowUploadDialog,
                                 currentPage = 1,
                                 totalPages = 1,
                                 onPageChange,
                                 totalItems = 0,
                             }: NewMediaGridProps) {
    const { success, error } = useEnhancedToast();
    const [selectedVideo, setSelectedVideo] = useState<any | null>(null);
    const [selectedImage, setSelectedImage] = useState<any | null>(null);
    const [editingVideo, setEditingVideo] = useState<any | null>(null);
    const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
    const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [showCommunityPrompt, setShowCommunityPrompt] = useState(false);
    const [lastUploadedVideoId, setLastUploadedVideoId] = useState<string | null>(
        null,
    );

    // Processing video states
    const [processingVideos, setProcessingVideos] = useState<ProcessingVideo[]>(
        [],
    );
    const [completedProcessingVideos, setCompletedProcessingVideos] = useState<
        ProcessingVideo[]
    >([]);
    const [activeUploadId, setActiveUploadId] = useState<string | null>(null);
    const uploadInitializedRef = useRef(false);

    const router = useRouter();
    const {onEvent, offEvent, isConnected} = useSocket();

    // Check for pending community prompt after page load/refresh
    useEffect(() => {
        const pendingPrompt = localStorage.getItem("pendingCommunityPrompt");
        if (pendingPrompt) {
            const {videoId, timestamp} = JSON.parse(pendingPrompt);
            // Only show if within last 30 seconds (to avoid stale prompts)
            if (Date.now() - timestamp < 30000) {
                setLastUploadedVideoId(videoId);
                setShowCommunityPrompt(true);
            }
            localStorage.removeItem("pendingCommunityPrompt");
        }
    }, []);

    const getTagIcon = (tag: string) => {
        switch (tag.toLowerCase()) {
            case "music":
                return <Music className="w-3 h-3 text-white"/>;
            case "public":
                return <Globe className="w-3 h-3 text-white"/>;
            case "private":
                return <Lock className="w-3 h-3 text-white"/>;
            case "community":
                return <Users className="w-3 h-3 text-white"/>;
            default:
                return null;
        }
    };

    const handleSaveSettings = async (videoId: string, settings: any) => {
        try {
            // TODO: change to API route

            /*
            await submitEditVideo({
                videoId: videoId,
                title: settings.title,
                isPrivate: Boolean(settings.isPrivate),
                commentsDisabled: Boolean(settings.commentsDisabled),
                musicDisabled: Boolean(settings.musicDisabled),
                showCommunity: Boolean(settings.showCommunity),
            });
             */

            const response = await fetch("/api/user/media/video/update", {
                method: "PATCH",
                body: JSON.stringify({
                    id: videoId,
                    title: settings.title,
                    isPrivate: Boolean(settings.isPrivate),
                    commentsDisabled: Boolean(settings.commentsDisabled),
                    musicDisabled: Boolean(settings.musicDisabled),
                    showCommunity: Boolean(settings.showCommunity),
                }),
            })

            const data = await response.json();
            if (!(data.status >= 200 && data.status < 300)) {
                error("Update Failed", data.message);
                return;
            }

            success("Video Updated", "Video has been updated.");

            router.refresh();
        } catch (err) {
            console.error("Error updating settings:", err);
            error("Update Error", "Failed to update settings");
        }
    };

    const handleDeleteVideo = async (videoId: string) => {
        try {
            const response = await fetch("/api/user/media/video/delete", {
                method: "DELETE",
                body: JSON.stringify({
                    videoId: videoId,
                }),
            });

            const data = await response.json();
            if (!response.ok) {
                error("Delete Failed", data.message);
                return;
            }

            success("Video Deleted", data.message);
            router.refresh();
        } catch (err) {
            console.error("Error deleting video:", err);
            error("Delete Error", "Failed to delete video");
        }
    };

    const handleDeleteImage = async (imageId: string) => {
        try {
            const response = await fetch("/api/user/media/image/delete", {
                method: "DELETE",
                body: JSON.stringify({
                    imageId: imageId,
                }),
            });

            const data = await response.json();
            if (!response.ok) {
                error("Delete Failed", data.message);
                return;
            }

            success("Image Deleted", data.message);
            router.refresh();
        } catch (err) {
            console.error("Error deleting image:", err);
            error("Delete Error", "Failed to delete image");
        }
    };

    const copyShortLink = (shortLink: string) => {
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "";
        const linkType = media.find((m) => m.shortLink === shortLink)?.type;
        const url =
            linkType === "image"
                ? `${baseUrl}/i/${shortLink}`
                : `${baseUrl}/v/${shortLink}`;

        navigator.clipboard.writeText(url);
        success("Link Copied", "Link has been copied to your clipboard");
    };

    const downloadVideo = async (videoId: string) => {
        const response = await fetch("/api/user/media/video/download", {
            method: "POST",
            body: JSON.stringify({
                videoId
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            error("Download Failed", errorText);
            return;
        }

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        const a = document.createElement("a");
        a.href = url;
        a.download = `${videoId}.mp4`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        success("Video Downloaded", "The video has been downloaded!");
    };

    const handlePlayVideo = (item: MediaItem) => {
        if (item.type === "video") {
            const video = videos.find((v) => v.id === item.id);
            if (video) {
                setSelectedVideo(video);
                setIsVideoModalOpen(true);
            }
        } else if (item.type === "image") {
            const image = images.find((img) => img.id === item.id);
            if (image) {
                setSelectedImage(image);
                setIsImageModalOpen(true);
            }
        }
    };

    // Auto-remove completed processing cards after 5 seconds
    useEffect(() => {
        if (completedProcessingVideos.length > 0) {
            const timer = setTimeout(() => setCompletedProcessingVideos([]), 5000);
            return () => clearTimeout(timer);
        }
    }, [completedProcessingVideos]);

    // Handle websocket upload events
    useEffect(() => {
        if (!isConnected) return;

        const resetUploadState = () => {
            setActiveUploadId(null);
            uploadInitializedRef.current = false;
        };

        const cleanupAllProcessingCards = () => {
            setProcessingVideos([]);
            setCompletedProcessingVideos([]);
        };

        // Initialize upload when videoSubmit is true
        if (videoSubmit && !uploadInitializedRef.current) {
            cleanupAllProcessingCards();

            const placeholderId = `uploading-placeholder-${Date.now()}`;
            setActiveUploadId(placeholderId);
            uploadInitializedRef.current = true;

            setProcessingVideos([
                {
                    id: placeholderId,
                    title: "Uploading...",
                    progress: 0,
                    message: "Connecting to the API",
                    fileName: "unknown.mp4",
                    status: "uploading",
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ]);

            setVideoSubmit(false);
        }

        const handleVideoUpload = (_data: any) => {
            const data = JSON.parse(_data);
            if (!data) return;

            if (data.id && data.id !== "uploading-placeholder") {
                if (
                    activeUploadId &&
                    activeUploadId.startsWith("uploading-placeholder")
                ) {
                    setActiveUploadId(data.id);
                }
            }

            const updatedVideo = {
                ...data,
                id: data.id || activeUploadId || `upload-${Date.now()}`,
                progress: typeof data.progress === "number" ? data.progress : 0,
                status: data.status,
            };

            if (data.status === "complete") {
                setProcessingVideos([]);
                setCompletedProcessingVideos([updatedVideo]);

                success("Upload Complete!", "Your video has been uploaded successfully");

                setTimeout(() => {
                    window.location.reload();
                }, 2000);

                resetUploadState();
            } else {
                setProcessingVideos([updatedVideo]);
            }
        };

        onEvent("video-step", handleVideoUpload);
        return () => {
            offEvent("video-step", handleVideoUpload);
            resetUploadState();
        };
    }, [
        userId,
        videoSubmit,
        isConnected,
        onEvent,
        offEvent,
        setVideoSubmit,
        activeUploadId,
    ]);

    // Render processing card component
    const renderProcessingCard = (
        video: ProcessingVideo,
        isCompleted = false,
    ) => (
        <motion.div
            key={video.id}
            className="rounded-lg sm:rounded-xl bg-[rgba(17,0,24,1)] border border-[rgba(184,81,223,0.5)] p-3 sm:p-4 relative overflow-hidden"
            initial={{opacity: 0, scale: 0.9}}
            animate={{opacity: 1, scale: 1}}
            exit={{opacity: 0, scale: 0.9}}
            transition={{duration: 0.3}}
        >
            {/* Thumbnail/Status Area */}
            <div
                className="relative rounded-lg overflow-hidden mb-3 aspect-[1.5] bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                {video.thumbnailUrl ? (
                    <div className="absolute inset-0 w-full h-full">
                        <Image
                            src={video.thumbnailUrl}
                            alt="Video thumbnail"
                            fill
                            className="object-cover"
                            unoptimized={shouldDisableOptimization(video.thumbnailUrl)}
                        />
                        <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                            <div className="text-center">
                                {isCompleted ? (
                                    <div
                                        className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <CheckCircle className="w-6 h-6 text-white"/>
                                    </div>
                                ) : (
                                    <div
                                        className="w-10 h-10 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                                )}
                                <div className="text-white text-xs font-medium">
                                    {isCompleted ? "Upload Complete!" : "Processing..."}
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="text-center">
                        {isCompleted ? (
                            <div
                                className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                                <CheckCircle className="w-8 h-8 text-white"/>
                            </div>
                        ) : (
                            <div
                                className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                        )}
                        <div className="text-xs font-medium text-white mb-2">
                            {isCompleted
                                ? "Upload Complete!"
                                : video.status === "uploading"
                                    ? "Uploading Video"
                                    : video.status === "processing"
                                        ? "Processing Video"
                                        : video.status === "muting"
                                            ? "Creating Muted Version"
                                            : "Processing..."}
                        </div>
                    </div>
                )}
            </div>

            {/* Content */}
            <div className="space-y-4">
                {/* Title */}
                <h3 className="text-lg font-semibold leading-[1.4] text-white truncate">
                    {video.title}
                </h3>

                {/* Progress Steps */}
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                        <div
                            className={`flex items-center gap-2 ${
                                video.status === "uploading" ||
                                (isCompleted && video.progress >= 33)
                                    ? "text-green-500 font-medium"
                                    : "text-white/40"
                            }`}
                        >
                            <div
                                className={`w-2 h-2 rounded-full ${
                                    video.status === "uploading"
                                        ? "bg-purple-500 animate-pulse"
                                        : isCompleted || video.progress >= 33
                                            ? "bg-green-500"
                                            : "bg-white/20"
                                }`}
                            ></div>
                            Upload
                        </div>
                        <div
                            className={`flex items-center gap-2 ${
                                video.status === "processing" ||
                                (isCompleted && video.progress >= 66)
                                    ? "text-green-500 font-medium"
                                    : "text-white/40"
                            }`}
                        >
                            <div
                                className={`w-2 h-2 rounded-full ${
                                    video.status === "processing"
                                        ? "bg-purple-500 animate-pulse"
                                        : isCompleted || video.progress >= 66
                                            ? "bg-green-500"
                                            : "bg-white/20"
                                }`}
                            ></div>
                            Process
                        </div>
                        <div
                            className={`flex items-center gap-2 ${
                                isCompleted || video.status === "complete"
                                    ? "text-green-500 font-medium"
                                    : "text-white/40"
                            }`}
                        >
                            <div
                                className={`w-2 h-2 rounded-full ${
                                    isCompleted || video.status === "complete"
                                        ? "bg-green-500"
                                        : "bg-white/20"
                                }`}
                            ></div>
                            Complete
                        </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-white/10 rounded-full h-1.5">
                        <div
                            className="bg-gradient-to-r from-purple-500 to-pink-500 h-1.5 rounded-full transition-all duration-500"
                            style={{width: `${video.progress}%`}}
                        ></div>
                    </div>
                </div>

                {/* Status Message */}
                <p className="text-sm text-white/70">
                    {video.message || "Processing your video..."}
                </p>
            </div>
        </motion.div>
    );

    return (
        <div
            className="mt-6 sm:mt-8 min-h-[300px] sm:min-h-[400px] font-['Montserrat'] text-white"
            data-tutorial="media-grid"
        >
            <div
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6">
                {/* Processing Video Cards */}
                <AnimatePresence>
                    {processingVideos.map((video) => renderProcessingCard(video, false))}
                    {completedProcessingVideos.map((video) =>
                        renderProcessingCard(video, true),
                    )}
                </AnimatePresence>

                {/* Regular Media Cards */}
                {media.map((item, index) => (
                    <div
                        key={item.id}
                        className="rounded-lg sm:rounded-xl bg-[rgba(17,0,24,1)] border border-[rgba(255,255,255,0.12)] p-4 sm:p-5 cursor-pointer hover:border-[rgba(255,255,255,0.2)] transition-all duration-300"
                        onClick={() => handlePlayVideo(item)}
                    >
                        {/* Thumbnail */}
                        <div className="relative rounded-lg overflow-hidden mb-3">
                            <div className="aspect-[1.5] relative">
                                {item.type === "video" ? (
                                    <Image
                                        src={`${API_URL}/videos/thumbnail/${item.id}`}
                                        alt={item.title}
                                        fill
                                        className="object-cover"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = "none";
                                            target.nextElementSibling?.classList.remove("hidden");
                                        }}
                                    />
                                ) : item.thumbnailUrl ? (
                                    <Image
                                        src={API_URL + "/images/stream/" + userId + "/" + item.id}
                                        alt={item.title}
                                        fill
                                        className="object-cover"
                                        unoptimized={shouldDisableOptimization(item.thumbnailUrl)}
                                    />
                                ) : null}

                                {/* Fallback for failed thumbnail loads */}
                                <div
                                    className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center hidden">
                  <span className="text-white/40 text-3xl">
                    {item.type === "video" ? "🎬" : "🖼️"}
                  </span>
                                </div>

                                {/* Duration Overlay (top-left) */}
                                {item.type === "video" && item.duration && (
                                    <div className="absolute top-3 left-3">
                                        <div className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-lg bg-black/80 backdrop-blur-sm border border-white/10 shadow-lg">
                                            <svg className="w-3 h-3 text-white/90" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-white font-['Montserrat'] text-xs font-semibold tracking-wide">
                                                {formattedDuration(item.duration)}
                                            </span>
                                        </div>
                                    </div>
                                )}

                                {/* View Count Overlay (top-right) */}
                                <div className="absolute top-3 right-3">
                                    <div className="flex items-center gap-1.5 px-2.5 py-1.5 rounded-lg bg-gradient-to-r from-purple-600/90 to-pink-600/90 backdrop-blur-sm border border-white/20 shadow-lg hover:from-purple-500/90 hover:to-pink-500/90 transition-all duration-300">
                                        <svg className="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-white font-['Montserrat'] text-xs font-semibold tracking-wide">
                                            {item.views.toLocaleString()}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="space-y-2 sm:space-y-3">
                            {/* Title and Menu */}
                            <div className="flex items-center justify-between">
                                <h3 className="text-sm sm:text-base font-semibold leading-[1.4] flex-1 truncate">
                                    {item.title}
                                </h3>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-[18.75px] w-[16.071px] text-white/70 hover:bg-white/10 rotate-90 cursor-pointer"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <div className="flex flex-col gap-[2.679px]">
                                                <div className="w-1 h-1 bg-white rounded-full"></div>
                                                <div className="w-1 h-1 bg-white rounded-full"></div>
                                                <div className="w-1 h-1 bg-white rounded-full"></div>
                                            </div>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent
                                        align="end"
                                        onClick={(e) => e.stopPropagation()}
                                        className="w-[152px] p-3.5 border border-white/24 bg-black/80 rounded-lg"
                                    >
                                        <div className="flex flex-col gap-3">
                                            {item.type === "video" && (
                                                <>
                                                    {/* Settings */}
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            const video = videos.find(
                                                                (v) => v.id === item.id,
                                                            );
                                                            if (video) {
                                                                setEditingVideo(video);
                                                                setIsSettingsDialogOpen(true);
                                                            }
                                                        }}
                                                        className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                                    >
                                                        <Settings className="w-5 h-5 text-white"/>
                                                        <span
                                                            className="text-sm font-normal text-white font-['Montserrat']">
                              Settings
                            </span>
                                                    </DropdownMenuItem>

                                                    <div className="w-full h-px bg-white/10"></div>

                                                    {/* Copy Link */}
                                                    <DropdownMenuItem
                                                        onClick={() => copyShortLink(item.shortLink)}
                                                        className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                                    >
                                                        <Link2 className="w-5 h-5 text-white"/>
                                                        <span
                                                            className="text-sm font-normal text-white font-['Montserrat']">
                              Copy Link
                            </span>
                                                    </DropdownMenuItem>

                                                    <div className="w-full h-px bg-white/10"></div>

                                                    {/* Analytics */}
                                                    <DropdownMenuItem
                                                        onClick={() =>
                                                            router.push(`/v/${item.shortLink}/analytics`)
                                                        }
                                                        className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                                    >
                                                        <BarChart3 className="w-5 h-5 text-white"/>
                                                        <span
                                                            className="text-sm font-normal text-white font-['Montserrat']">
                              Analytics
                            </span>
                                                    </DropdownMenuItem>

                                                    <div className="w-full h-px bg-white/10"></div>

                                                    {/* Download */}
                                                    <DropdownMenuItem
                                                        onClick={() => downloadVideo(item.id)}
                                                        className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                                    >
                                                        <Download className="w-5 h-5 text-white"/>
                                                        <span
                                                            className="text-sm font-normal text-white font-['Montserrat']">
                              Download
                            </span>
                                                    </DropdownMenuItem>

                                                    <div className="w-full h-px bg-white/10"></div>
                                                </>
                                            )}

                                            {/* For images, just show Copy Link and Delete */}
                                            {item.type === "image" && (
                                                <>
                                                    <DropdownMenuItem
                                                        onClick={() => copyShortLink(item.shortLink)}
                                                        className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                                    >
                                                        <Link2 className="w-5 h-5 text-white"/>
                                                        <span
                                                            className="text-sm font-normal text-white font-['Montserrat']">
                              Copy Link
                            </span>
                                                    </DropdownMenuItem>

                                                    <div className="w-full h-px bg-white/10"></div>
                                                </>
                                            )}

                                            {/* Delete */}
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    if (item.type === "video") {
                                                        handleDeleteVideo(item.id);
                                                    } else {
                                                        handleDeleteImage(item.id);
                                                    }
                                                }}
                                                className="flex items-center gap-2.5 p-0 cursor-pointer hover:bg-transparent"
                                            >
                                                <Trash2 className="w-5 h-5 text-white"/>
                                                <span className="text-sm font-normal text-white font-['Montserrat']">
                          Delete
                        </span>
                                            </DropdownMenuItem>
                                        </div>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                            {/* Tags */}
                            <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                {item.tags.map((tag, tagIndex) => (
                                    <div
                                        key={tagIndex}
                                        className="rounded-lg bg-[rgba(255,255,255,0.08)] border border-[rgba(255,255,255,0.2)] px-2 py-0.5 sm:py-1 flex items-center gap-1"
                                    >
                                        <div className="flex items-center gap-1">
                                            {getTagIcon(tag)}
                                            <span className="text-xs font-medium whitespace-nowrap">
                        {tag}
                      </span>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Shortlink Copy Field */}
                            <div
                                className="flex items-center gap-2 p-2 rounded-lg bg-white/5 border border-white/10"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <a
                                    href={`${process.env.NEXT_PUBLIC_APP_URL || ""}/${item.type === "image" ? "i" : "v"}/${item.shortLink}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    onClick={(e) => e.stopPropagation()}
                                    className="text-xs text-white/80 hover:text-white font-mono flex-1 truncate cursor-pointer transition-colors duration-200 hover:underline"
                                >
                                    {item.shortLink}
                                </a>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        copyShortLink(item.shortLink);
                                    }}
                                    className="flex items-center justify-center w-6 h-6 rounded-md bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-200 group flex-shrink-0 cursor-pointer"
                                >
                                    <Copy className="w-3 h-3 text-white/70 group-hover:text-white transition-colors"/>
                                </button>
                            </div>
                        </div>
                    </div>
                ))}

                {/* Empty state - show when no media */}
                {media.length === 0 && (
                    <div className="col-span-full flex flex-col items-center justify-center py-20 text-center">
                        <div
                            className="w-24 h-24 rounded-full bg-white/5 border border-white/10 flex items-center justify-center mb-6">
                            <svg
                                className="w-12 h-12 text-white/40"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V7H3a1 1 0 01-1-1V5a1 1 0 011-1h4z"
                                />
                            </svg>
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">
                            No files found
                        </h3>
                        <p className="text-white/70">
                            Upload your first file to get started
                        </p>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && onPageChange && (
                <div className="mt-8 flex flex-col sm:flex-row items-center justify-between gap-4">
                    {/* Page info */}
                    <div className="text-sm text-white/70 font-['Montserrat']">
                        Showing {(currentPage - 1) * 12 + 1} to{" "}
                        {Math.min(currentPage * 12, totalItems)} of {totalItems} files
                    </div>

                    {/* Pagination controls */}
                    <div className="flex items-center gap-2">
                        {/* Previous button */}
                        <button
                            onClick={() => onPageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="flex items-center justify-center w-8 h-8 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            <ChevronLeft className="w-4 h-4 text-white/70"/>
                        </button>

                        {/* Page numbers */}
                        <div className="flex items-center gap-1">
                            {Array.from({length: totalPages}, (_, i) => i + 1).map(
                                (page) => {
                                    const shouldShow =
                                        page === 1 ||
                                        page === totalPages ||
                                        (page >= currentPage - 1 && page <= currentPage + 1);

                                    if (!shouldShow) {
                                        if (page === currentPage - 2 || page === currentPage + 2) {
                                            return (
                                                <span key={page} className="px-2 text-white/50">
                          ...
                        </span>
                                            );
                                        }
                                        return null;
                                    }

                                    return (
                                        <button
                                            key={page}
                                            onClick={() => onPageChange(page)}
                                            className={`flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 font-['Montserrat'] text-sm font-medium ${
                                                page === currentPage
                                                    ? "bg-gradient-to-r from-[#B851E0] to-[#EB489B] text-white"
                                                    : "bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 text-white/70"
                                            }`}
                                        >
                                            {page}
                                        </button>
                                    );
                                },
                            )}
                        </div>

                        {/* Next button */}
                        <button
                            onClick={() => onPageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="flex items-center justify-center w-8 h-8 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            <ChevronRight className="w-4 h-4 text-white/70"/>
                        </button>
                    </div>
                </div>
            )}

            {/* Modals and Dialogs */}
            <UnifiedUploader
                open={showUploadDialog}
                onOpenChange={setShowUploadDialog}
                setVideoSubmit={setVideoSubmit}
                userId={userId}
                authToken={authToken}
                subscription={userSubscription}
                fileType="both"
                onUpload={(newImages) => onImageUpload(newImages.length)}
                onVideoUploadSuccess={(videoId, refreshPage) => {
                    // Store the community prompt request in localStorage to survive page refresh
                    localStorage.setItem(
                        "pendingCommunityPrompt",
                        JSON.stringify({
                            videoId,
                            timestamp: Date.now(),
                        }),
                    );
                    refreshPage();
                }}
            />

            <CommunityPromptModal
                open={showCommunityPrompt}
                onOpenChange={(open) => {
                    setShowCommunityPrompt(open);
                    if (!open) {
                        setLastUploadedVideoId(null);
                    }
                }}
                onConfirm={(publishToCommunity) => {
                    if (lastUploadedVideoId && publishToCommunity) {
                        console.log("Publishing video to community:", lastUploadedVideoId);
                    }
                    setLastUploadedVideoId(null);
                }}
            />

            {selectedVideo && (
                <VideoModal
                    isOpen={isVideoModalOpen}
                    onClose={() => {
                        setSelectedVideo(null);
                        setIsVideoModalOpen(false);
                    }}
                    video={{
                        ...selectedVideo,
                        isPrivate: selectedVideo.isPrivate ?? false,
                    }}
                    userId={userId}
                    userSubscription={userSubscription}
                />
            )}

            {selectedImage && (
                <ImageModal
                    isOpen={isImageModalOpen}
                    onClose={() => {
                        setSelectedImage(null);
                        setIsImageModalOpen(false);
                    }}
                    image={selectedImage}
                    userId={userId}
                />
            )}

            {editingVideo && (
                <VideoSettingsDialog
                    isOpen={isSettingsDialogOpen}
                    onClose={() => {
                        setIsSettingsDialogOpen(false);
                        setEditingVideo(null);
                    }}
                    video={editingVideo}
                    onSave={handleSaveSettings}
                />
            )}
        </div>
    );
}