"use client";

import type React from "react";
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import submitCreateTicket from "../_actions/create-ticket";
import { Label } from "@/components/ui/label";
import { Bold, Italic, Link2, Paperclip, Send, X } from "lucide-react";
import { z } from "zod";
// Utility function to convert camelCase to Title Case
const camelToTitleCase = (str: string) => {
  return str.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
};

const ticketSchema = z.object({
  title: z
    .string()
    .min(3, "Title must be at least 3 characters")
    .max(100, "Title must be less than 100 characters"),
  content: z.string().min(10, "Description must be at least 10 characters"),
  priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]),
  category: z.enum(["GENERAL", "TECHNICAL", "BILLING", "SUGGESTION", 'OTHER', 'BUG']),
});

export function NewTicketDialog() {
  const { success, error } = useEnhancedToast();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [category, setCategory] = useState("GENERAL");
  const [priority, setPriority] = useState("NORMAL");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [files, setFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const resetForm = () => {
    setTitle("");
    setContent("");
    setCategory("GENERAL");
    setPriority("NORMAL");
    setErrors({});
    setFiles([]);
  };

  const validateForm = () => {
    try {
      ticketSchema.parse({ title, content, category, priority });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      const response = await fetch("/api/tickets/create", {
        method: "POST",
        body: JSON.stringify({
          title,
          content,
          priority,
          category,
        })
      });

      const data = await response.json();
      if (!(data.status >= 200 && data.status < 300)) {
        error("Ticket Creation Failed", data.message);
        return;
      }

      if (data.data) {
        success("Ticket Created", "Ticket has been created.");
        router.push("/dashboard/tickets/" + data.data.id);
      }
    } catch (err) {
      error("Creation Error", "Failed to create ticket. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        id="new-ticket-button"
        onClick={() => setOpen(true)}
        className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white text-sm md:text-base font-semibold rounded-full px-6 py-3 h-auto flex items-center gap-2"
      >
        New Ticket
      </Button>
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
          if (!isOpen) resetForm();
        }}
      >
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto bg-[#121212] text-white border-[#333]">
          <DialogHeader>
            <DialogTitle className="text-xl">Create New Ticket</DialogTitle>
          </DialogHeader>

          <form onSubmit={onSubmit} className="space-y-6 pt-4">
            <div className="grid gap-2">
              <Label
                htmlFor="title"
                className="text-sm font-medium flex items-center"
              >
                Title <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Brief summary of your issue"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                disabled={loading}
                className={`bg-[#1e1e1e] border-[#333] ${errors.title ? "border-red-500" : ""}`}
              />
              {errors.title && (
                <p className="text-sm text-red-500 mt-1">{errors.title}</p>
              )}
            </div>

            <div className="flex flex-row gap-2 w-full">
              <div className="grid gap-2 w-full">
                <Label
                    htmlFor="category"
                    className="text-sm font-medium flex items-center"
                >
                  Category <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                    value={category}
                    onValueChange={setCategory}
                    disabled={loading}
                >
                  <SelectTrigger
                      id="category"
                      className="bg-[#1e1e1e] border-[#333]"
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1e1e1e] border-[#333]">
                    <SelectItem
                        value="GENERAL"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      General Question
                    </SelectItem>
                    <SelectItem
                        value="TECHNICAL"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      Technical Support
                    </SelectItem>
                    <SelectItem
                        value="BILLING"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      Billing Issue
                    </SelectItem>
                    <SelectItem
                        value="SUGGESTION"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      Suggestions
                    </SelectItem>
                    <SelectItem
                        value="OTHER"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      Other Issues
                    </SelectItem>
                    <SelectItem
                        value="BUG"
                        className="hover:bg-[#131313] hover:cursor-pointer"
                    >
                      Bug
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                    <p className="text-sm text-red-500 mt-1">{errors.category}</p>
                )}
              </div>
              <div className="grid gap-2 w-full">
              <Label
                  htmlFor="priority"
                  className="text-sm font-medium flex items-center"
              >
                Priority <span className="text-red-500 ml-1">*</span>
              </Label>
              <Select
                  value={priority}
                  onValueChange={setPriority}
                  disabled={loading}
              >
                <SelectTrigger
                    id="priority"
                    className="bg-[#1e1e1e] border-[#333]"
                >
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent className="bg-[#1e1e1e] border-[#333]">
                  <SelectItem
                      value="LOW"
                      className="hover:bg-[#131313] hover:cursor-pointer"
                  >
                    Low
                  </SelectItem>
                  <SelectItem
                      value="NORMAL"
                      className="hover:bg-[#131313] hover:cursor-pointer"
                  >
                    Normal
                  </SelectItem>
                  <SelectItem
                      value="HIGH"
                      className="hover:bg-[#131313] hover:cursor-pointer"
                  >
                    High
                  </SelectItem>
                  <SelectItem
                      value="URGENT"
                      className="hover:bg-[#131313] hover:cursor-pointer"
                  >
                    Urgent
                  </SelectItem>
                </SelectContent>
              </Select>
              {errors.priority && (
                  <p className="text-sm text-red-500 mt-1">{errors.priority}</p>
              )}
            </div>
            </div>

            <div className="grid gap-2">
              <Label
                htmlFor="content"
                className="text-sm font-medium flex items-center"
              >
                Message <span className="text-red-500 ml-1">*</span>
              </Label>

              <div className="rounded-md border border-[#333] overflow-hidden">
                <div className="flex items-center gap-1 p-2 bg-[#1e1e1e] border-b border-[#333]">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-slate-400 hover:text-white hover:bg-[#333]"
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-slate-400 hover:text-white hover:bg-[#333]"
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-slate-400 hover:text-white hover:bg-[#333]"
                  >
                    <Link2 className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-slate-400 hover:text-white hover:bg-[#333]"
                    onClick={triggerFileInput}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileUpload}
                    className="hidden"
                    multiple
                  />
                </div>
                <Textarea
                  id="content"
                  placeholder="Please provide details about your issue..."
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  disabled={loading}
                  className={`min-h-[250px] resize-none bg-[#1e1e1e] border-0 rounded-none focus-visible:ring-0 ${
                    errors.content ? "border-red-500" : ""
                  }`}
                />
              </div>
              {errors.content && (
                <p className="text-sm text-red-500 mt-1">{errors.content}</p>
              )}

              {/* File attachments preview */}
              {files.length > 0 && (
                <div className="mt-4">
                  <Label className="text-sm font-medium mb-2">
                    Attachments
                  </Label>
                  <div className="space-y-2">
                    {files.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-[#1e1e1e] rounded border border-[#333]"
                      >
                        <div className="flex items-center gap-2 overflow-hidden">
                          <Paperclip className="h-4 w-4 flex-shrink-0" />
                          <span className="text-sm truncate">{file.name}</span>
                          <span className="text-xs text-slate-400">
                            ({(file.size / 1024).toFixed(1)} KB)
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-slate-400 hover:text-white hover:bg-[#333]"
                          onClick={() => removeFile(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="gap-2 sm:gap-0">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={loading}
                className="bg-transparent border-[#333] text-white hover:bg-[#333]"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {loading ? (
                  "Creating..."
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Create
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}