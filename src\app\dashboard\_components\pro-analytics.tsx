"use client";

import React from "react";
import { motion } from "framer-motion";

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function ProAnalytics() {
  return (
    <>
      {/* Traffic Sources and Viewer Retention */}
      <div className="flex flex-col lg:flex-row gap-4 md:gap-6 mb-6">
        {/* Traffic Sources */}
        <motion.div variants={itemVariants} className="flex-1">
          <div className="w-full max-w-[846px] h-[300px] md:h-[468px] rounded-xl border border-white/12 bg-[#110018] relative">
            <h3 className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat'] absolute left-3 md:left-6 top-3 md:top-6">
              Traffic Sources (Estimated)
            </h3>

            <div className="absolute left-4 md:left-16 top-[60px] md:top-[125px] right-4 md:right-16 h-[200px] md:h-[319px] opacity-70">
              <div className="text-white font-normal text-sm md:text-lg leading-[160%] font-['Montserrat'] absolute left-0 top-0">
                Direct 45%
              </div>
              <div className="text-white font-normal text-sm md:text-lg leading-[160%] font-['Montserrat'] absolute left-[140px] md:left-[200px] top-[50px] md:top-[77px]">
                Search 30%
              </div>
              <div className="text-white font-normal text-sm md:text-lg leading-[160%] font-['Montserrat'] absolute right-[60px] md:right-[140px] top-[100px] md:top-[154px]">
                Social 15%
              </div>
              <div className="text-white font-normal text-sm md:text-lg leading-[160%] font-['Montserrat'] absolute right-0 bottom-[30px] md:bottom-[68px]">
                External 10%
              </div>
              <div className="text-white font-normal text-xs md:text-sm leading-[160%] font-['Montserrat'] absolute left-1/2 transform -translate-x-1/2 bottom-0">
                Based on Industry Standard Distribution
              </div>
            </div>
          </div>
        </motion.div>

        {/* Viewer Retention Curve */}
        <motion.div variants={itemVariants} className="flex-1">
          <div className="w-full max-w-[846px] h-[300px] md:h-[468px] rounded-xl border border-white/12 bg-[#110018] relative">
            <h3 className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat'] absolute left-3 md:left-6 top-3 md:top-6">
              Viewer Retention Curve (Estimated)
            </h3>

            {/* Retention Chart */}
            <div className="absolute left-3 md:left-[116px] top-[60px] md:top-[98px] right-3 md:right-[111px] h-[150px] md:h-[240px]">
              <svg
                className="w-full h-full"
                viewBox="0 0 639 248"
                fill="none"
                preserveAspectRatio="none"
              >
                <path
                  d="M10.6733 240.997L13.2115 13.4629C199.284 15.5386 500.154 116.288 627.33 166.403L626.421 247.866L10.6733 240.997Z"
                  fill="url(#paint0_linear_retention)"
                />
                <path
                  d="M13.2442 10.3416C90.4794 10.8078 321.432 42.1875 627.358 163.977"
                  stroke="#B851E0"
                  strokeWidth="6.74219"
                />
                {/* Dots */}
                <circle
                  cx="10"
                  cy="10"
                  r="7"
                  fill="url(#paint1_linear_retention)"
                />
                <circle
                  cx="107"
                  cy="37"
                  r="7"
                  fill="url(#paint2_linear_retention)"
                />
                <circle
                  cx="274"
                  cy="71"
                  r="7"
                  fill="url(#paint3_linear_retention)"
                />
                <circle
                  cx="455"
                  cy="121"
                  r="7"
                  fill="url(#paint4_linear_retention)"
                />
                <circle
                  cx="629"
                  cy="167"
                  r="7"
                  fill="url(#paint5_linear_retention)"
                />

                <defs>
                  <linearGradient
                    id="paint0_linear_retention"
                    x1="318.515"
                    y1="19.8978"
                    x2="316.024"
                    y2="243.268"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" stopOpacity="0.5" />
                    <stop offset="1" stopColor="#B851E0" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="paint1_linear_retention"
                    x1="7.86588"
                    y1="0"
                    x2="7.86588"
                    y2="15.7318"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" />
                    <stop offset="1" stopColor="#EB489B" />
                  </linearGradient>
                  <linearGradient
                    id="paint2_linear_retention"
                    x1="7.86588"
                    y1="0"
                    x2="7.86588"
                    y2="15.7318"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" />
                    <stop offset="1" stopColor="#EB489B" />
                  </linearGradient>
                  <linearGradient
                    id="paint3_linear_retention"
                    x1="7.86588"
                    y1="0"
                    x2="7.86588"
                    y2="15.7318"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" />
                    <stop offset="1" stopColor="#EB489B" />
                  </linearGradient>
                  <linearGradient
                    id="paint4_linear_retention"
                    x1="7.86588"
                    y1="0"
                    x2="7.86588"
                    y2="15.7318"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" />
                    <stop offset="1" stopColor="#EB489B" />
                  </linearGradient>
                  <linearGradient
                    id="paint5_linear_retention"
                    x1="7.86588"
                    y1="0"
                    x2="7.86588"
                    y2="15.7318"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#B851E0" />
                    <stop offset="1" stopColor="#EB489B" />
                  </linearGradient>
                </defs>
              </svg>
            </div>

            {/* Chart Labels */}
            <div className="absolute left-3 md:left-12 bottom-8 md:bottom-[69px] right-3 md:right-12 flex justify-between opacity-70">
              <span className="text-white font-normal text-xs md:text-lg leading-[160%] font-['Montserrat']">
                Start (100%)
              </span>
              <span className="text-white font-normal text-xs md:text-lg leading-[160%] font-['Montserrat']">
                25%
              </span>
              <span className="text-white font-normal text-xs md:text-lg leading-[160%] font-['Montserrat']">
                50%
              </span>
              <span className="text-white font-normal text-xs md:text-lg leading-[160%] font-['Montserrat']">
                75%
              </span>
              <span className="text-white font-normal text-xs md:text-lg leading-[160%] font-['Montserrat']">
                End (20%)
              </span>
            </div>

            <div className="text-white font-normal text-xs md:text-sm leading-[160%] font-['Montserrat'] absolute left-1/2 transform -translate-x-1/2 bottom-3 md:bottom-6">
              Based on Typical Viewer Behavior Pattern
            </div>
          </div>
        </motion.div>
      </div>

      {/* Top Countries and Device Breakdown */}
      <div className="flex flex-col lg:flex-row gap-4 md:gap-6 mb-6">
        {/* Top Countries */}
        <motion.div variants={itemVariants} className="flex-1">
          <div className="w-full max-w-[846px] rounded-xl border border-white/12 bg-[#110018] p-6">
            <h3 className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat'] mb-6">
              Top Countries
            </h3>

            <div className="flex flex-col gap-6">
              {[
                { country: "United States", percentage: 65 },
                { country: "United Kingdom", percentage: 25 },
                { country: "Germany", percentage: 5 },
                { country: "India", percentage: 15 },
                { country: "Other", percentage: 35 },
              ].map((item, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-lg ${
                    index === 0
                      ? "border border-white/14 bg-white/6"
                      : "bg-white/6"
                  }`}
                >
                  <span className="text-white font-bold text-sm md:text-base leading-[160%] font-['Montserrat']">
                    {item.country}
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="flex h-2 w-20 items-center rounded-full bg-[#3D3D3D]">
                      <div
                        className="h-2 rounded-full bg-gradient-to-r from-[#B851E0] to-[#EB489B]"
                        style={{
                          width: `${(item.percentage / 65) * 100}%`,
                        }}
                      />
                    </div>
                    <span className="text-white font-bold text-xs md:text-sm leading-[160%] font-['Montserrat'] min-w-[32px]">
                      {item.percentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Device Breakdown */}
        <motion.div variants={itemVariants} className="flex-1">
          <div className="w-full max-w-[846px] rounded-xl border border-white/12 bg-[#110018] p-6">
            <h3 className="text-white font-bold text-lg md:text-2xl leading-[160%] font-['Montserrat'] mb-6">
              Device Breakdown
            </h3>

            <div className="flex flex-col gap-6">
              {[
                { device: "Mobile", percentage: 65 },
                { device: "Desktop", percentage: 25 },
                { device: "Tablet", percentage: 10 },
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg bg-white/6"
                >
                  <span className="text-white font-bold text-sm md:text-base leading-[160%] font-['Montserrat']">
                    {item.device}
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="flex h-2 w-20 items-center rounded-full bg-[#3D3D3D]">
                      <div
                        className="h-2 rounded-full bg-gradient-to-r from-[#B851E0] to-[#EB489B]"
                        style={{
                          width: `${(item.percentage / 65) * 100}%`,
                        }}
                      />
                    </div>
                    <span className="text-white font-bold text-xs md:text-sm leading-[160%] font-['Montserrat'] min-w-[32px]">
                      {item.percentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </>
  );
}