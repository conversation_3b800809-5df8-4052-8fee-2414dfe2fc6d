"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useDropzone, type Accept } from "react-dropzone";
import { Video, ImageIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { useVideoUpload } from "@/hooks/useUpload";
import { getImageByUserId } from "@/server/image";

type FileType = "video" | "image" | "both";

interface UnifiedUploaderProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setVideoSubmit?: (videoSubmit: boolean) => void;
  userId: string;
  authToken: string;
  subscription: string;
  fileType?: FileType;
  onUpload?: (newImages: any[]) => void;
  onVideoUploadSuccess?: (videoId: string, refreshPage: () => void) => void;
}

export function UnifiedUploader({
  open,
  onOpenChange,
  setVideoSubmit,
  userId,
  authToken,
  subscription,
  fileType = "both",
  onUpload,
  onVideoUploadSuccess,
}: UnifiedUploaderProps) {
  const { success, error } = useEnhancedToast();
  const [title, setTitle] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const {
    uploading: videoUploading,
    uploadVideo,
    refreshPage,
  } = useVideoUpload();

  const isVideoFile = file?.type.startsWith("video/");
  const isImageFile = file?.type.startsWith("image/");

  const handleCancel = useCallback(() => {
    setFile(null);
    setTitle("");
    onOpenChange(false);
  }, [onOpenChange]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleCancel();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleCancel();
      }
    };

    if (open) {
      document.body.style.overflow = "hidden";
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open, handleCancel]);

  const getAcceptedTypes = (): Accept => {
    switch (fileType) {
      case "video":
        return { "video/*": [".mp4", ".mov", ".avi", ".webm"] };
      case "image":
        return { "image/*": [".png", ".jpg", ".jpeg", ".gif"] };
      case "both":
      default:
        return {
          "video/*": [".mp4", ".mov", ".avi", ".webm"],
          "image/*": [".png", ".jpg", ".jpeg", ".gif"],
        };
    }
  };

  const getFileTypeLabel = () => {
    switch (fileType) {
      case "video":
        return "MP4, MOV, AVI, WebM";
      case "image":
        return "PNG, JPG, GIF";
      case "both":
      default:
        return "PNG, JPG, GIF, MP4, MOV, AVI, WebM";
    }
  };

  const getSizeLimit = useCallback(() => {
    if (fileType === "image") return "10MB";
    if (subscription === "FREE") return "512MB";
    if (subscription === "PRO") return "10GB";
    return "unlimited";
  }, [fileType, subscription]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];

      // Determine size limit based on file type and subscription
      let allowedSize = 0;
      if (file.type.startsWith("image/")) {
        allowedSize = 10 * 1024 * 1024; // 10MB for images
      } else if (file.type.startsWith("video/")) {
        if (subscription === "FREE") {
          allowedSize = 512 * 1024 * 1024; // 512MB
        } else if (subscription === "PRO") {
          allowedSize = 10 * 1024 * 1024 * 1024; // 10GB
        } else {
          allowedSize = 999999 * 1024 * 1024 * 1024; // 999999GB
        }
      }

      if (file.size > allowedSize) {
        const sizeLimit = file.type.startsWith("image/")
          ? "10MB"
          : getSizeLimit();
        error("File too large", `File size must be less than ${sizeLimit} for your ${subscription} subscription.`);
        return;
      }

      setFile(file);
      setTitle(file.name.replace(/\.[^/.]+$/, ""));
    },
    [subscription, getSizeLimit],
  );
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedTypes(),
    maxFiles: 1,
    multiple: false,
  });

  async function onSubmit() {
    if (!file) return;

    try {
      setUploading(true);

      if (isVideoFile) {
        // Handle video upload
        if (setVideoSubmit) setVideoSubmit(true);

        const formData = new FormData();
        formData.append('file', file);

        handleCancel();

        const result = await fetch("/api/user/media/video", {
          method: "POST",
          body: formData
        });

        if (!result.ok) {
          const data  = await result.json();
          error("Upload Failed", data.message);
          return;
        }

        success("Upload Complete", "Video successfully uploaded.");

        if (setVideoSubmit) setVideoSubmit(false);
      } else if (isImageFile) {
        // Handle image upload

        const formData = new FormData();
        formData.append("file", file);

        handleCancel();

        const response = await fetch("/api/user/media/image", {
          method: "POST",
          body: formData
        });

        const data  = await response.json();
        if (!response.ok) {
          error("Upload Failed", data.message);
          return;
        }

        success("Upload Complete", "Image successfully uploaded.");

        if (onUpload) onUpload([data.image])
      }
    } catch (err) {
      console.error("Upload error:", err);
      error("Upload Error", "Upload failed");
    } finally {
      setUploading(false);
    }
  }

  if (!open) return null;

  const currentUploading = uploading || videoUploading;

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4 font-['Montserrat']">
      <div
        ref={modalRef}
        className="w-full max-w-[600px] bg-[#110018] border border-white/24 rounded-[24px] shadow-[0px_4px_21.7px_0px_rgba(0,0,0,0.25)] overflow-hidden"
      >
        {/* Header */}
        <div className="px-6 pt-6 pb-4 flex items-center justify-between">
          <h2 className="text-white text-2xl font-semibold leading-[160%] font-['Montserrat']">
            Upload Your Files
          </h2>
          <button
            onClick={handleCancel}
            disabled={currentUploading}
            className="text-white/70 hover:text-white transition-colors disabled:opacity-50"
          >
            <X className="h-6 w-6" />
            <span className="sr-only">Close</span>
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          <div className="space-y-[26px]">
            {/* Drop Zone */}
            <div
              {...getRootProps()}
              className={cn(
                "w-full border border-white/12 bg-white/[0.04] rounded-[18px] p-8 md:p-12 flex items-center justify-center cursor-pointer transition-all duration-300",
                isDragActive
                  ? "border-white/30 bg-white/[0.08]"
                  : "hover:border-white/20 hover:bg-white/[0.06]",
              )}
            >
              <input {...getInputProps()} />

              <div className="flex flex-col items-center gap-7 max-w-[498px] w-full">
                <div className="flex flex-col items-center gap-2 text-center">
                  <h3 className="text-white text-xl md:text-2xl font-semibold leading-[160%] font-['Montserrat']">
                    Drag & drop or click to browse
                  </h3>
                  <p className="text-white/70 text-base font-normal leading-[160%] font-['Montserrat'] max-w-[350px]">
                    Supports {getFileTypeLabel()} — up to {getSizeLimit()}
                  </p>
                </div>

                {file ? (
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-16 h-16 rounded-full bg-white/10 border border-white/20 flex items-center justify-center">
                      {isVideoFile ? (
                        <Video className="h-8 w-8 text-white" />
                      ) : (
                        <ImageIcon className="h-8 w-8 text-white" />
                      )}
                    </div>
                    <div className="text-center">
                      <p className="text-white font-medium mb-1">{file.name}</p>
                      <p className="text-white/60 text-sm">
                        Click to change file
                      </p>
                    </div>
                  </div>
                ) : (
                  <Button
                    type="button"
                    className="bg-white/[0.12] border border-white/24 rounded-full px-7 py-[10px] text-white text-lg font-semibold leading-[160%] font-['Montserrat'] hover:bg-white/[0.16] hover:border-white/30 transition-all duration-300"
                  >
                    Select File
                  </Button>
                )}
              </div>
            </div>

            {/* Title Input (only for videos) */}
            {file && isVideoFile && (
              <div className="space-y-2">
                <Label
                  htmlFor="title"
                  className="text-white text-sm font-medium font-['Montserrat']"
                >
                  Video Title (optional)
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter video title or use filename"
                  className="bg-white/[0.04] border border-white/12 rounded-lg px-4 py-3 text-white placeholder:text-white/50 focus:border-white/30 focus:bg-white/[0.06] font-['Montserrat']"
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center gap-4">
              <Button
                onClick={onSubmit}
                disabled={!file || currentUploading}
                className="bg-gradient-to-b from-[#B851E0] to-[#EB489B] hover:from-[#A641D0] hover:to-[#DA3A8B] text-white text-lg font-semibold leading-[160%] font-['Montserrat'] rounded-full px-6 py-[14px] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
              >
                {currentUploading ? "Uploading..." : "Upload Now"}
              </Button>

              <Button
                onClick={handleCancel}
                disabled={currentUploading}
                className="bg-transparent border border-white text-white text-lg font-semibold leading-[160%] font-['Montserrat'] rounded-full px-6 py-[14px] opacity-70 hover:opacity-100 transition-all duration-300 disabled:opacity-30"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}