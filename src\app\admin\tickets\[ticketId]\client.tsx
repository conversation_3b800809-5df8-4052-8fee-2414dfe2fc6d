"use client";

import { format } from "date-fns";
import { useEffect, useRef, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import {
  ArrowLeft,
  MoreVertical,
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle2,
  Check,
  Inbox,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TicketResponseForm } from "../_components/ticket-response-form";
import { TicketStatusActions } from "../_components/ticket-status-actions";
import { AdminTicketAssign } from "../_components/admin-ticket-assign";
import { StatusBadge } from "../_components/status-badge";
import { PriorityBadge } from "../_components/priority-badge";
import { TicketChatTimeline } from "../_components/ticket-chat-timeline";
import { TicketActivityTimelineWrapper } from "../_components/ticket-activity-timeline-wrapper";
import submitAssignTicket from "../_actions/assign-ticket";
import submitUpdateStatus from "../_actions/update-status";
import {
  Message,
  MessageType,
  TicketActivity,
  TicketResponse,
} from "@/types/ticket";
import { useSocket } from "@/components/socket-context";
import { getUserById } from "@/lib/db/user";
import { getUserProfilePicture } from "@/server/profile";

interface AdminTicketDetailClientProps {
  initialTicket: {
    responses: any;
    activities: any;
    id: string;
    title: string;
    content: string;
    status: "OPEN" | "IN_PROGRESS" | "CLOSED" | "RESOLVED";
    priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
    userId: string;
    assignedTo: AdminUser | null;
    createdAt: Date;
    updatedAt: Date;
    user: {
      id: string;
      name: string | null;
      email: string;
      image?: string | null;
    };
  };
  ticketManagers: AdminUser[];
  userId: string;
}

interface AdminUser {
  id: string;
  name: string | null;
  email: string;
  image?: string | null;
  roleId?: number;
}

export default function AdminTicketDetailClient({
  userId,
  initialTicket,
  ticketManagers,
}: AdminTicketDetailClientProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const ticketIdShort = initialTicket.id.slice(0, 8);
  const [ticket, setTicket] = useState(initialTicket);
  const [activeTab, setActiveTab] = useState("discussion");
  const [confirmClose, setConfirmClose] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [timelineOpen, setTimelineOpen] = useState(false);
  const { isConnected, onEvent, sendEvent, offEvent } = useSocket();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const chatContainerRef = useRef<HTMLDivElement | null>(null);

  const handleMessageReceived = useCallback(
    async (data: any) => {
      try {
        const { sender, message, messageId } = data;
        const senderUser = await getUserById(sender);
        if (!senderUser) {
          console.error("Sender user not found");
          return;
        }
        const senderImage = await getUserProfilePicture(senderUser.id);
        if (sender && message && messageId) {
          const newMessage: TicketResponse = {
            id: messageId,
            content: message,
            createdAt: new Date(),
            updatedAt: new Date(),
            ticketId: ticket.id,
            userId: senderUser.id,
            user: {
              id: senderUser.id,
              name: senderUser.name,
              email: senderUser.email,
              image: senderImage,
            },
          };
          const newActivity: TicketActivity = {
            id: crypto.randomUUID(),
            action: "message_received",
            createdAt: new Date(),
            userId: senderUser.id,
            ticketId: ticket.id,
            user: {
              id: senderUser.id,
              name: senderUser.name,
              email: senderUser.email,
              image: senderImage,
            },
          };
          setTicket((prevTicket) => ({
            ...prevTicket,
            responses: [
              ...(prevTicket.responses ?? []),
              newMessage as TicketResponse,
            ],
            activities: [
              ...(prevTicket.activities ?? []),
              newActivity as TicketActivity,
            ],
          }));
          if (senderUser.id !== userId) {
            if (audioRef.current) {
              audioRef.current.play();
            }
          }
        }
      } catch (error) {
        console.error("Error handling message received:", error);
      }
    },
    [ticket, userId],
  );

  useEffect(() => {
    const handleChatEvent = (data: any) => {
      handleMessageReceived(data);
    };
    if (isConnected && ticket) {
      sendEvent(JSON.stringify({
        type: "ticket:join",
        ticketId: ticket.id
      }));
      onEvent("ticket:chat", handleChatEvent);
    }
    return () => {
      if (isConnected && ticket) {
        sendEvent(JSON.stringify({
          type: "ticket:leave",
          ticketId: ticket.id
        }));
        offEvent("ticket:chat", handleChatEvent);
      }
    };
  }, [
    isConnected,
    ticket,
    sendEvent,
    handleMessageReceived,
    offEvent,
    onEvent,
  ]);

  const messages: Message[] = [
    {
      id: "original",
      type: "message" as MessageType,
      content: ticket.content,
      createdAt: ticket.createdAt,
      user: ticket.user,
    },
    {
      id: "streambliss-bot-auto",
      type: "bot" as MessageType,
      content:
        "Hello! 👋 This is an automated message from Streambliss Bot. A support agent will be with you shortly. Thank you for your patience.",
      createdAt: new Date(new Date(ticket.createdAt).getTime() + 1000 * 60 * 2),
      user: {
        id: "streambliss-bot",
        name: "Streambliss Bot",
        email: "<EMAIL>",
        image: null,
        roleId: 0,
        isBot: true,
      },
      icon: "bot",
    },
    ...ticket.responses.map((r) => ({
      id: r.id,
      type: "message" as MessageType,
      content: r.content,
      createdAt: r.createdAt,
      user: r.user,
    })),
    ...ticket.activities
      .filter(
        (a) =>
          a.action.startsWith("STATUS_CHANGED_TO") ||
          a.action === "ASSIGNED" ||
          a.action === "SELF_ASSIGNED" ||
          a.action === "UNASSIGNED",
      )
      .map((a) => ({
        id: a.id,
        type: a.action.startsWith("STATUS_CHANGED_TO")
          ? ("status" as MessageType)
          : ("assign" as MessageType),
        content: a.action.startsWith("STATUS_CHANGED_TO")
          ? `Status changed to ${a.action.replace("STATUS_CHANGED_TO_", "").replace("_", " ").toLowerCase()}`
          : a.action === "ASSIGNED"
            ? "Ticket assigned"
            : a.action === "SELF_ASSIGNED"
              ? "Self-assigned"
              : "Unassigned",
        createdAt: a.createdAt,
        user: a.user,
      })),
  ].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages.length]);

  useEffect(() => {
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const handleAssignTicket = async (assigneeId: string | null) => {
    setIsUpdating(true);
    try {
      const result = await submitAssignTicket({
        ticketId: ticket.id,
        assignedTo: assigneeId,
      });
      if (result.success) {
        success("Assignment Updated", "Ticket assignment updated");
        router.refresh();
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      error("Assignment Failed", "Failed to assign ticket");
      console.error("Error assigning ticket:", err);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleStatusUpdate = async (
    newStatus: "OPEN" | "IN_PROGRESS" | "CLOSED" | "RESOLVED",
  ) => {
    setIsUpdating(true);
    try {
      const result = await submitUpdateStatus({
        ticketId: ticket.id,
        status: newStatus,
      });
      if (result.success) {
        success("Status Updated", "Ticket status updated");
        router.refresh();
        setConfirmClose(false);
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      error("Update Failed", "Failed to update ticket status");
    } finally {
      setIsUpdating(false);
    }
  };

  if (!ticket) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center min-h-[400px]">
        <div className="bg-[#1e1e1e] rounded-full p-3 mb-4">
          <Inbox className="h-8 w-8 text-purple-500" />
        </div>
        <p className="text-lg font-medium mb-2">No ticket found</p>
        <p className="text-sm text-muted-foreground max-w-md mb-4">
          The ticket you&apos;re looking for doesn&apos;t exist or has been
          deleted.
        </p>
        <Link href="/admin/tickets">
          <Button variant="outline" className="bg-[#1e1e1e] border-[#333]">
            <Check className="w-4 h-4 mr-2" /> View All Tickets
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Navigation and Header */}
      <div className="flex items-center gap-2">
        <Link href="/admin/tickets" className="shrink-0">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800/40"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back to tickets</span>
          </Button>
        </Link>
        <span className="text-sm text-gray-400">Back to tickets</span>
      </div>

      {/* Ticket Header */}
      <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-sm text-gray-400 font-mono bg-gray-800/60 px-2 py-1 rounded">
                #{ticketIdShort}
              </span>
              <StatusBadge status={ticket.status} className="shrink-0" />
              <PriorityBadge priority={ticket.priority} className="shrink-0" />
            </div>
            <h1 className="text-xl font-bold text-white truncate">
              {ticket.title}
            </h1>
            <div className="mt-2 text-sm text-gray-400 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>
                Created{" "}
                {format(new Date(ticket.createdAt), "MMM d, yyyy 'at' HH:mm")}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40 hover:text-white"
                >
                  <MoreVertical className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="bg-gray-950 border-gray-800/40"
              >
                {ticket.status !== "CLOSED" && (
                  <>
                    <DropdownMenuItem
                      onClick={() => setConfirmClose(true)}
                      className="text-red-400 hover:bg-gray-800/40 cursor-pointer"
                    >
                      <AlertCircle className="w-4 h-4 mr-2" />
                      Close Ticket
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-gray-800/40" />
                  </>
                )}
                <DropdownMenuItem
                  onClick={() => handleStatusUpdate("RESOLVED")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                  Mark as Resolved
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleStatusUpdate("IN_PROGRESS")}
                  className="text-gray-300 hover:bg-gray-800/40 cursor-pointer"
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Mark In Progress
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden sm:grid sm:grid-cols-[2fr_1fr] gap-4 items-start">
        <div className="space-y-4">
          {/* Chat Section */}
          <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
            <h2 className="text-lg font-semibold mb-3 text-white">
              Conversation
            </h2>
            <audio ref={audioRef} src="/notification.mp3" />
            <div
              ref={chatContainerRef}
              className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden max-h-[400px] overflow-y-auto"
            >
              {messages.length === 0 ? (
                <div className="p-4 text-center text-gray-400">
                  No messages yet.
                </div>
              ) : (
                <TicketChatTimeline
                  messages={messages}
                  currentUserId={ticket.userId}
                  renderBubbleClass={(msg) =>
                    msg.type === "bot"
                      ? "bg-purple-500/10 text-white border-0"
                      : msg.user.id === ticket.userId
                        ? "bg-purple-500/10 text-white border-0"
                        : "bg-gray-800/50 text-white border-0"
                  }
                  renderContainerClass={(msg) =>
                    msg.user.id === ticket.userId
                      ? "flex flex-row items-start gap-3 justify-end"
                      : "flex flex-row items-start gap-3 justify-start"
                  }
                />
              )}
            </div>
          </div>

          {/* Response Form */}
          {ticket.status !== "CLOSED" ? (
            <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
              <h2 className="text-lg font-semibold mb-3 text-white">
                Add Response
              </h2>
              <TicketResponseForm userId={userId} ticketId={ticket.id} />
            </div>
          ) : (
            <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-3 flex items-center gap-3">
              <AlertCircle className="h-4 w-4 text-amber-400" />
              <p className="text-sm text-amber-400">
                This ticket is closed. No further responses can be added.
              </p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sticky top-4">
          <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl overflow-hidden">
            <div className="p-4 border-b border-gray-800/40">
              <h3 className="font-medium text-white">Ticket Details</h3>
            </div>
            <div className="p-4">
              <dl className="space-y-4">
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Status</dt>
                  <dd>
                    <StatusBadge status={ticket.status} />
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Priority</dt>
                  <dd>
                    <PriorityBadge priority={ticket.priority} />
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Created</dt>
                  <dd className="text-white text-sm">
                    {format(new Date(ticket.createdAt), "MMM d, yyyy - HH:mm")}
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Ticket ID</dt>
                  <dd className="font-mono text-xs text-gray-300 bg-gray-800/60 px-3 py-2 rounded break-all">
                    {ticket.id}
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Submitted by</dt>
                  <dd className="flex items-center gap-2">
                    <Avatar className="h-5 w-5 ring-1 ring-gray-700">
                      {ticket.user.image ? (
                        <AvatarImage
                          src={ticket.user.image}
                          alt={ticket.user.name || "User"}
                        />
                      ) : (
                        <AvatarFallback className="text-xs bg-purple-500/20 text-purple-400">
                          {ticket.user.name?.[0] || ticket.user.email[0]}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className="text-white text-sm truncate">
                      {ticket.user.name || ticket.user.email}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-2 text-sm">Assigned to</dt>
                  <dd>
                    {ticket.assignedTo ? (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-5 w-5 ring-1 ring-gray-700">
                          {ticket.assignedTo.image ? (
                            <AvatarImage
                              src={ticket.assignedTo.image}
                              alt={ticket.assignedTo.name || "Agent"}
                            />
                          ) : (
                            <AvatarFallback className="text-xs bg-purple-500/20 text-purple-400">
                              {ticket.assignedTo.name?.[0]}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <span className="text-white text-sm truncate">
                          {ticket.assignedTo.name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">Unassigned</span>
                    )}
                  </dd>
                </div>
              </dl>

              <div className="pt-6 space-y-3">
                <h4 className="font-medium text-white">Admin Actions</h4>
                <div className="space-y-2">
                  <div>
                    <label className="text-gray-400 text-sm mb-1 block">
                      Assigned to
                    </label>
                    <AdminTicketAssign
                      ticketId={ticket.id}
                      admins={ticketManagers}
                      currentAssignee={ticket.assignedTo}
                      onAssign={handleAssignTicket}
                      disabled={isUpdating}
                    />
                  </div>
                  <TicketStatusActions
                    ticketId={ticket.id}
                    currentStatus={ticket.status}
                  />
                  {ticket.status !== "CLOSED" && (
                    <Button
                      variant="destructive"
                      size="sm"
                      className="w-full h-9 bg-red-600 hover:bg-red-700 text-white border-0"
                      onClick={() => setConfirmClose(true)}
                    >
                      Close Ticket
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full h-9 bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
                    onClick={() => setTimelineOpen(true)}
                  >
                    Show Timeline
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="sm:hidden space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-gray-900/30 border border-gray-800/40 rounded-xl p-1 w-full">
            <TabsTrigger
              value="discussion"
              className="flex-1 data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Discussion
            </TabsTrigger>
            <TabsTrigger
              value="details"
              className="flex-1 data-[state=active]:bg-gray-700/60 data-[state=active]:text-white text-gray-400 rounded-lg px-4 py-2 hover:bg-gray-800/40 hover:text-gray-200 cursor-pointer transition-colors"
            >
              <Clock className="mr-2 h-4 w-4" />
              Details
            </TabsTrigger>
          </TabsList>

          <TabsContent value="discussion" className="mt-4">
            <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4">
              <div className="bg-gray-900/30 border border-gray-800/40 rounded-xl overflow-hidden max-h-[500px] overflow-y-auto">
                <TicketChatTimeline
                  messages={messages}
                  currentUserId={ticket.userId}
                  renderBubbleClass={(msg) =>
                    msg.type === "bot"
                      ? "bg-purple-500/10 text-white border border-purple-500/20"
                      : msg.user.id === ticket.userId
                        ? "bg-purple-500/10 text-white"
                        : "bg-gray-800/50 text-white"
                  }
                  renderContainerClass={(msg) =>
                    msg.user.id === ticket.userId
                      ? "flex flex-row items-start gap-3 justify-start"
                      : "flex flex-row items-start gap-3 justify-start"
                  }
                />
              </div>

              {ticket.status !== "CLOSED" ? (
                <div className="mt-4">
                  <TicketResponseForm userId={userId} ticketId={ticket.id} />
                </div>
              ) : (
                <div className="bg-amber-500/10 border border-amber-500/30 rounded-xl p-4 flex items-center gap-3 mt-4">
                  <AlertCircle className="h-5 w-5 text-amber-400" />
                  <p className="text-sm text-amber-400">
                    This ticket is closed. No further responses can be added.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="details" className="mt-4">
            <div className="bg-gray-950/50 border border-gray-800/40 rounded-xl p-4 space-y-4">
              <dl className="space-y-4">
                <div>
                  <dt className="text-gray-400 mb-1.5">Priority</dt>
                  <dd>
                    <PriorityBadge priority={ticket.priority} />
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-1.5">Status</dt>
                  <dd>
                    <StatusBadge status={ticket.status} />
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-1.5">Created At</dt>
                  <dd className="text-white">
                    {format(new Date(ticket.createdAt), "PPP p")}
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-1.5">Last Updated</dt>
                  <dd className="text-white">
                    {format(new Date(ticket.updatedAt), "PPP p")}
                  </dd>
                </div>
                <div>
                  <dt className="text-gray-400 mb-1.5">Support Agent</dt>
                  <dd>
                    {ticket.assignedTo ? (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-5 w-5 ring-2 ring-gray-700">
                          {ticket.assignedTo.image ? (
                            <AvatarImage
                              src={ticket.assignedTo.image}
                              alt={ticket.assignedTo.name || "Agent"}
                            />
                          ) : (
                            <AvatarFallback className="text-xs bg-purple-500/20 text-purple-400">
                              {ticket.assignedTo.name?.[0]}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <span className="text-white">
                          {ticket.assignedTo.name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400">Unassigned</span>
                    )}
                  </dd>
                </div>
              </dl>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Dialog open={confirmClose} onOpenChange={setConfirmClose}>
        <DialogContent className="bg-gray-950 border-gray-800/40">
          <DialogHeader>
            <DialogTitle className="text-white">
              Confirm Close Ticket
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              Are you sure you want to close this ticket? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmClose(false)}
              disabled={isUpdating}
              className="bg-gray-900/30 border-gray-800/40 text-gray-300 hover:bg-gray-800/40"
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleStatusUpdate("CLOSED")}
              disabled={isUpdating}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Closing...
                </>
              ) : (
                "Close Ticket"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={timelineOpen} onOpenChange={setTimelineOpen}>
        <DialogContent className="max-w-2xl bg-gray-950 border-gray-800/40">
          <DialogHeader>
            <DialogTitle className="text-white">Activity Timeline</DialogTitle>
          </DialogHeader>
          <div className="p-2 max-h-[70vh] overflow-y-auto bg-gray-900/30 border border-gray-800/40 rounded-xl">
            <TicketActivityTimelineWrapper
              activities={ticket.activities}
              responses={[]}
              limit={0}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}