"use server";

import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { addMaintenanceUpdate } from "@/server/maintenance";
import {LogActions, MaintenanceTypes} from "@prisma/client";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {getUserSession} from "@/server/session";

type Callback = {
  success: boolean;
  message: string;
};

type Props = {
  message: string;
  type: MaintenanceTypes;
};

export async function submitUpdateMaintenance({
  message,
  type,
}: Props): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later.",
    };
  }

  const user = await getUserSession();
  if (!user || !user.userId) {
    return {
      success: false,
      message: "User not found",
    }
  }

  if (!message || !type) {
    return {
      success: false,
      message: "Please provide a valid message and type.",
    };
  }

  const maintenanceMessage = message.trim();
  await addMaintenanceUpdate(maintenanceMessage, type);
  await createLog(user.userId, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_MAINTENANCE_UPDATED, LogActions.ACCOUNT);

  return {
    success: true,
    message: "Maintenance mode updated successfully.",
  };
}