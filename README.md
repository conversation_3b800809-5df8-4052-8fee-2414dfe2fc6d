# Streambliss Project


# TODO:
[-] Packages: add limits (Upload size and so on)  (needs testing)
[] Impressum datenschutz
[] For Production remove any console logs
[] After Upload ask the user if he wants to the video on the community page
[] Fix any Site with responsive issues
[] Redesign Email template
[] Finish Community and Profile Page
[] Add under Dashboard Profile: add social links
[] Admin permissions: Only <PERSON><PERSON> can check user Profile and see all uploads
[] Fix Shortlink for Images
[] Add Google login (need to make an google account with the noreply email - no need for discord)
