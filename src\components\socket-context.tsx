"use client";

import {createContext, useContext, useEffect, useRef, useState} from "react";

type SocketContextType = {
    socket: WebSocket | null;
    sendEvent: (data: string) => void;
    onEvent: (event: string, callback: (data: any) => void) => void;
    offEvent: (event: string, callback: (data: any) => void) => void;
    isConnected: boolean;
};

const SocketContext = createContext<SocketContextType | null>(null);

export const SocketProvider = ({
                                   userId,
                                   children,
                                   userToken
                               }: {
    userId: string;
    children: React.ReactNode;
    userToken: string;
}) => {
    const [socket, setSocket] = useState<WebSocket | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const eventListeners = useRef(new Map<string, Set<(data: any) => void>>());

    useEffect(() => {
        const ws = new WebSocket("wss://dev-socket.streambliss.cloud?token=" + userToken);
        ws.onopen = () => {
            console.log("WebSocket connected, sending register...");
            setIsConnected(true);
        };

        ws.onclose = () => {
            console.log("WebSocket disconnected");
            setIsConnected(false);
        };

        ws.onmessage = (message) => {
            try {
                const parsed = JSON.parse(message.data);
                const {type} = parsed;

                const listeners = eventListeners.current.get(type);
                if (listeners) {
                    listeners.forEach((callback) => callback(message.data));
                }
            } catch (err) {
                console.error("Failed to parse WebSocket message:", err);
            }
        };

        setSocket(ws);

        return () => {
            ws.close();
        };
    }, [userId]);

    const sendEvent = (data: string) => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            console.log('Sending Event: ' + data);
            socket.send(data);
        } else {
            console.error("WebSocket is not connected. Cannot send event:", data);
        }
    };

    const onEvent = (event: string, callback: (data: any) => void) => {
        if (!eventListeners.current.has(event)) {
            eventListeners.current.set(event, new Set());
            console.log("Registered event: " + event);
        }
        eventListeners.current.get(event)!.add(callback);
    };

    const offEvent = (event: string, callback: (data: any) => void) => {
        const listeners = eventListeners.current.get(event);
        if (listeners) {
            listeners.delete(callback);
            if (listeners.size === 0) {
                eventListeners.current.delete(event);
            }
        }
    }

    return (
        <SocketContext.Provider
            value={{socket, sendEvent, onEvent, offEvent, isConnected}}
        >
            {children}
        </SocketContext.Provider>
    );
};

export const useSocket = () => {
    const context = useContext(SocketContext);
    if (!context) {
        throw new Error("useSocket must be used within a SocketProvider");
    }
    return context;
};