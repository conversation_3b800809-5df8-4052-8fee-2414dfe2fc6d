"use server";

import { prisma } from "@/lib/prisma";
import { getUserSession } from "@/server/session";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export async function completeTutorial(userId: string) {
  const session = await getUserSession();

  if (!session?.userId || session.userId !== userId) {
    throw new Error("Unauthorized");
  }

  try {
    await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        hasSeenTutorial: true,
      },
    });

    revalidatePath("/dashboard");
    return { success: true };
  } catch (error) {
    console.error("Error completing tutorial:", error);
    throw new Error("Failed to complete tutorial");
  }
}