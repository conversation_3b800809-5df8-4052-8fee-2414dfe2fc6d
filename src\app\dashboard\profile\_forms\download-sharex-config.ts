"use server";

import { decryptData } from "@/lib/crypter";
import { getUserById } from "@/lib/db/user";
import { rateLimiter } from "@/lib/rate-limit";
import { getClientIp } from "@/server/geolocation";
import { getUserSession } from "@/server/session";
import { getUserSettings } from "@/server/user-settings";

type Callback = {
  success: boolean;
  message: string;
  config?: string;
}

type ShareXConfig = {
  Version: string;
  Name: string;
  DestinationType: string;
  RequestMethod: string;
  RequestURL: string;
  Headers: {
    "Auth-Token": string;
  };
  Body: string;
  Arguments: {
    "userId": string
  };
  FileFormName: string;
}

export default async function submitDownloadShareXConfig(): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "Rate limit exceeded. Please try again later.",
    };
  }

  try {
    const userSession = await getUserSession();
    if (!userSession) {
      return {
        success: false,
        message: "User session not found",
      };
    }

    const userId = userSession.userId;
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        message: "User not found",
      };
    }

    const userSettings = await getUserSettings(userId);
    if (!userSettings) {
      return {
        success: false,
        message: "User settings not found",
      };
    }

    const tokenData = decryptData(userSettings.secretToken, userSettings.secretTokenIv);
    if (!tokenData) {
      return {
        success: false,
        message: "Failed to decrypt user settings",
      };
    }

    const shareXConfig: ShareXConfig = {
      Version: "14.0.0",
      Name: "Streambliss",
      DestinationType: "ImageUploader",
      RequestMethod: "POST",
      RequestURL: `https://api.streambliss.cloud/image/upload`,
      Headers: {
        "Auth-Token": tokenData,
      },
      Body: "MultipartFormData",
      Arguments: {
        userId: userId,
      },
      FileFormName: "file",
    };

    return {
      success: true,
      message: "ShareX config downloaded successfully",
      config: JSON.stringify(shareXConfig, null, 2),
    }
  } catch (error) {
    return {
      success: false,
      message: "Failed to download ShareX config",
    };
  }
}
