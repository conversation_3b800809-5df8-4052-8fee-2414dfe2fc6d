"use server"

import { getUserSession } from "@/server/session"
import { prisma } from "@/lib/prisma"
import { getVideoOwner } from "@/server/video"
import { getMaintenance } from "@/server/maintenance"
import { getClientIp } from "@/server/geolocation"
import { rateLimiter } from "@/lib/rate-limit"
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

interface ReportData {
  videoId: string
  reason: string
  details?: string
}

type Callback = {
  success: boolean;
  message: string;
}

const reportReasons = ["inappropriate", "copyright", "spam", "other"];

export async function submitReport({ videoId, reason, details }: ReportData): Promise<Callback> {
  const ip = await getClientIp();
  if (rateLimiter(ip)) {
    return {
      success: false,
      message: "You have exceeded the rate limit. Please try again later."
    }
  }

  const session = await getUserSession();
  if (!session.userId) {
    return { success: false, message: "You must be logged in to report a video" }
  }
  
  const videoOwner = await getVideoOwner(videoId);
  if (videoOwner == null) {
    return { success: false, message: "Video not found" }
  }

  if (videoOwner === session.userId) {
    return { success: false, message: "You cannot report your own video" }
  }

  if (!reportReasons.includes(reason)) {
    return { success: false, message: "Invalid reason" }
  }

  await prisma.reports.create({
    data: {
      reportedUserId: videoOwner,
      reportVideoId: videoId,
      reportReason: reason,
      details,
      userId: session.userId,
      reportCase: {
        create: {
          status: "OPEN"
        }
      }
    }
  });
  await createLog(session.userId, LogConstants.REPORTED_VIDEO, LogActions.VIDEO);

  return { success: true, message: "Video has been successfully reported!" }
}