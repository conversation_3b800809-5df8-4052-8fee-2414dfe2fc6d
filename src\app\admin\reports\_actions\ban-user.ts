"use server";

import {getUserById} from "@/lib/db/user";
import {prisma} from "@/lib/prisma";
import {rateLimiter} from "@/lib/rate-limit";
import {hasPermission} from "@/server/admin";
import {getClientIp} from "@/server/geolocation";
import {createNotification} from "@/server/notifications";
import {getUser} from "@/server/session";
import {createLog} from "@/server/logs";
import {LogConstants} from "@/server/log-constants";
import {LogActions} from "@prisma/client";

type Callback = {
    success: boolean;
    message: string;
}

type Params = {
    userId: string;
    reason: string;
};

export default async function submitBanUser({userId, reason}: Params): Promise<Callback> {
    const ip = await getClientIp();
    if (rateLimiter(ip)) {
        return {
            success: false,
            message: "You have exceeded the rate limit. Please try again later."
        }
    }

    const userSession = await getUser();
    if (!userSession) {
        return {success: false, message: "No user session found"};
    }

    const adminUser = await getUserById(userSession.id);
    if (!adminUser) {
        return {success: false, message: "No admin user found"};
    }

    if (!userId) {
        return {success: false, message: "User ID is required"};
    }

    if (!reason) {
        return {success: false, message: "Reason is required"};
    }

    const user = await hasPermission(adminUser.roleId, ["ADMIN_BAN_USER"]);
    if (!user || user == null) {
        return {success: false, message: "No permission for this action."};
    }

    const bannedUser = await getUserById(userId);
    if (!bannedUser || bannedUser == null) {
        return {success: false, message: "User not found"};
    }

    await prisma.user.update({
        where: {id: bannedUser.id},
        data: {
            banReason: {
                create: {
                    reason: reason,
                }
            }
        }
    });

    await createNotification(bannedUser.id, "You have been banned: " + reason, "WARNING");
    await createLog(adminUser.id, LogConstants.ADMIN_ACTION_PREFIX + LogConstants.ADMIN_USER_BAN, LogActions.ACCOUNT, "Banned: " + bannedUser.name);

    return {success: true, message: "User has been banned"};
}
