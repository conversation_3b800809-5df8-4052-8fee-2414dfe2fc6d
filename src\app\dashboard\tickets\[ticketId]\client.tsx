"use client";

import { format } from "date-fns";
import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useEnhancedToast } from "@/components/ui/enhanced-toast";
import { ArrowLeft, Send, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { TicketActivityTimelineWrapper } from "../../../admin/tickets/_components/ticket-activity-timeline-wrapper";
import closeTicket from "../_actions/close-ticket";
import type {
  Ticket,
  TicketResponse,
  TicketActivity,
} from "../../../../types/ticket";
import { MessageType } from "@/types/ticket";
import { useSocket } from "@/components/socket-context";
import { getUserById } from "@/lib/db/user";
import { getUserProfilePicture } from "@/server/profile";
import { DashboardWrapper } from "../../_components/dashboard-wrapper";
import { DashboardHeader } from "../../_components/dashboard-header";

import { $Enums } from "@prisma/client";
import EmojiPicker from "emoji-picker-react";

interface TimelineUser {
  id: string;
  name: string | null;
  email: string;
  image?: string | null;
  roleId?: number;
}

interface User {
  id: string;
  name?: string | null;
  email: string;
  image?: string | null;
  roleId?: number;
  isBot?: boolean;
}

interface DashboardTicketDetailClientProps {
  ticket: Ticket & {
    responses: TicketResponse[];
    activities: TicketActivity[];
    user: User;
    assignedTo?: User | null;
  };
  userId: string;
  userName?: string;
  userImage?: string | null;
  notifications?: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    type: $Enums.NotificationType;
    data: string;
    read: boolean;
  }[];
  hasAccessToAdmin?: boolean;
}

export default function DashboardTicketDetailClient({
  ticket: initialTicket,
  userId,
  userName = "",
  userImage,
  notifications = [],
  hasAccessToAdmin = false,
}: DashboardTicketDetailClientProps) {
  const router = useRouter();
  const { success, error } = useEnhancedToast();
  const ticketIdShort = initialTicket.id.slice(0, 7);
  const [confirmClose, setConfirmClose] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [timelineOpen, setTimelineOpen] = useState(false);
  const [ticket, setTicket] = useState(initialTicket);
  const [messageText, setMessageText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { isConnected, onEvent, sendEvent, offEvent } = useSocket();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const chatEndRef = useRef<HTMLDivElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);

  const handleMessageReceived = useCallback(
    async (data: any) => {
      try {
        const { sender, message, messageId } = data;
        const senderUser = await getUserById(sender);
        if (!senderUser) {
          console.error("Sender user not found");
          return;
        }
        const senderImage = await getUserProfilePicture(senderUser.id);
        if (sender && message && messageId) {
          const newMessage: TicketResponse = {
            id: messageId,
            content: message,
            createdAt: new Date(),
            updatedAt: new Date(),
            ticketId: ticket.id,
            userId: senderUser.id,
            user: {
              id: senderUser.id,
              name: senderUser.name,
              email: senderUser.email,
              image: senderImage,
            },
          };
          const newActivity: TicketActivity = {
            id: crypto.randomUUID(),
            action: "message_received",
            createdAt: new Date(),
            userId: senderUser.id,
            ticketId: ticket.id,
            user: {
              id: senderUser.id,
              name: senderUser.name,
              email: senderUser.email,
              image: senderImage,
            },
          };
          setTicket((prevTicket) => ({
            ...prevTicket,
            responses: [
              ...(prevTicket.responses ?? []),
              newMessage as TicketResponse,
            ],
            activities: [
              ...(prevTicket.activities ?? []),
              newActivity as TicketActivity,
            ],
          }));
          if (senderUser.id !== userId) {
            if (audioRef.current) {
              audioRef.current.play();
            }
          }
        }
      } catch (error) {
        console.error("Error handling message received:", error);
      }
    },
    [ticket, userId],
  );

  useEffect(() => {
    const handleChatEvent = (data: any) => {
      handleMessageReceived(data);
    };

    if (isConnected && ticket) {
      sendEvent(JSON.stringify({
        type: "ticket:join",
        ticketId: ticket.id
      }));
      onEvent("ticket:chat", handleChatEvent);
    }

    return () => {
      if (isConnected && ticket) {
        sendEvent(JSON.stringify({
          type: "ticket:leave",
          ticketId: ticket.id
        }));
        offEvent("ticket:chat", handleChatEvent);
      }
    };
  }, [
    isConnected,
    ticket,
    sendEvent,
    handleMessageReceived,
    offEvent,
    onEvent,
  ]);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [ticket.responses]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showEmojiPicker]);

  const handleCloseTicket = async () => {
    try {
      setIsClosing(true);
      const result = await closeTicket(ticket.id);

      if (result.success) {
        success("Ticket Closed", result.message || "Ticket closed successfully");
        router.refresh();
      } else {
        throw new Error(result.message || "Failed to close ticket");
      }
    } catch (err) {
      error("Close Failed", err instanceof Error ? err.message : "Failed to close ticket");
    } finally {
      setIsClosing(false);
      setConfirmClose(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/tickets/${ticket.id}/responses`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: messageText.trim() }),
      });

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      const responseData = await response.json();
      setMessageText("");

      // Send socket event for real-time updates
      if (isConnected) {
        sendEvent(JSON.stringify({
          type: "ticket:chat",
          ticketId: ticket.id,
          messageData: {
            sender: userId,
            message: messageText.trim(),
            messageId: responseData.id,
          },
        }));
      }

      router.refresh();
    } catch (err) {
      error("Send Failed", "Failed to send message. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiClick = (emojiData: any) => {
    setMessageText((prev) => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  const messages = useMemo(() => {
    return [
      {
        id: "original",
        type: "message" as MessageType,
        content: ticket.content,
        createdAt: new Date(ticket.createdAt),
        user: ticket.user,
      },
      ...(ticket.responses.length === 0
        ? [
            {
              id: "streambliss-bot-auto",
              type: "bot" as MessageType,
              content:
                "Hello! 👋 I'm the StreamBliss bot. I've received your ticket and our support team has been notified. An agent will be with you shortly to assist with your issue. Thank you for your patience!",
              createdAt: new Date(new Date(ticket.createdAt).getTime() + 1000),
              user: {
                id: "streambliss-bot",
                name: "StreamBliss ChatBot",
                email: "<EMAIL>",
                image: null,
                roleId: 0,
                isBot: true,
              },
              icon: "bot",
            },
          ]
        : []),
      ...ticket.responses.map((r) => ({
        id: r.id,
        type: "message" as MessageType,
        content: r.content,
        createdAt: new Date(r.createdAt),
        user: r.user as User,
      })),
      ...ticket.activities
        .filter(
          (a) =>
            a.action.startsWith("STATUS_CHANGED_TO") ||
            a.action === "ASSIGNED" ||
            a.action === "SELF_ASSIGNED" ||
            a.action === "UNASSIGNED",
        )
        .map((a) => ({
          id: a.id,
          type: a.action.startsWith("STATUS_CHANGED_TO")
            ? ("status" as MessageType)
            : ("assign" as MessageType),
          content: a.action.startsWith("STATUS_CHANGED_TO")
            ? `Status changed to ${a.action.replace("STATUS_CHANGED_TO_", "").replace("_", " ").toLowerCase()}`
            : a.action === "ASSIGNED" || a.action === "SELF_ASSIGNED"
              ? `${a.user?.name || "Support Agent"} has been assigned to your ticket`
              : "Support agent has been unassigned",
          createdAt: new Date(a.createdAt),
          user: {
            id: a.user?.id || "system",
            name: a.user?.name || "System",
            email: a.user?.email || "<EMAIL>",
            image: null,
            roleId: 0,
            isBot: false,
          },
        })),
    ].sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );
  }, [ticket]);

  return (
    <DashboardWrapper
      userName={userName}
      userImage={userImage}
      notifications={notifications}
      className="font-['Montserrat']"
    >
      <div className="mb-4">
        <Link href="/dashboard/tickets">
          <Button
            variant="ghost"
            size="sm"
            className="text-white/70 hover:text-white hover:bg-white/5 mb-2"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Tickets
          </Button>
        </Link>

        <DashboardHeader
          userName={userName}
          userImage={userImage}
          notifications={notifications}
          hasAccessToAdmin={hasAccessToAdmin}
          title={`Support Chat - ${ticket.title}`}
          description={`Ticket #${ticketIdShort}`}
        />
      </div>

      {/* Content Area */}
      <div className="flex flex-col lg:flex-row gap-4 overflow-hidden">
        {/* Chat Section */}
        <div className="flex-1 bg-[#110018] border border-white/12 rounded-2xl flex flex-col overflow-hidden max-h-[calc(100vh-200px)]">
          {/* Chat Header */}
          <div className="p-4 border-b border-white/12 flex-shrink-0">
            <div className="flex items-center gap-3">
              <Avatar className="w-10 h-10 ring-2 ring-white/5">
                <AvatarImage src="/assets/images/svg/logo.svg" />
                <AvatarFallback className="bg-[#B851E0]/20">SB</AvatarFallback>
              </Avatar>
              <div>
                <div className="text-white font-medium text-lg leading-tight">
                  <span className="font-montserrat">Chat</span>
                </div>
                <div className="text-[#12C527] text-xs">Online</div>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 space-y-6 overflow-y-auto">
            {messages.map((message, index) => (
              <div key={message.id}>
                {message.user.id === userId || message.user.isBot ? (
                  // User or Bot messages (right aligned for user, left for bot)
                  <div
                    className={`flex ${message.user.id === userId ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-md px-4 py-3 rounded-[25px] text-base ${
                        message.user.id === userId
                          ? "bg-[#B851E0] text-white rounded-br-none"
                          : "bg-[#EEEEEE] text-black/70 rounded-bl-none"
                      }`}
                    >
                      {message.content}
                    </div>
                  </div>
                ) : (
                  // Support agent messages (left aligned with avatar)
                  <div className="flex items-end gap-4">
                    <Avatar className="w-12 h-12 ring-2 ring-white/5 flex-shrink-0">
                      <AvatarImage
                        src={
                          message.user.image ||
                          "https://cdn.builder.io/api/v1/image/assets/TEMP/546e07049e27e9bab000531ca533cd9d496f4c4c?placeholderIfAbsent=true"
                        }
                      />
                      <AvatarFallback className="bg-[#B851E0]/20 text-white">
                        {message.user.name?.[0] || "S"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="max-w-md px-4 py-3 bg-[#EEEEEE] text-black/70 rounded-[25px] rounded-bl-none text-base">
                      {message.content}
                    </div>
                  </div>
                )}
              </div>
            ))}
            <div ref={chatEndRef} />
          </div>

          {/* Message Input */}
          {ticket.status !== "CLOSED" && (
            <div className="p-4 flex-shrink-0">
              <div className="relative bg-white/5 border border-white/12 rounded-xl p-4 flex items-center gap-3">
                <Input
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Write your message"
                  className="flex-1 bg-transparent border-none text-white text-lg placeholder:text-white/40 focus:ring-0 focus:outline-none focus:border-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0"
                  disabled={isSubmitting}
                />
                <div className="relative" ref={emojiPickerRef}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/10 p-2 h-8 w-8 text-sm"
                    title="Add emoji"
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  >
                    😊
                  </Button>
                  {showEmojiPicker && (
                    <div className="absolute bottom-full right-0 mb-2 z-50">
                      <div className="bg-white border border-gray-200 rounded-lg shadow-2xl">
                        <EmojiPicker
                          onEmojiClick={handleEmojiClick}
                          width={280}
                          height={350}
                          theme={"dark" as any}
                          lazyLoadEmojis={true}
                          previewConfig={{
                            showPreview: false,
                          }}
                          skinTonesDisabled={true}
                          searchDisabled={false}
                          emojiStyle={"native" as any}
                        />
                      </div>
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageText.trim() || isSubmitting}
                  size="icon"
                  className="bg-gradient-to-r from-[#B851E0] to-[#EB489B] hover:from-[#A347D1] hover:to-[#D6427A] rounded-lg w-10 h-7 flex-shrink-0 border-0"
                >
                  {isSubmitting ? (
                    <Loader2 className="w-4 h-4 animate-spin text-white" />
                  ) : (
                    <Send className="w-4 h-4 text-white" />
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Ticket Details Panel */}
        <div className="w-full lg:w-72 bg-[#110018] border border-white/12 rounded-2xl flex flex-col overflow-hidden max-h-[calc(100vh-200px)]">
          {/* Header */}
          <div className="p-4 border-b border-white/12 flex-shrink-0">
            <h3 className="text-white font-semibold text-lg">Ticket Details</h3>
          </div>

          {/* Details */}
          <div className="p-4 space-y-4 flex-1 overflow-y-auto">
            <div>
              <h4 className="text-white text-lg font-medium mb-2">Status</h4>
              <div className="text-white/80 text-base">
                {ticket.status === "IN_PROGRESS"
                  ? "In progress"
                  : ticket.status.toLowerCase()}
              </div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <h4 className="text-white text-lg font-medium mb-2">Title</h4>
              <div className="text-white/80 text-base">{ticket.title}</div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <h4 className="text-white text-lg font-medium mb-2">Created</h4>
              <div className="text-white/80 text-base">
                {format(new Date(ticket.createdAt), "MMM d, yyyy - h:mm a")}
              </div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <h4 className="text-white text-lg font-medium mb-2">Email ID</h4>
              <div className="text-white/80 text-base break-all">
                {ticket.user.email}
              </div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <h4 className="text-white text-lg font-medium mb-2">Ticket ID</h4>
              <div className="text-white/80 text-base">#{ticketIdShort}</div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <h4 className="text-white text-lg font-medium mb-2">
                Support Agent
              </h4>
              <div className="text-white/80 text-base">
                {ticket.assignedTo?.name || "Unassigned"}
              </div>
            </div>

            <div className="border-t border-white/20 pt-4">
              <Button
                onClick={() => setTimelineOpen(true)}
                className="w-full bg-white/8 border border-white/24 hover:bg-white/12 text-white font-semibold text-base py-3 rounded-xl"
                variant="outline"
              >
                Show Timeline
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Audio for notifications */}
      <audio ref={audioRef} src="/notification.mp3" />

      {/* Timeline Dialog */}
      <Dialog open={timelineOpen} onOpenChange={setTimelineOpen}>
        <DialogContent className="max-w-2xl bg-[#110018] border border-white/20">
          <DialogHeader>
            <DialogTitle className="text-white">Activity Timeline</DialogTitle>
          </DialogHeader>
          <div className="p-2 max-h-[70vh] overflow-y-auto">
            <TicketActivityTimelineWrapper
              activities={ticket.activities.map((a) => ({
                id: a.id,
                action: a.action,
                createdAt: new Date(a.createdAt).toISOString(),
                user: {
                  id: a.user?.id || "system",
                  name: a.user?.name || "System",
                  email: a.user?.email || "<EMAIL>",
                  image: a.user?.image || null,
                },
                content: undefined,
              }))}
              responses={[]}
              limit={0}
            />
          </div>
        </DialogContent>
      </Dialog>
    </DashboardWrapper>
  );
}