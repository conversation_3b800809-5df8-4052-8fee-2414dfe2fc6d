"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nst<PERSON><PERSON>, FaTwitch, FaYoutube, FaGlobe, FaUser } from "react-icons/fa"
import Image from "next/image"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { useState } from "react"
import { Star, Calendar, Video } from "lucide-react"

type UserProfileHeaderProps = {
  user: {
    id: string
    name: string | null
    image: string
    website?: string | null
    twitter?: string | null
    instagram?: string | null
    twitch?: string | null
    youtube?: string | null
    createdAt: Date
    videosCount: number
  }
}

export function UserProfileHeader({ user }: UserProfileHeaderProps) {
  const [imageError, setImageError] = useState(false)
  const joinedDate = formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })
  const hasSocialLinks = user.website || user.twitter || user.instagram || user.twitch || user.youtube

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-slate-950 via-purple-950/30 to-slate-950">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-2xl animate-float"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-12">
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
            <div className="relative w-48 h-48 md:w-56 md:h-56 rounded-full overflow-hidden bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center shadow-2xl">
              {user.image && !imageError ? (
                <Image
                  src={user.image || "/placeholder.svg"}
                  alt={user.name || "User"}
                  fill
                  className="object-cover"
                  priority
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  {user.name ? (
                    <span className="text-white text-8xl font-bold">{user.name.charAt(0).toUpperCase()}</span>
                  ) : (
                    <FaUser className="text-white text-6xl" />
                  )}
                </div>
              )}
            </div>

            <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full p-3 shadow-lg border-4 border-slate-950">
              <Star className="w-6 h-6 text-white" />
            </div>
          </div>

          <div className="flex-1 text-center md:text-left">
            <h1 className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent leading-tight">
              {user.name}
            </h1>

            <div className="flex flex-wrap items-center justify-center md:justify-start gap-6 mb-8 text-lg">
              <div className="flex items-center gap-3 bg-slate-800/50 backdrop-blur-sm px-4 py-2 rounded-full border border-purple-500/30">
                <Calendar className="text-purple-400 w-5 h-5" />
                <span className="text-slate-300">Joined {joinedDate}</span>
              </div>

              <div className="flex items-center gap-3 bg-slate-800/50 backdrop-blur-sm px-4 py-2 rounded-full border border-purple-500/30">
                <Video className="text-purple-400 w-5 h-5" />
                <span className="text-slate-300">
                  {user.videosCount} {user.videosCount === 1 ? "video" : "videos"}
                </span>
              </div>
            </div>

            {hasSocialLinks && (
              <>
                <div className="w-full md:w-3/4 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent my-8"></div>

                <div className="flex flex-wrap gap-4 justify-center md:justify-start">
                  {user.website && (
                    <Link
                      href={user.website.startsWith("http") ? user.website : `https://${user.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-white bg-slate-800/50 hover:bg-purple-600/20 px-6 py-3 rounded-full transition-all duration-300 border border-purple-500/30 hover:border-purple-400/50 backdrop-blur-sm group"
                    >
                      <FaGlobe className="text-lg group-hover:text-purple-300 transition-colors" />
                      <span>Website</span>
                    </Link>
                  )}

                  {user.twitter && (
                    <Link
                      href={`https://twitter.com/${user.twitter}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-white bg-slate-800/50 hover:bg-[#1DA1F2]/20 px-6 py-3 rounded-full transition-all duration-300 border border-purple-500/30 hover:border-[#1DA1F2]/50 backdrop-blur-sm group"
                    >
                      <FaTwitter className="text-lg group-hover:text-[#1DA1F2] transition-colors" />
                      <span>Twitter</span>
                    </Link>
                  )}

                  {user.instagram && (
                    <Link
                      href={`https://instagram.com/${user.instagram}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-white bg-slate-800/50 hover:bg-[#E1306C]/20 px-6 py-3 rounded-full transition-all duration-300 border border-purple-500/30 hover:border-[#E1306C]/50 backdrop-blur-sm group"
                    >
                      <FaInstagram className="text-lg group-hover:text-[#E1306C] transition-colors" />
                      <span>Instagram</span>
                    </Link>
                  )}

                  {user.twitch && (
                    <Link
                      href={`https://twitch.tv/${user.twitch}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-white bg-slate-800/50 hover:bg-[#6441A4]/20 px-6 py-3 rounded-full transition-all duration-300 border border-purple-500/30 hover:border-[#6441A4]/50 backdrop-blur-sm group"
                    >
                      <FaTwitch className="text-lg group-hover:text-[#6441A4] transition-colors" />
                      <span>Twitch</span>
                    </Link>
                  )}

                  {user.youtube && (
                    <Link
                      href={user.youtube.startsWith("http") ? user.youtube : `https://youtube.com/${user.youtube}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-white bg-slate-800/50 hover:bg-[#FF0000]/20 px-6 py-3 rounded-full transition-all duration-300 border border-purple-500/30 hover:border-[#FF0000]/50 backdrop-blur-sm group"
                    >
                      <FaYoutube className="text-lg group-hover:text-[#FF0000] transition-colors" />
                      <span>YouTube</span>
                    </Link>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}