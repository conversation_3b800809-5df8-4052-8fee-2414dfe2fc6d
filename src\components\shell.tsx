import type React from "react"
import { cn } from "@/lib/utils"

interface ShellProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  dark?: boolean
}

export function Shell({ children, className, dark = false, ...props }: ShellProps) {
  return (
    <div
      className={cn("container mx-auto py-10 space-y-6 w-full", dark && "dark bg-slate-900 text-white", className)}
      {...props}
    >
      {children}
    </div>
  )
}