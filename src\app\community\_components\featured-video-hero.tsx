"use client";

import { useState, useRef, useEffect } from "react";
import { Play } from "lucide-react";
import type { Video } from "@/types/video";
import Image from "next/image";
import { useRouter } from "next/navigation";

const API_URL =
  process.env.NEXT_PUBLIC_VIDEO_API_URL ||
  process.env.VIDEO_API_URL ||
  "https://api.streambliss.cloud";

interface FeaturedVideoHeroProps {
  video: Video;
}

export function FeaturedVideoHero({ video }: FeaturedVideoHeroProps) {
  const [imgError, setImgError] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  const [hovered, setHovered] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const router = useRouter();

  const thumbnail = `${API_URL}/video/thumbnail/${video.userId}/${video.id}`;
  const videoSrc = `${API_URL}/video/stream/${video.userId}/${video.id}`;
  const uploader = video.user?.name || "Unknown";
  const uploaderImage = video.user?.image;
  const displayTitle =
    video.title && video.title.length > 0 ? video.title : "Clip by " + uploader;

  useEffect(() => {
    if (hovered) {
      const timer = setTimeout(() => {
        setVideoVisible(true);
      }, 500); // Delay for better UX
      return () => clearTimeout(timer);
    } else {
      setVideoVisible(false);
    }
  }, [hovered]);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (videoVisible) {
      videoElement.currentTime = 0;

      // Try to play with sound first
      videoElement.muted = false;
      videoElement.volume = 0.5; // Set reasonable volume

      videoElement.play().catch((err) => {
        console.log("Video play with sound failed, trying muted:", err.message);
        // Fallback to muted if autoplay with sound is blocked
        videoElement.muted = true;
        videoElement.play().catch((mutedErr) => {
          console.log("Muted video play also failed:", mutedErr.message);
        });
      });
    } else {
      if (!videoElement.paused) {
        videoElement.pause();
      }
    }
  }, [videoVisible]);

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M Views`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K Views`;
    }
    return `${views} Views`;
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "1 day ago";
    return `${diffDays} days ago`;
  };

  const handleWatchNow = () => {
    router.push(`/v/${video.shortLink}`);
  };

  const handleViewProfile = () => {
    router.push(`/community/u/${uploader}`);
  };

  return (
    <div
      className="relative w-full h-[600px] lg:h-[906px] overflow-hidden bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url('/assets/images/webp/comm-hero.webp')`,
      }}
    >
      {/* Content Container */}
      <div className="absolute inset-0 flex justify-center items-center px-4">
        <div className="w-full max-w-[1920px] h-full relative">
          <div className="absolute lg:left-[300px] lg:top-[224px] lg:w-[1320px] lg:h-[535px] flex flex-col lg:flex-row items-center lg:gap-[143px] gap-8 inset-x-4 top-20 lg:inset-auto">
            {/* Left Content */}
            <div className="w-full lg:w-[450px] flex flex-col items-start gap-6 lg:gap-10">
              {/* User Info and Title Section */}
              <div className="flex flex-col items-start gap-4 lg:gap-[21px] w-full">
                {/* User Info */}
                <div className="flex items-center gap-4 lg:gap-[22px]">
                  {/* User Avatar */}
                  <div className="relative w-[50px] h-[50px] lg:w-[60px] lg:h-[60px]">
                    {uploaderImage && !avatarError ? (
                      <Image
                        src={uploaderImage}
                        alt={uploader}
                        fill
                        className="rounded-full object-cover border border-white"
                        onError={() => setAvatarError(true)}
                        unoptimized
                      />
                    ) : (
                      <div className="w-full h-full rounded-full bg-gray-400 border border-white flex items-center justify-center">
                        <span className="text-black text-lg lg:text-xl font-semibold">
                          {uploader.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Username */}
                  <div className="text-white text-xl lg:text-[28px] font-normal leading-[160%] font-sans">
                    {uploader}
                  </div>
                </div>

                {/* Title and Stats */}
                <div className="flex flex-col items-start gap-4 lg:gap-[21px] w-full">
                  {/* Title */}
                  <h1 className="text-white text-3xl lg:text-[58px] font-bold leading-tight lg:leading-[160%] w-full font-montserrat">
                    {displayTitle}
                  </h1>

                  {/* Stats Row */}
                  <div className="flex flex-col md:flex-row items-start md:items-center gap-4 lg:gap-[33px] w-full">
                    {/* Views */}
                    <div className="flex items-center gap-3 lg:gap-5">
                      <div className="flex items-center gap-2 lg:gap-3">
                        {/* Views Icon */}
                        <svg
                          width="28"
                          height="28"
                          className="lg:w-[35px] lg:h-[35px]"
                          viewBox="0 0 35 35"
                          fill="none"
                        >
                          <path
                            d="M16.8436 3.80327C16.6469 4 16.625 4.16394 16.625 5.33336C16.625 6.86345 16.7343 7.08203 17.4993 7.08203C18.2644 7.08203 18.3737 6.86345 18.3737 5.33336C18.3737 3.80327 18.2644 3.58469 17.4993 3.58469C17.2042 3.58469 16.9857 3.66119 16.8436 3.80327Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M11.9814 4.8416C11.6098 5.20226 11.6316 5.57386 12.1016 6.66678C12.5934 7.82527 12.7136 7.95642 13.2929 7.95642C13.8502 7.95642 14.2218 7.61762 14.2218 7.10394C14.2218 6.69956 13.424 4.90717 13.1617 4.69952C12.8557 4.47001 12.2764 4.53558 11.9814 4.8416Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M21.8394 4.7215C21.5881 4.91823 20.7793 6.72154 20.7793 7.09314C20.7793 7.25707 20.8777 7.50845 21.0088 7.67238C21.1837 7.9019 21.3258 7.95654 21.6973 7.95654C22.2766 7.95654 22.3968 7.83632 22.8996 6.67783C23.3804 5.58491 23.4023 5.11495 23.0089 4.798C22.681 4.5357 22.1345 4.50292 21.8394 4.7215Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M14.713 9.93458C12.2867 10.3608 9.41235 11.563 6.95328 13.1806C5.52156 14.1095 3.70731 15.5303 3.01877 16.2517L2.59253 16.6998L3.01877 17.1479C3.60895 17.7599 5.40133 19.1916 6.55983 19.9676C9.32492 21.8256 12.1228 23.0168 14.8551 23.4868C16.5054 23.76 17.2267 23.76 16.0792 23.4759C15.03 23.2136 14.3742 22.9403 13.5764 22.3939C12.4179 21.607 11.4233 20.2845 10.9315 18.8856C10.5927 17.9129 10.5162 16.0659 10.7785 15.0604C11.4343 12.5029 13.4015 10.5794 16.0354 9.93458C16.8333 9.73786 16.8661 9.716 16.407 9.72693C16.1338 9.72693 15.3797 9.81436 14.713 9.93458Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M18.9208 9.92364C19.97 10.1859 20.6258 10.4592 21.4236 11.0056C22.5821 11.7925 23.5767 13.115 24.0685 14.5139C24.4073 15.4866 24.4838 17.3336 24.2215 18.3391C23.5657 20.8966 21.5985 22.8201 18.9645 23.4649C17.7733 23.76 18.4727 23.7709 20.1449 23.4758C22.7023 23.0496 25.5221 21.8802 28.0467 20.2189C29.4784 19.29 31.2927 17.8692 31.9812 17.1478L32.4075 16.6997L31.9812 16.2516C31.2927 15.5303 29.4784 14.1095 28.0467 13.1805C25.5221 11.5193 22.7133 10.3608 20.1886 9.92364C18.5055 9.63948 17.7623 9.63948 18.9208 9.92364Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M16.102 13.432C13.8724 14.2954 13.2604 17.4758 14.9872 19.2135C15.9599 20.1753 17.5119 20.4813 18.8671 19.9676C20.0474 19.5305 20.9983 18.1971 20.9983 16.984V16.5249L20.3425 16.5687C18.7031 16.6889 17.5119 15.4976 17.6321 13.8582L17.6758 13.2025L17.184 13.2134C16.9107 13.2134 16.4189 13.3118 16.102 13.432Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M12.6581 25.6289C12.363 25.9677 11.7073 27.5961 11.7073 27.9896C11.7073 28.6672 12.6144 29.0934 13.1609 28.6781C13.4122 28.4814 14.221 26.6781 14.221 26.3065C14.221 26.1425 14.1226 25.8912 13.9915 25.7272C13.8166 25.4977 13.6745 25.4431 13.3029 25.4431C12.9969 25.4431 12.7783 25.5086 12.6581 25.6289Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M21.0197 25.6943C20.8886 25.8364 20.7793 26.0877 20.7793 26.2517C20.7793 26.6779 21.6318 28.5905 21.8831 28.7217C22.4952 29.0496 23.293 28.6342 23.293 27.9894C23.293 27.596 22.6373 25.9675 22.3422 25.6287C22.0799 25.3446 21.3148 25.3773 21.0197 25.6943Z"
                            fill="#B851E0"
                          />
                          <path
                            d="M16.8436 26.5362C16.6469 26.7329 16.625 26.8968 16.625 28.0663C16.625 29.5964 16.7343 29.8149 17.4993 29.8149C18.2644 29.8149 18.3737 29.5964 18.3737 28.0663C18.3737 26.5362 18.2644 26.3176 17.4993 26.3176C17.2042 26.3176 16.9857 26.3941 16.8436 26.5362Z"
                            fill="#B851E0"
                          />
                        </svg>
                        <span className="text-white text-lg lg:text-[28px] font-normal leading-[160%] font-sans">
                          {formatViews(video.views)}
                        </span>
                      </div>
                    </div>

                    {/* Time */}
                    <div className="flex items-center gap-2 lg:gap-3">
                      <svg
                        width="28"
                        height="28"
                        className="lg:w-[34px] lg:h-[34px]"
                        viewBox="0 0 34 35"
                        fill="none"
                      >
                        <path
                          d="M17 30C10.3724 30 5 24.6276 5 18C5 11.3724 10.3724 6 17 6C23.6276 6 29 11.3724 29 18C29 24.6276 23.6276 30 17 30ZM18.2 18V12H15.8V20.4H23V18H18.2Z"
                          fill="#B851E0"
                        />
                      </svg>
                      <span className="text-white text-lg lg:text-[28px] font-normal leading-[160%] font-sans">
                        {getTimeAgo(new Date(video.createdAt))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4 lg:gap-6 w-full">
                {/* Watch Now Button */}
                <button
                  onClick={handleWatchNow}
                  className="flex justify-center items-center px-6 py-3 lg:py-[14px] rounded-[65px] text-white text-base lg:text-lg font-bold leading-[160%] transition-all duration-300 hover:opacity-90 font-montserrat min-w-[180px] lg:min-w-[204px]"
                  style={{
                    background:
                      "linear-gradient(180deg, #B851E0 0%, #EB489B 100%)",
                  }}
                >
                  Watch Now
                </button>

                {/* View Profile Button */}
                <button
                  onClick={handleViewProfile}
                  className="flex justify-center items-center px-6 py-3 lg:py-[14px] rounded-[65px] border border-white text-white text-base lg:text-lg font-bold leading-[160%] opacity-70 hover:opacity-100 transition-all duration-300 font-montserrat min-w-[180px] lg:min-w-[204px]"
                >
                  View Profile
                </button>
              </div>
            </div>

            {/* Right Video Player */}
            <div
              className="relative cursor-pointer w-full max-w-[800px] lg:max-w-none lg:w-[800px] lg:h-[600px] group"
              onClick={handleWatchNow}
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              style={{
                width: "100%",
                maxWidth: "800px",
                height: "400px",
                aspectRatio: "800/600",
              }}
            >
              {/* Video Player Container */}
              <div
                className="w-full h-full rounded-2xl overflow-hidden relative transform transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl group-hover:shadow-purple-500/20"
                style={{
                  border: "6px solid rgba(184, 81, 224, 0.4)",
                  background: "#1a1a1a",
                }}
              >
                {/* Video Thumbnail */}
                <div className="relative w-full h-full">
                  {!imgError ? (
                    <Image
                      src={thumbnail}
                      alt={displayTitle}
                      fill
                      className={`object-cover transition-all duration-300 ${videoVisible ? "opacity-0" : "opacity-100"} group-hover:scale-110`}
                      onError={() => setImgError(true)}
                      unoptimized
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                      <span className="text-white text-lg">Video Preview</span>
                    </div>
                  )}

                  {/* Video Player - Shows on hover */}
                  {videoVisible && (
                    <video
                      ref={videoRef}
                      src={videoSrc}
                      className="absolute inset-0 w-full h-full object-cover transition-opacity duration-300"
                      muted
                      loop
                      playsInline
                      preload="metadata"
                    />
                  )}

                  {/* Video Player UI Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/20 transition-all duration-300 group-hover:from-black/70 group-hover:via-black/10">
                    {/* Play Button - Hide when video is playing */}
                    <div
                      className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${videoVisible ? "opacity-0" : "opacity-100"}`}
                    >
                      <div className="w-20 h-20 lg:w-24 lg:h-24 rounded-full bg-gradient-to-b from-[#b851e0] to-[#eb489b] flex items-center justify-center transition-all duration-300 shadow-lg group-hover:scale-125 group-hover:shadow-purple-500/50">
                        <Play
                          className="w-8 h-8 lg:w-10 lg:h-10 text-white ml-1"
                          fill="currentColor"
                        />
                      </div>
                    </div>

                    {/* Video Info Overlay (bottom left) */}
                    <div className="absolute bottom-6 left-6 flex items-center gap-4 transition-all duration-300 group-hover:translate-y-1">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-b from-[#b851e0] to-[#eb489b] flex items-center justify-center">
                        <Play
                          className="w-4 h-4 text-white ml-0.5"
                          fill="currentColor"
                        />
                      </div>
                      <div className="text-white">
                        <div className="text-base font-semibold">
                          {displayTitle}
                        </div>
                        <div className="text-sm opacity-70">
                          {formatViews(video.views)} •{" "}
                          {getTimeAgo(new Date(video.createdAt))}
                        </div>
                      </div>
                    </div>

                    {/* Hover Play Indicator */}
                    <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <div className="px-3 py-1 bg-black/70 rounded-full text-white text-sm font-medium">
                        {videoVisible ? "Playing preview" : "Hover to preview"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}